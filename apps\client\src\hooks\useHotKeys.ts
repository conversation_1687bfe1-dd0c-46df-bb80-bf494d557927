import { useCallback, useEffect, useRef } from "react";

const parseHotkey = (
  hotkeyStr: string | undefined,
): Omit<HotkeyType, "key"> => {
  if (!hotkeyStr)
    return {
      ctrlKey: false,
      altKey: false,
      shiftKey: false,
      metaKey: false,
      expectedKey: "",
    };
  const keys = hotkeyStr
    .toLowerCase()
    .split("+")
    .map((k) => k.trim());
  return {
    ctrlKey: keys.includes("ctrl") || keys.includes("control"),
    altKey: keys.includes("alt"),
    shiftKey: keys.includes("shift"),
    metaKey: keys.includes("meta"),
    expectedKey: keys.length > 1 ? keys[keys.length - 1] : "",
  };
};

const getTargetElement = (
  target: Element | string | null,
): Element | Document => {
  if (!target) return document;
  if (typeof target === "string") {
    return document.querySelector(target) || document;
  }

  return target;
};

interface ModifiersState {
  ctrlKey: boolean;
  altKey: boolean;
  shiftKey: boolean;
  metaKey: boolean;
}

export interface HotkeyType extends ModifiersState {
  key: string;
  expectedKey: string;
}

type KeyboardEventLike = {
  ctrlKey: boolean;
  altKey: boolean;
  shiftKey: boolean;
  key?: string;
};

interface useHotkeysProps {
  hotkeyStr?: string;
  enabled?: boolean;
  preventDefault?: boolean;
  target?: HTMLElement | string | null;
}

export const useHotkeys = <T extends HTMLElement>(
  callback: (event: HotkeyType) => void,
  {
    hotkeyStr,
    enabled = true,
    preventDefault = true,
    target = null,
  } = {} as useHotkeysProps,
) => {
  const elementRef = useRef<T | null>(null);

  const modifiersRef = useRef<ModifiersState>({
    ctrlKey: false,
    altKey: false,
    shiftKey: false,
    metaKey: false,
  });

  const callbackRef = useRef(callback);
  callbackRef.current = callback;

  const hotkey = useRef(parseHotkey(hotkeyStr));

  const getCurrentTarget = useCallback(() => {
    const optionsTarget = getTargetElement(target);
    if (optionsTarget && optionsTarget !== document) {
      return optionsTarget;
    }
    const refTarget = getTargetElement(elementRef.current);
    if (refTarget && refTarget !== document) {
      return refTarget;
    }
    return document;
  }, [target]);

  const checkModifiers = useCallback((event: KeyboardEventLike): boolean => {
    const { ctrlKey, altKey, shiftKey } = hotkey.current;
    return (
      (!ctrlKey || event.ctrlKey) &&
      (!altKey || event.altKey) &&
      (!shiftKey || event.shiftKey)
    );
  }, []);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      const currentTarget = getCurrentTarget();
      if (
        currentTarget !== document &&
        !currentTarget.contains(event.target as Node)
      ) {
        return;
      }

      if (preventDefault) {
        event.preventDefault();
      }

      modifiersRef.current = {
        ctrlKey: event.ctrlKey,
        altKey: event.altKey,
        shiftKey: event.shiftKey,
        metaKey: event.metaKey,
      };

      const key = event.key.toLowerCase();
      const expectedKey = hotkey.current.expectedKey;

      const payload = { ...modifiersRef.current, key, expectedKey };

      if (hotkeyStr) {
        checkModifiers(event) &&
          key === expectedKey &&
          callbackRef.current(payload);
      } else {
        callbackRef.current(payload);
      }
    },
    [preventDefault, getCurrentTarget, hotkeyStr, checkModifiers],
  );

  const handleKeyUp = useCallback(
    (event: KeyboardEvent) => {
      const currentTarget = getCurrentTarget();
      if (
        currentTarget !== document &&
        !currentTarget.contains(event.target as Node)
      ) {
        return;
      }

      const key = event.key.toLowerCase();
      const updates: Partial<ModifiersState> = { ...modifiersRef.current };
      if (["control", "ctrl"].includes(key)) updates.ctrlKey = false;
      if (key === "alt") updates.altKey = false;
      if (key === "shift") updates.shiftKey = false;
      modifiersRef.current = { ...modifiersRef.current, ...updates };
    },
    [getCurrentTarget],
  );

  useEffect(() => {
    if (!enabled) return;

    const currentTarget = getCurrentTarget();
    if (!currentTarget) return;

    // Type-safe event listener addition
    const keyDownHandler: EventListener = (event: Event) => {
      handleKeyDown(event as KeyboardEvent);
    };

    const keyUpHandler: EventListener = (event: Event) => {
      handleKeyUp(event as KeyboardEvent);
    };

    // Use type assertion for addEventListener
    currentTarget.addEventListener("keydown", keyDownHandler);
    currentTarget.addEventListener("keyup", keyUpHandler);

    return () => {
      currentTarget.removeEventListener("keydown", keyDownHandler);
      currentTarget.removeEventListener("keyup", keyUpHandler);
    };
  }, [enabled, handleKeyDown, handleKeyUp, getCurrentTarget]);

  return {
    ref: elementRef, // DOM 引用

    onKeyDown: handleKeyDown,
    onKeyUp: handleKeyUp,
  };
};
