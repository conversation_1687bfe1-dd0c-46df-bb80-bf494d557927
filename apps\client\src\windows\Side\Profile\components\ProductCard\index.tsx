import useSession from "@/business/hooks/useSession";
import useRemote from "@/hooks/useRemote";
import {
  type IProductItem,
  deleteShopAttr,
  getProductList,
  searchByName,
} from "@/network/api/product";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import AttrBottomSheet from "./components/AttrBottomSheet";
import FocusProduct from "./components/FocusProduct";
import SKUBottomSheet from "./components/SKUBottomSheet";
import Search from "./components/Search";
import SearchProduct from "./components/SearchProduct";
import { ProductContext } from "./context";

const ProductCard = () => {
  const { buyer, serviceAccount, thirdShopId } = useSession();

  const [searchProduct, setSearchProduct] = useState<
    IProductItem | undefined
  >();
  const [openSkU, setOpenSKU] = useState(false);
  const [openAttr, setOpenAttr] = useState(false);
  const [attr, setAttr] = useState<
    NonNullable<IProductItem["userAttrs"]>[number] & { productId: string }
  >();
  const [product, setProduct] = useState<IProductItem | undefined>();

  const { data, mutate } = useRemote(
    buyer?.buyerNick ? [getProductList.name, buyer?.buyerNick] : null,
    ([_, buyerNick]) => getProductList({ buyerNick }),
  );

  const productList = useMemo<IProductItem[]>(() => {
    return data?.items || [];
  }, [data]);

  /** 选择商品 */
  const onSelect = (data: IProductItem) => {
    setSearchProduct(data);
  };
  /** 取消选择商品 */
  const onClear = () => {
    setSearchProduct(undefined);
  };

  const handleOpenAttr = (
    attr: NonNullable<IProductItem["userAttrs"]>[number] & {
      productId: string;
    },
  ) => {
    setOpenAttr(true);
    setAttr(attr);
  };

  const handleOpenSkU = (product: IProductItem) => {
    setProduct(product);
    setOpenSKU(true);
  };

  const handleCloseSKU = () => {
    setProduct(undefined);
    setOpenSKU(false);
  };

  const handleCloseAttr = () => {
    setOpenAttr(false);
  };

  // 获取商品详情
  const getProductDetailById = async (value: string) => {
    const params = {
      searchById: value,
      account: serviceAccount?.account as string, //第几页
      pageIndex: "1",
      //一页展示多少条
      pageSize: 10,
    };
    const res = await searchByName(params).promise.catch(
      (error: Error) => error,
    );
    if (res instanceof Error) return;
    if (res.data.success) {
      if (res.data.data?.results?.[0]?.productId === searchProduct?.productId) {
        searchProduct &&
          setSearchProduct({
            ...searchProduct,
            userAttrs: res.data.data.results[0].userAttrs || [],
          });
      }
    }
  };

  const handleUpateAttr = () => {
    if (searchProduct) {
      getProductDetailById(searchProduct.productId);
    } else {
      mutate();
    }
    setOpenAttr(false);
    setAttr(undefined);
  };

  const handleDelectAttr = async (proAttrId: string) => {
    console.log(proAttrId, "当前删除的属性");
    const res = await deleteShopAttr({ proAttrId }).promise.catch(
      (error) => error,
    );
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      handleUpateAttr();
    }
  };

  return (
    <ProductContext.Provider
      value={{
        onEditArrt: handleOpenAttr,
        openSKU: handleOpenSkU,
        closeSKU: handleCloseSKU,
        delectAttr: handleDelectAttr,
      }}
    >
      <div className="flex flex-col gap-2 bg-white rounded-4">
        <Search
          sellerAccount={serviceAccount?.account as string}
          onSelect={onSelect}
        />
        <SearchProduct
          className={`${searchProduct ? "block" : "hidden"} pt-0`}
          buyerNick={buyer?.buyerNick as string}
          data={searchProduct as unknown as IProductItem}
          onClear={onClear}
        />
        <FocusProduct
          buyerNick={buyer?.buyerNick as string}
          className={`${searchProduct ? "hidden" : "block"} pt-0`}
          data={productList}
        />
      </div>
      <SKUBottomSheet
        open={openSkU}
        onClose={handleCloseSKU}
        data={product}
        buyerNick={buyer?.buyerNick as string}
      />
      <AttrBottomSheet
        open={openAttr}
        onClose={handleCloseAttr}
        data={attr}
        onSuccess={handleUpateAttr}
        tyShopId={thirdShopId as string}
        sellerAccount={serviceAccount?.account as string}
      />
    </ProductContext.Provider>
  );
};
export default ProductCard;
