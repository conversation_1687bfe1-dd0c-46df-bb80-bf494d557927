import type { FormatedAgentMessageType } from "@/business/components/AgentProvider/store";
import type { MessageToPreviewResource } from "@/constant/protocol";
import type { BatchSendMessageData } from "@/network/api/base";
import React from "react";

export type AgentState = {
  settingOpen: boolean;
  topic: FormatedAgentMessageType | undefined;
};

const AgentContext = React.createContext<{
  sendMessages: (payload: BatchSendMessageData) => Promise<boolean>;
  preview: (payload: MessageToPreviewResource["data"]) => void;
  insert: (content: string) => void;
  state: AgentState;
  setState: (value: Partial<AgentState>) => void;
  shortcutIndexMap: Map<string, [number, number]>;
  updateShortcutIndexMap: (data: FormatedAgentMessageType[]) => void;
  updateMsg: (data: FormatedAgentMessageType) => void;
}>({
  sendMessages: (value) => {
    console.log(value);
    return Promise.resolve(false);
  },
  preview: console.log,
  insert: console.log,
  setState: console.log,
  state: {
    settingOpen: false,
    topic: undefined,
  },
  shortcutIndexMap: new Map<string, [number, number]>(),
  updateShortcutIndexMap: (data: FormatedAgentMessageType[]) =>
    console.log(data),
  updateMsg: (data: FormatedAgentMessageType) => console.log(data),
});

export default AgentContext;
