import useSession from "@/business/hooks/useSession";
import useRemote from "@/hooks/useRemote";
import { getGroupLabelsList } from "@/network/api/base";
import { getBuyerSystemLabel, getUserBindLabel } from "@/network/api/buyer";
import { ScrollArea } from "@workspace/ui";
import { Divider } from "antd";
import { useCallback, useState } from "react";
import BuyerInfo from "./components/BuyerInfo";
import ProductCard from "./components/ProductCard";
import TagsCard from "./components/TagsCard";

interface CustomLabelItem {
  groupId: number;
  groupName: string;
  labels: {
    groupId: number;
    value: number;
    labelName: string;
    labelId: string;
  }[];
}

export const Component: React.FC = () => {
  const { buyer } = useSession();
  //分组标签
  const [labelList, setLabelList] = useState<CustomLabelItem[]>([]);

  //当前用户绑定的标签id
  const [userBindLabelId, setUserBindLabelId] = useState<string[]>([]);

  const { data: buyerInfo, mutate: buyerInfoMutate } = useRemote(
    buyer?.buyerNick ? [getBuyerSystemLabel.name, buyer.buyerNick] : null,
    ([_, buyerAccount]) => getBuyerSystemLabel({ buyerNick: buyerAccount }),
  );

  //获取分组标签
  const getGroupLabeInfo = useCallback(async () => {
    try {
      const labelListRes = (await getGroupLabelsList().promise).data.data;
      const userBindLabelInfoRes = (
        await getUserBindLabel({ buyerNick: buyer?.buyerNick as string })
          .promise
      ).data.data;
      const bindLabelIds = userBindLabelInfoRes.map((item) => item.labelId);

      //设置用户绑定标签
      setUserBindLabelId(bindLabelIds);
      const filterList = labelListRes.map((item) => {
        const labels = item.labelInfos.map((label) => {
          const isSelect = bindLabelIds.includes(label.labelId);
          return { ...label, value: isSelect ? 1 : 0 };
        });
        return { ...item, labels };
      });
      setLabelList(filterList);
    } catch (error) {}
  }, [buyer?.buyerNick]);

  const handleRefresh = () => {
    buyerInfoMutate();
    getGroupLabeInfo();
  };

  return (
    <ScrollArea className="flex-1 h-0">
      <div className="p-2 space-y-2 bg-gray-3">
        <div className="p-2 bg-white rounded-4">
          <BuyerInfo
            data={buyerInfo}
            mutate={buyerInfoMutate}
            refresh={handleRefresh}
          />
          <Divider className="my-3" />
          <TagsCard
            labelList={labelList}
            setLabelList={setLabelList}
            userBindLabelId={userBindLabelId}
            mutate={getGroupLabeInfo}
          />
        </div>
        <ProductCard />
      </div>
    </ScrollArea>
  );
};
Component.displayName = "Profile";
