import type { DownloadProgressMessage } from "@/constant/protocol/index";
import { throttle } from "lodash-es";
import { createStore } from "zustand";

export type DownloadFile =
  DownloadProgressMessage["data"]["fileList"][number] & {
    progress: string;
  };

export interface DownloadState {
  files: Record<string, DownloadFile>;
  removeFile: (id: string) => void;
  setFiles: (files: DownloadState["files"]) => void;
  unsubscribe: (() => void) | undefined;
  setSubscribe: (unsubscribe: (() => void) | undefined) => void;
}

export const downloadStore = createStore<DownloadState>((set, get) => ({
  files: {},
  removeFile: (id) =>
    set({
      files: Object.fromEntries(
        Object.entries(get().files).filter(([key]) => key !== id),
      ),
    }),
  setFiles: throttle((files: DownloadState["files"]) => {
    set((state) => ({ files: { ...state.files, ...files } }));
  }, 500),
  unsubscribe: undefined,
  setSubscribe: (unsubscribe: (() => void) | undefined) =>
    set({
      unsubscribe: () => {
        unsubscribe?.();
        set({ unsubscribe: undefined });
      },
    }),
}));
