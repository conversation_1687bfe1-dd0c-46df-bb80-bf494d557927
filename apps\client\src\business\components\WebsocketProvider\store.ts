import type WebSocketManager from "@/class/Ws";
import { createStore } from "zustand";
interface WebSocketStore {
  socket: WebSocketManager | null;
  setWs: (socket: WebSocketManager | null) => void;
  readyState: number;
  setReadyState: (state: number) => void;
}

export const websocketStore = createStore<WebSocketStore>((set) => ({
  socket: null,
  setWs: (socket) => set({ socket }),
  readyState: 0,
  setReadyState: (state: number) => set({ readyState: state }),
}));
