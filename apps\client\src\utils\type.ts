export enum DataType {
  String = "String",
  Number = "Number",
  Boolean = "Boolean",
  Symbol = "Symbol",
  Undefined = "Undefined",
  Null = "Null",
  Function = "Function",
  Date = "Date",
  Array = "Array",
  RegExp = "RegExp",
  Error = "Error",
  HTMLDocument = "HTMLDocument",
  Global = "Global",
}

/** 获取类型
 * @param {any} input
 * @author: ocass
 */
export const getDataType = (data: unknown): DataType => {
  const typeString: string = Object.prototype.toString.call(data);
  // 使用正则表达式提取类型字符串中的具体类型名称
  const typeName: string = typeString.match(/\[object (\w+)\]/)![1];
  return typeName as DataType;
};
