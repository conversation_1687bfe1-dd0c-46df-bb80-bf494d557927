import { useState } from "react";
import ArrowDownOutlined from "~icons/ant-design/arrow-down-outlined";
import ArrowUpOutlined from "~icons/ant-design/arrow-up-outlined";

const sortedMap = {
  Ascending: "Ascending",
  Descending: "Descending",
} as const;

interface SorterProps {
  onChange: (value: keyof typeof sortedMap) => void;
  defaultValue?: keyof typeof sortedMap;
}

const Sorter = ({ onChange, defaultValue }: SorterProps) => {
  const [state, setState] = useState<keyof typeof sortedMap>(
    defaultValue || sortedMap.Ascending,
  );

  return (
    <button
      className="flex items-center gap-2 "
      type="button"
      onClick={() => {
        const newState =
          state === sortedMap.Ascending
            ? sortedMap.Descending
            : sortedMap.Ascending;
        setState(newState);
        onChange?.(newState);
      }}
    >
      <span>等待时间</span>
      {state === sortedMap.Ascending ? (
        <ArrowUpOutlined className="text-blue-7" />
      ) : (
        <ArrowDownOutlined className="text-blue-7" />
      )}
    </button>
  );
};

export default Sorter;
