import clsx from "clsx";
import type { ReactNode } from "react";

interface WrapperProps {
  children?: ReactNode;
  title?: ReactNode;
  action?: ReactNode;
  className?: string;
}

const Seassion = ({ children, title, action, className }: WrapperProps) => {
  return (
    <div className={clsx("p-2 bg-white space-y-3 rounded-4", className)}>
      <div className="flex items-center justify-between">
        <span className="before:content-[''] before:bg-blue-7 before:w-[3px] before:h-[14px] before:inline-block before:mr-2 before:rounded-full text-14 font-medium text-black-5 m-0 flex items-center mr-auto">
          {title}
        </span>
        <span>{action}</span>
      </div>
      {children}
    </div>
  );
};

export default Seassion;
