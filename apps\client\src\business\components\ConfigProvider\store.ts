import type { AppTypeEnum } from "@/constant/client";
import { Keys } from "@/constant/localstorage";
import { getSupportedClientInfo } from "@/network/api/assets";
import { Platform } from "@workspace/business";
import { createStore } from "zustand";

export type SupportedClient = {
  isDefaultVs: boolean;
  is64Bit: boolean;
  platform: Platform;
  rarUrl: string;
  version: string;
  updateTime: string;
  fileUrl: string;
  _id: string;
};

export interface PluginInfoItem {
  is64Bit: boolean;
  pluginType: AppTypeEnum;
  pluginVersion: string;
  pluginPath: string;
}
export interface Config {
  /** 杂项 */
  others: {
    /** 版本号 */
    version: string | undefined;
    /** 开机自启动 */
    isAutoBootStart: boolean;
    isDyRobotExist: boolean;
    isKsRobotExist: boolean;
  };
  clientsInfo: {
    [key in Platform]: {
      platformType: Platform;
      isExist: boolean;
      version: string;
      is64Bit: boolean;
      _id: string;
    };
  };
  localPlatformClientsInfo: {
    [key in Platform]: {
      platformPath: string;
      platformVersion: string;
      is64Bit: boolean;
      _id: string;
    }[];
  };
  supportedClientsInfo: {
    [key in Platform]: SupportedClient[];
  };

  installDirectory: string;

  /** 基础设置 */
  base: {
    offlineNotify: boolean;
    updateNotify: boolean;
    autoLogin: boolean;
    reminderShortcut: string;
    cardMsgShortcut: string;
    singleMsgShortcut: string;
  };
  client: {
    fielPath: string;
    platformClient: {
      [key in Platform]: {
        platformVersion: string;
        is64Bit: boolean;
        _id: string;
      };
    };
  };
  /** 智能体设置 */
  agentConfig: {
    oldSidebarSwitch: boolean;
  };
  /** 智能体快捷键设置 */
  agent: Record<number, string>;
  /** 聚合接待设置 */
  reception: {
    enable: boolean;
    isAlone: boolean;
    shortcutForOpenconversation: string;
    shortcutForChangeMode: string;
  };
  /** 应用设置  */
  appConfig: {
    pluginInfo?: PluginInfoItem[];
    fileSavePath: string;
  };
  /** 插件信息  */
  pluginInfo: Array<{
    isExist: boolean;
    pluginType: AppTypeEnum;
    version: string;
  }>;
}

export interface ConfigState extends Config {
  setConfig: (config: Partial<Config>) => void;
  unsubscribe: (() => void) | undefined;
  setSubscribe: (unsubscribe: (() => void) | undefined) => void;
  getAllPlatformSupportedClientsInfo: (
    versionNo?: string,
  ) => Promise<Config["supportedClientsInfo"] | undefined>;
}

export const configStore = createStore<ConfigState>((set, get) => ({
  others: {
    isAutoBootStart: true,
    isDyRobotExist: false,
    isKsRobotExist: false,
    version: undefined,
    enableAutoLogin: false,
  },
  clientsInfo: {} as Config["clientsInfo"],
  supportedClientsInfo: {} as Config["supportedClientsInfo"],
  localPlatformClientsInfo: {} as Config["localPlatformClientsInfo"],
  installDirectory: "",
  base: {
    offlineNotify: false,
    updateNotify: false,
    autoLogin: false,
    reminderShortcut: "",
    cardMsgShortcut: "",
    singleMsgShortcut: "",
  },
  client: {} as Config["client"],
  agentConfig: {
    oldSidebarSwitch: false,
  },
  agent: {},
  reception: {
    enable: false,
    isAlone: false,
    shortcutForOpenconversation: "",
    shortcutForChangeMode: "",
  },
  appConfig: {
    fileSavePath: "",
    pluginInfo: [],
  },
  /** 插件信息  */
  pluginInfo: [],
  setConfig: (config: Partial<Config>) => {
    const oldState = get();
    const newState = { ...oldState, ...config };
    set(newState);
    localStorage.setItem(
      Keys.ConfigBaseShortcut,
      JSON.stringify({
        reminderShortcut: newState.base.reminderShortcut,
      }),
    );
  },
  unsubscribe: undefined,
  setSubscribe: (unsubscribe: (() => void) | undefined) =>
    set({
      unsubscribe: () => {
        unsubscribe?.();

        set({ unsubscribe: undefined });
      },
    }),
  getAllPlatformSupportedClientsInfo: async (versionNo?: string) => {
    const res = await getSupportedClientInfo({
      versionNo: (versionNo || get().others.version) as string,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error || !res.data.success) return;

    const data = res.data.data
      .map((item) => ({
        isDefaultVs: item.isDefaultVs,
        is64Bit: item.bit === "x64",
        platform: item.platformCode,
        rarUrl: item.platformRar,
        version: item.version,
        updateTime: item.updDate,
        // 千牛平台exe文件，使用platform字段，其他平台rar文件，使用platformRar字段
        fileUrl: [Platform.TAOBAO, Platform.ALI1688].includes(
          Number(item.platformCode),
        )
          ? item.platform
          : item.platformRar,
        _id: `${item.bit}_${item.version}`,
      }))
      .reduce(
        (acc, cur) => {
          const list = (acc[cur.platform] ||
            []) as unknown as SupportedClient[];
          list.push(cur);
          return Object.assign(acc, {
            [cur.platform]: list,
          });
        },
        {} as Config["supportedClientsInfo"],
      );
    set({
      supportedClientsInfo: data,
    });
    return data;
  },
}));
