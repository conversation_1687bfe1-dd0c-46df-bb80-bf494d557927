<svg xmlns="http://www.w3.org/2000/svg" width="240" height="150" viewBox="0 0 240 150" fill="none">
  <path opacity="0.8" fill-rule="evenodd" clip-rule="evenodd" d="M48.188 79.101C50.6559 71.1759 52.9527 66.2416 55.0744 64.2963C60.9503 58.9121 69.0744 61.8424 70.8016 62.2621C76.8692 63.7405 74.8963 54.0118 80.1021 52.0344C83.5714 50.7167 86.4246 52.3274 88.6636 56.8665C90.6458 52.6318 93.6665 50.6921 97.7258 51.0381C103.816 51.5617 105.947 72.4539 114.315 67.8807C122.685 63.3056 132.945 62.2602 137.329 69.0585C138.276 70.5293 138.639 68.2475 145.082 60.4566C151.526 52.6639 157.951 49.2307 171.132 53.768C177.125 55.8286 182.053 61.4151 185.923 70.5256C185.923 83.5286 195.34 91.2268 214.172 93.6145C242.423 97.197 220.495 128.048 185.923 137.083C151.349 146.12 71.7508 150.998 28.556 128.168C-0.240557 112.951 6.30344 96.594 48.1861 79.101H48.188Z" fill="url(#paint0_linear_61_16549)"/>
  <path d="M119.026 143.008C147.794 143.008 171.115 137.525 171.115 130.761C171.115 123.998 147.794 118.514 119.026 118.514C90.2576 118.514 66.9363 123.998 66.9363 130.761C66.9363 137.525 90.2576 143.008 119.026 143.008Z" fill="url(#paint1_linear_61_16549)"/>
  <path opacity="0.675" fill-rule="evenodd" clip-rule="evenodd" d="M206.967 146.042C167.802 156.02 34.1174 144.607 19.8885 139.395C12.963 136.856 6.72051 132.083 1.16295 125.073C0.540936 124.289 0.14996 123.342 0.0352041 122.342C-0.0795523 121.342 0.0865944 120.33 0.514445 119.422C0.942295 118.513 1.61438 117.747 2.45305 117.21C3.29172 116.674 4.26273 116.389 5.25388 116.389H238.861C243.71 129.506 233.078 139.39 206.967 146.042Z" fill="url(#paint2_linear_61_16549)"/>
  <g filter="url(#filter0_b_61_16549)">
    <path d="M162.057 91.856C157.21 93.484 152.011 90.8164 150.444 85.8977C148.877 80.979 151.536 75.6718 156.383 74.0438C161.229 72.4157 176.036 77.3009 176.036 77.3009C176.036 77.3009 166.904 90.2279 162.057 91.856Z" fill="url(#paint3_linear_61_16549)" fill-opacity="0.6"/>
  </g>
  <g filter="url(#filter1_b_61_16549)">
    <path d="M158.252 90.2062C154.807 91.3634 151.111 89.4672 149.997 85.9709C148.883 82.4746 150.773 78.7022 154.218 77.5449C157.663 76.3877 168.188 79.8601 168.188 79.8601C168.188 79.8601 161.697 89.0489 158.252 90.2062Z" fill="url(#paint4_linear_61_16549)" fill-opacity="0.4"/>
  </g>
  <g filter="url(#filter2_b_61_16549)">
    <path d="M155.412 88.6525C153.374 89.3373 151.187 88.2152 150.528 86.1461C149.868 84.077 150.987 81.8445 153.026 81.1597C155.064 80.4748 161.293 82.5298 161.293 82.5298C161.293 82.5298 157.451 87.9676 155.412 88.6525Z" fill="url(#paint5_linear_61_16549)"/>
  </g>
  <g filter="url(#filter3_i_61_16549)">
    <path d="M100.164 117.153C84.602 115.576 72.5291 109.205 72.2342 103.461C71.9393 97.7175 80.4181 86.0918 100.674 77.4177C120.93 68.7435 140.512 68.8556 149.294 77.7473C158.076 86.639 156.354 96.9255 148.586 104.846C138.678 114.946 119.617 119.124 100.164 117.153Z" fill="white"/>
  </g>
  <g filter="url(#filter4_d_61_16549)">
    <ellipse cx="22.3778" cy="9.80747" rx="22.3778" ry="9.80747" transform="matrix(0.96752 -0.256091 0.240851 0.969748 91.2131 78.286)" fill="url(#paint6_linear_61_16549)"/>
  </g>
  <ellipse cx="20.2787" cy="8.49215" rx="20.2787" ry="8.49215" transform="matrix(0.967524 -0.256076 0.240865 0.969745 93.0193 78.0415)" fill="url(#paint7_linear_61_16549)"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M94.3818 116.315C81.6866 113.966 72.4569 108.492 72.2036 103.488C72.0065 99.5939 75.8591 92.9885 84.6998 86.4318C91.0162 90.8498 95.3056 98.0957 95.7304 106.497C95.9039 109.929 95.4167 113.244 94.3818 116.315Z" fill="url(#paint8_linear_61_16549)"/>
  <path d="M91.5315 115.709C80.3907 113.096 72.5469 108.055 72.3104 103.422C72.1239 99.7686 75.4615 93.7392 83.0894 87.5994C88.5801 92.0806 92.2389 98.8524 92.6346 106.603C92.7964 109.772 92.3993 112.841 91.5315 115.709Z" fill="url(#paint9_linear_61_16549)"/>
  <g filter="url(#filter5_ii_61_16549)">
    <path d="M107.981 78.8429C110.423 78.2889 110.852 75.6448 110.761 74.392C110.345 71.8386 108.146 69.7845 100.726 69.2195C93.306 68.6545 84.4934 72.5567 85.2094 78.4319C85.9255 84.3071 92.9807 84.1085 95.4551 82.6394C97.9294 81.1703 104.928 79.5355 107.981 78.8429Z" fill="white"/>
    <path d="M107.981 78.8429C110.423 78.2889 110.852 75.6448 110.761 74.392C110.345 71.8386 108.146 69.7845 100.726 69.2195C93.306 68.6545 84.4934 72.5567 85.2094 78.4319C85.9255 84.3071 92.9807 84.1085 95.4551 82.6394C97.9294 81.1703 104.928 79.5355 107.981 78.8429Z" fill="url(#paint10_linear_61_16549)" fill-opacity="0.4"/>
  </g>
  <g filter="url(#filter6_ii_61_16549)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M129.655 83.4499C126.48 85.7778 121.94 87.8954 116.712 89.2866C110.127 91.0391 103.963 91.236 99.8868 90.106C98.6004 87.6568 97.8987 84.8289 97.9712 81.8277C98.1912 72.7079 105.475 65.4411 114.241 65.5967C126.965 65.8227 129.933 73.2717 129.713 82.3915C129.704 82.7472 129.685 83.1002 129.655 83.4499Z" fill="white"/>
  </g>
  <path opacity="0.25" fill-rule="evenodd" clip-rule="evenodd" d="M129.655 83.4499C126.48 85.7778 121.94 87.8954 116.712 89.2866C110.127 91.0391 103.963 91.236 99.8868 90.106C98.6004 87.6568 97.8987 84.8289 97.9712 81.8277C98.1912 72.7079 105.475 65.4411 114.241 65.5967C126.965 65.8227 129.933 73.2717 129.713 82.3915C129.704 82.7472 129.685 83.1002 129.655 83.4499Z" fill="url(#paint11_linear_61_16549)"/>
  <g opacity="0.5" filter="url(#filter7_f_61_16549)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M127.937 72.5964C123.209 75.0014 117.46 76.1491 111.282 76.0311C107.005 75.9493 103.233 75.2744 100.014 74.0139C102.827 68.9389 108.148 65.5611 114.172 65.6763C121.756 65.8212 125.883 68.5171 127.937 72.5964Z" fill="#78818D"/>
  </g>
  <ellipse cx="6.35094" cy="6.80433" rx="6.35094" ry="6.80433" transform="matrix(0.999361 0.0190279 -0.0386586 0.999783 100.022 74.9788)" fill="#CACDD6"/>
  <ellipse cx="5.28356" cy="5.66074" rx="5.28356" ry="5.66074" transform="matrix(0.999361 0.0190279 -0.0386586 0.999783 101.044 76.1424)" fill="url(#paint12_linear_61_16549)"/>
  <ellipse opacity="0.54" cx="3.22941" cy="3.45996" rx="3.22941" ry="3.45996" transform="matrix(0.999361 0.0190279 -0.0386586 0.999783 103.457 76.6059)" fill="url(#paint13_linear_61_16549)"/>
  <g filter="url(#filter8_dii_61_16549)">
    <path d="M121.343 76.4303C120.619 73.8994 122.75 72.1768 123.906 71.6319C126.377 70.7338 129.294 71.7321 133.489 78.2925C137.684 84.8528 138.611 94.9397 133.045 97.2278C127.479 99.5158 124.142 92.9085 124.213 89.8889C124.284 86.8693 122.249 79.5939 121.343 76.4303Z" fill="white"/>
    <path d="M121.343 76.4303C120.619 73.8994 122.75 72.1768 123.906 71.6319C126.377 70.7338 129.294 71.7321 133.489 78.2925C137.684 84.8528 138.611 94.9397 133.045 97.2278C127.479 99.5158 124.142 92.9085 124.213 89.8889C124.284 86.8693 122.249 79.5939 121.343 76.4303Z" fill="url(#paint14_linear_61_16549)" fill-opacity="0.51"/>
  </g>
  <path d="M140.563 49.2186C140.196 65.2554 127.231 73.196 111.469 72.8957C95.7065 72.5954 86.8093 64.2382 87.1761 48.2014C87.543 32.1647 96.9249 19.3374 112.687 19.6377C128.45 19.938 140.93 33.1818 140.563 49.2186Z" fill="white"/>
  <path d="M140.563 49.2186C140.196 65.2554 127.231 73.196 111.469 72.8957C95.7065 72.5954 86.8093 64.2382 87.1761 48.2014C87.543 32.1647 96.9249 19.3374 112.687 19.6377C128.45 19.938 140.93 33.1818 140.563 49.2186Z" fill="url(#paint15_linear_61_16549)"/>
  <path d="M137.522 40.7256C137.554 26.6949 126.535 15.35 112.91 15.386C99.2853 15.422 88.2146 26.8254 88.1829 40.8561" stroke="url(#paint16_linear_61_16549)" stroke-width="2.5011"/>
  <path d="M139.267 43.6607C140.158 57.6901 129.828 69.8438 116.194 70.8066C114.035 70.9591 111.922 70.8218 109.892 70.4268" stroke="#9198AE" stroke-width="2.5011"/>
  <rect width="6.68652" height="3.61237" rx="1.80618" transform="matrix(0.987908 0.148062 -0.145786 0.990359 105.036 67.8522)" fill="#9198AE"/>
  <path d="M112.703 25.2237L113.13 22.9819L110.896 22.9878L110.468 25.2296L112.703 25.2237Z" fill="url(#paint17_linear_61_16549)"/>
  <path d="M105.885 32.6359L107.917 32.6305L108.973 27.0924L108.973 27.0922C109.175 26.0224 108.945 24.9983 108.335 24.2446C107.724 23.4909 106.783 23.0692 105.716 23.0721L104.318 23.0758L103.929 25.1147L105.327 25.111C105.855 25.1096 106.321 25.3179 106.623 25.6903C106.925 26.0627 107.04 26.5688 106.941 27.098L105.885 32.6359Z" fill="url(#paint18_linear_61_16549)"/>
  <path d="M127.059 49.6723C126.823 60.476 117.498 65.8054 104.664 65.5486C91.8288 65.2917 84.5522 59.6302 84.7885 48.8264C85.0248 38.0227 92.6136 29.4125 105.448 29.6694C118.283 29.9262 127.295 38.8685 127.059 49.6723Z" fill="#CACDD6"/>
  <path d="M123.52 49.4178C123.328 58.1763 115.348 62.4883 104.027 62.2618C92.7059 62.0352 86.2788 57.4349 86.4704 48.6764C86.662 39.918 93.3421 32.9484 104.663 33.1749C115.984 33.4015 123.711 40.6594 123.52 49.4178Z" fill="#18318B"/>
  <path d="M123.52 49.4178C123.328 58.1763 115.348 62.4883 104.027 62.2618C92.7059 62.0352 86.2788 57.4349 86.4704 48.6764C86.662 39.918 93.3421 32.9484 104.663 33.1749C115.984 33.4015 123.711 40.6594 123.52 49.4178Z" fill="url(#paint19_radial_61_16549)" fill-opacity="0.6"/>
  <path d="M123.52 49.4178C123.328 58.1763 115.348 62.4883 104.027 62.2618C92.7059 62.0352 86.2788 57.4349 86.4704 48.6764C86.662 39.918 93.3421 32.9484 104.663 33.1749C115.984 33.4015 123.711 40.6594 123.52 49.4178Z" fill="url(#paint20_radial_61_16549)" fill-opacity="0.4"/>
  <path d="M123.52 49.4178C123.328 58.1763 115.348 62.4883 104.027 62.2618C92.7059 62.0352 86.2788 57.4349 86.4704 48.6764C86.662 39.918 93.3421 32.9484 104.663 33.1749C115.984 33.4015 123.711 40.6594 123.52 49.4178Z" fill="#9198AE"/>
  <path opacity="0.5" d="M93.8021 58.7313C90.0649 55.6829 88.771 50.897 88.7251 48.8953L87.2909 49.9166C87.5696 51.5066 88.3383 55.5171 93.8021 58.7313Z" fill="url(#paint21_linear_61_16549)"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M92.4009 44.0217C92.0807 43.5519 91.5607 43.5528 91.2395 44.0237C90.9183 44.4945 90.9175 45.2571 91.2376 45.7269L92.8619 48.1101L91.238 50.492C90.9175 50.9622 90.9167 51.7235 91.2361 52.1926C91.5556 52.6616 92.0744 52.6608 92.395 52.1906L94.0192 49.8082L95.6821 52.2482C96.0023 52.718 96.5223 52.7171 96.8435 52.2462C97.1647 51.7754 97.1655 51.0128 96.8454 50.543L95.182 48.1025L96.8445 45.664C97.165 45.1938 97.1658 44.4324 96.8463 43.9634C96.5269 43.4943 96.008 43.4952 95.6875 43.9654L94.0247 46.4044L92.4009 44.0217Z" fill="url(#paint22_linear_61_16549)"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M104.582 44.0213C104.262 43.5515 103.742 43.5524 103.421 44.0233C103.099 44.4942 103.099 45.2568 103.419 45.7265L105.043 48.1104L103.42 50.492C103.099 50.9622 103.098 51.7235 103.418 52.1926C103.737 52.6616 104.256 52.6608 104.577 52.1906L106.201 49.8085L107.863 52.2479C108.183 52.7177 108.703 52.7168 109.024 52.2459C109.346 51.775 109.347 51.0124 109.026 50.5427L107.363 48.1027L109.026 45.664C109.347 45.1938 109.347 44.4324 109.028 43.9634C108.708 43.4943 108.19 43.4952 107.869 43.9654L106.206 46.4046L104.582 44.0213Z" fill="url(#paint23_linear_61_16549)"/>
  <path opacity="0.5" d="M107.121 35.4592C112.761 36.9305 121.156 42.1477 117.557 48.1946C114.031 54.1187 94.0358 32.0456 107.121 35.4592Z" fill="url(#paint24_linear_61_16549)"/>
  <path d="M114.444 45.9633L114.444 48.5168" stroke="url(#paint25_linear_61_16549)" stroke-width="1.25055" stroke-linecap="round"/>
  <path d="M117.571 45.9633L117.571 50.4319" stroke="url(#paint26_linear_61_16549)" stroke-width="1.25055" stroke-linecap="round"/>
  <path d="M143.136 49.4201C142.849 54.5247 141.138 58.8242 138.941 60.5949C138.197 61.1938 137.399 61.5034 136.581 61.4571C133.343 61.274 131.03 55.5878 131.413 48.7568C131.796 41.9257 134.731 36.5366 137.968 36.7197C141.205 36.9029 143.519 42.5891 143.136 49.4201Z" fill="url(#paint27_linear_61_16549)"/>
  <path opacity="0.4" d="M142.797 49.4585C142.533 54.1625 141.321 58.1451 139.817 59.8063C139.308 60.3681 138.766 60.6644 138.215 60.6333C136.038 60.51 134.559 55.3071 134.912 49.0122C135.265 42.7173 137.316 37.7142 139.493 37.8374C141.671 37.9607 143.15 43.1636 142.797 49.4585Z" fill="url(#paint28_linear_61_16549)"/>
  <path d="M126.407 105.389L145.18 96.5195L160.571 105.157C158.131 106.418 150.503 109.541 139.509 111.942C125.767 114.945 124.45 111.327 124.328 108.955C124.23 107.057 125.673 105.787 126.407 105.389Z" fill="url(#paint29_linear_61_16549)"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M128.721 104.307L126.244 105.521C132.64 108.916 136.383 109.452 145.079 108.195C151.507 107.267 156.551 106.29 159.552 105.636C159.958 105.45 160.3 105.283 160.576 105.141L145.187 96.5304L128.721 104.307Z" fill="url(#paint30_linear_61_16549)"/>
  <path d="M133.203 -6.10352e-05V20.4282L140.588 3.64784L133.203 -6.10352e-05Z" fill="#B8BDCC"/>
  <path opacity="0.5" d="M146.036 11.4908C143.019 14.0848 136.604 19.4675 135.079 20.2457C139.049 19.1108 147.18 16.841 147.942 16.841C148.704 16.841 146.989 13.2742 146.036 11.4908Z" fill="#B8BDCC"/>
  <defs>
    <filter id="filter0_b_61_16549" x="149.201" y="72.9181" width="27.6288" height="20.209" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.396727"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_61_16549"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_61_16549" result="shape"/>
    </filter>
    <filter id="filter1_b_61_16549" x="148.884" y="76.5154" width="20.0979" height="14.8238" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.396727"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_61_16549"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_61_16549" result="shape"/>
    </filter>
    <filter id="filter2_b_61_16549" x="149.545" y="80.2265" width="12.5416" height="9.42041" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.396727"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_61_16549"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_61_16549" result="shape"/>
    </filter>
    <filter id="filter3_i_61_16549" x="70.9763" y="58.4888" width="84.2036" height="59.1391" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-1.25055" dy="-12.5055"/>
      <feGaussianBlur stdDeviation="7.84824"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.762222 0 0 0 0 0.777467 0 0 0 0 0.816667 0 0 0 0.74 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_61_16549"/>
    </filter>
    <filter id="filter4_d_61_16549" x="92.8171" y="70.9605" width="44.8183" height="23.4668" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.627859"/>
      <feGaussianBlur stdDeviation="0.31393"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.504687 0 0 0 0 0.57375 0 0 0 0 0.6375 0 0 0 0.4 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_61_16549"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_61_16549" result="shape"/>
    </filter>
    <filter id="filter5_ii_61_16549" x="78.8899" y="64.4563" width="31.8822" height="19.0528" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-6.27859" dy="-4.70895"/>
      <feGaussianBlur stdDeviation="4.70895"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.58 0 0 0 0 0.668 0 0 0 0 0.8 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_61_16549"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-3.1393" dy="-3.1393"/>
      <feGaussianBlur stdDeviation="1.56965"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="effect1_innerShadow_61_16549" result="effect2_innerShadow_61_16549"/>
    </filter>
    <filter id="filter6_ii_61_16549" x="93.2571" y="62.455" width="36.467" height="28.356" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-4.70894" dy="-3.1393"/>
      <feGaussianBlur stdDeviation="4.70894"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.468733 0 0 0 0 0.502906 0 0 0 0 0.554167 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_61_16549"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-3.1393" dy="-3.1393"/>
      <feGaussianBlur stdDeviation="1.56965"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="effect1_innerShadow_61_16549" result="effect2_innerShadow_61_16549"/>
    </filter>
    <filter id="filter7_f_61_16549" x="93.7358" y="59.3948" width="40.4802" height="22.9231" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="3.1393" result="effect1_foregroundBlur_61_16549"/>
    </filter>
    <filter id="filter8_dii_61_16549" x="114.918" y="66.641" width="24.3662" height="34.1984" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="0.784824" dy="1.56965"/>
      <feGaussianBlur stdDeviation="0.784824"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.732951 0 0 0 0 0.764568 0 0 0 0 0.870833 0 0 0 0.4 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_61_16549"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_61_16549" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-6.27859" dy="-4.70894"/>
      <feGaussianBlur stdDeviation="4.70894"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.721569 0 0 0 0 0.741176 0 0 0 0 0.8 0 0 0 0.84 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow_61_16549"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-3.1393" dy="-3.1393"/>
      <feGaussianBlur stdDeviation="1.56965"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="effect2_innerShadow_61_16549" result="effect3_innerShadow_61_16549"/>
    </filter>
    <linearGradient id="paint0_linear_61_16549" x1="124.108" y1="120.854" x2="124.108" y2="34.4879" gradientUnits="userSpaceOnUse">
      <stop stop-color="#DEDEDE" stop-opacity="0"/>
      <stop offset="1" stop-color="#A9A9A9" stop-opacity="0.3"/>
    </linearGradient>
    <linearGradient id="paint1_linear_61_16549" x1="113.598" y1="143.008" x2="113.598" y2="118.514" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0"/>
      <stop offset="1" stop-color="#96A1C5" stop-opacity="0.373"/>
    </linearGradient>
    <linearGradient id="paint2_linear_61_16549" x1="120" y1="150" x2="120" y2="109.777" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0"/>
      <stop offset="1" stop-color="#919191" stop-opacity="0.15"/>
    </linearGradient>
    <linearGradient id="paint3_linear_61_16549" x1="176.036" y1="77.3009" x2="150.316" y2="85.4944" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C7CCD0"/>
      <stop offset="1" stop-color="#B5BDC2" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint4_linear_61_16549" x1="168.188" y1="79.8602" x2="149.906" y2="85.6843" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint5_linear_61_16549" x1="161.293" y1="82.5298" x2="150.474" y2="85.9765" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="#D4D4D5" stop-opacity="0.31"/>
    </linearGradient>
    <linearGradient id="paint6_linear_61_16549" x1="22.3497" y1="-0.0043474" x2="24.4884" y2="21.1001" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="0.286458" stop-color="#E0E4F1"/>
      <stop offset="0.552083" stop-color="#F8F8FF"/>
      <stop offset="0.755208" stop-color="#C2CDDD"/>
      <stop offset="1" stop-color="white"/>
    </linearGradient>
    <linearGradient id="paint7_linear_61_16549" x1="25.4595" y1="16.3767" x2="21.0017" y2="-0.472814" gradientUnits="userSpaceOnUse">
      <stop stop-color="#D5DBE9"/>
      <stop offset="1" stop-color="#9EA2AF"/>
    </linearGradient>
    <linearGradient id="paint8_linear_61_16549" x1="83.0046" y1="86.5223" x2="93.8084" y2="116.194" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="0.286458" stop-color="#ECF0FD"/>
      <stop offset="0.552083" stop-color="white"/>
      <stop offset="0.755208" stop-color="#EEF2F8"/>
    </linearGradient>
    <linearGradient id="paint9_linear_61_16549" x1="77.5868" y1="91.9535" x2="84.7541" y2="113.812" gradientUnits="userSpaceOnUse">
      <stop stop-color="#DBE0ED"/>
      <stop offset="0.442708" stop-color="#E2E6F3"/>
      <stop offset="1" stop-color="#B8BDCC"/>
    </linearGradient>
    <linearGradient id="paint10_linear_61_16549" x1="102.163" y1="76.1225" x2="96.5991" y2="76.8381" gradientUnits="userSpaceOnUse">
      <stop stop-color="#717A88"/>
      <stop offset="1" stop-color="#737D8C" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint11_linear_61_16549" x1="111.86" y1="77.952" x2="114.461" y2="90.341" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0"/>
      <stop offset="1" stop-color="#A4ADBF"/>
    </linearGradient>
    <linearGradient id="paint12_linear_61_16549" x1="5.28356" y1="0" x2="5.28356" y2="11.3215" gradientUnits="userSpaceOnUse">
      <stop stop-color="#9198AE"/>
      <stop offset="1" stop-color="#B1B9D0"/>
    </linearGradient>
    <linearGradient id="paint13_linear_61_16549" x1="3.22941" y1="-4.80635e-08" x2="2.37218" y2="4.42177" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint14_linear_61_16549" x1="123.226" y1="72.1263" x2="126.299" y2="77.8924" gradientUnits="userSpaceOnUse">
      <stop stop-color="#9198AE"/>
      <stop offset="1" stop-color="#4C6385" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint15_linear_61_16549" x1="114.547" y1="19.6731" x2="113.533" y2="72.8903" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="0.0001" stop-color="white"/>
      <stop offset="1" stop-color="#CFD5E4"/>
    </linearGradient>
    <linearGradient id="paint16_linear_61_16549" x1="112.91" y1="15.386" x2="112.977" y2="40.7905" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C1C5D3"/>
      <stop offset="1" stop-color="#9198AE"/>
    </linearGradient>
    <linearGradient id="paint17_linear_61_16549" x1="108.731" y1="22.9936" x2="108.756" y2="32.6282" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B8BDCC"/>
      <stop offset="1" stop-color="#B8BDCC" stop-opacity="0.17"/>
    </linearGradient>
    <linearGradient id="paint18_linear_61_16549" x1="108.731" y1="22.9936" x2="108.756" y2="32.6282" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B8BDCC"/>
      <stop offset="1" stop-color="#B8BDCC" stop-opacity="0.17"/>
    </linearGradient>
    <radialGradient id="paint19_radial_61_16549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(106.849 38.0476) rotate(103.156) scale(19.5302 26.8357)">
      <stop stop-color="#5EE0FF"/>
      <stop offset="1" stop-color="#60E1FE" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="paint20_radial_61_16549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(125.172 49.1049) rotate(118.344) scale(21.9182 9.79601)">
      <stop stop-color="#5EFFFF"/>
      <stop offset="1" stop-color="#55FFFF" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="paint21_linear_61_16549" x1="92.4194" y1="57.3215" x2="88.2117" y2="50.1954" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint22_linear_61_16549" x1="94.8931" y1="43.4354" x2="94.9101" y2="53.5175" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint23_linear_61_16549" x1="107.074" y1="43.4354" x2="107.091" y2="53.5171" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint24_linear_61_16549" x1="113.201" y1="37.0455" x2="108.516" y2="42.1361" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint25_linear_61_16549" x1="114.441" y1="46.2736" x2="114.469" y2="46.2739" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint26_linear_61_16549" x1="117.567" y1="46.2355" x2="117.617" y2="46.236" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint27_linear_61_16549" x1="132.587" y1="46.6062" x2="143.824" y2="46.6951" gradientUnits="userSpaceOnUse">
      <stop stop-color="#9499A7"/>
      <stop offset="1" stop-color="#B8BDCC"/>
    </linearGradient>
    <linearGradient id="paint28_linear_61_16549" x1="139.493" y1="37.8374" x2="138.203" y2="60.6326" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B8BDCC"/>
      <stop offset="1" stop-color="#B8BDCC" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="paint29_linear_61_16549" x1="157.085" y1="106.505" x2="128.667" y2="115.221" gradientUnits="userSpaceOnUse">
      <stop stop-color="#E6EBEF"/>
      <stop offset="1" stop-color="#CDD1D6"/>
    </linearGradient>
    <linearGradient id="paint30_linear_61_16549" x1="153.445" y1="111.607" x2="140.722" y2="98.9863" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="#E1E4E8"/>
    </linearGradient>
  </defs>
</svg>