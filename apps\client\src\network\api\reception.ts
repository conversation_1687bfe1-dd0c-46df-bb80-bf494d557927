import client from "@/network/client";
import type { APIResponse } from "@workspace/types";

export interface saveStarMarkProps {
  platform: number;
  sellerAccount: string;
  color: string;
  buyerId: string;
}

export interface IStarMarkReq {
  platform: string;
  sellerAccount: string;
  color: string;
  buyerId: string;
  id: string;
  thirdShopId: number;
}

/** 保存星标 */
export function saveStarMark(data: saveStarMarkProps) {
  return client<APIResponse<IStarMarkReq>>("/api/im/agent/buyer-mark/save", {
    method: "POST",
    data,
  });
}

//删除星标
export function deleteStarMark(data: { id: string }) {
  return client<APIResponse<boolean>>("/api/im/agent/buyer-mark/delete", {
    method: "POST",
    data,
    shouldLog: true,
  });
}

export interface batchDeleteStarMarkProps {
  sellerAccounts: Array<{
    platform: number;
    sellerAccount: string;
  }>;
  colors: string[];
}

/** 批量删除星标 */
export function batchDeleteStarMark(data: batchDeleteStarMarkProps) {
  return client<APIResponse<boolean>>("/api/im/agent/buyer-mark/delete-batch", {
    method: "POST",
    data,
  });
}
