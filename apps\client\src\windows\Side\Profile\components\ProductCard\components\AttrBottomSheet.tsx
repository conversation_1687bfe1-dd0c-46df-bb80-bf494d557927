import {
  type IProductItem,
  type IShopAttr,
  addShopAttr,
  editShopAttr,
} from "@/network/api/product";
import { Button, Drawer, type DrawerProps, Form, Input } from "antd";
import clsx from "clsx";
import TextArea from "rc-textarea";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import CloseOutlined from "~icons/ant-design/close-outlined";
import UploadImage from "./UploadImage";

interface AttrBottomSheetProps extends DrawerProps {
  data?: NonNullable<IProductItem["userAttrs"]>[number] & { productId: string };
  onSuccess: (data: IShopAttr) => void;
  tyShopId: string;
  sellerAccount: string;
}

const AttrBottomSheet = ({
  open,
  onClose,
  data,
  onSuccess,
  tyShopId,
  sellerAccount,
}: AttrBottomSheetProps) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<Array<string>>([]);
  const [loading, setLoading] = useState(false);

  /** 编辑 */
  const handleAttrEdit = async (
    values: Pick<IShopAttr, "attrName" | "attrValue">,
  ) => {
    setLoading(true);
    const params: IShopAttr = {
      userOwn: true,
      attrPic: fileList[0],
      account: sellerAccount,
      productId: data?.productId as string, //商品id
      shopId: tyShopId, //当前店铺id
      proAttrId: data?.proAttrId as string,
      ...values,
      attrType: 99, //1:自定义商品属性  2:自定义店铺属性
    };
    const res = await editShopAttr(params).promise.catch((error) => error);
    setLoading(false);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      // props.getArrtList();
      onSuccess?.(params);
    }
  };
  /** 新增 */
  const handleAttrAdd = async (
    values: Pick<IShopAttr, "attrName" | "attrValue">,
  ) => {
    setLoading(true);
    const params: IShopAttr = {
      userOwn: true,
      attrPic: fileList[0],
      account: sellerAccount,
      productId: data?.productId as string, //商品id
      shopId: tyShopId, //当前店铺id
      ...values,
      attrType: 99,
      proAttrId: "",
    };
    const res = await addShopAttr(params).promise.catch((error) => error);
    setLoading(false);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      onSuccess?.(params);
    }
  };
  const onFinish = (values: Pick<IShopAttr, "attrName" | "attrValue">) => {
    if (data?.proAttrId) {
      handleAttrEdit(values);
    } else {
      handleAttrAdd(values);
    }
  };

  useEffect(() => {
    if (!open) {
      form?.resetFields();
      setFileList([]);
    } else {
      form?.setFieldsValue({
        attrName: data?.name,
        attrValue: data?.value,
      });
      setFileList(data?.attrPic ? [data.attrPic] : []);
    }
  }, [form, open, data]);

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <span>{data ? "修改商品属性" : "新增商品属性"}</span>
          <button
            type="button"
            className="bg-transparent outline-none border-none cursor-pointer "
            onClick={onClose}
          >
            <CloseOutlined />
          </button>
        </div>
      }
      footer={
        <div className="flex items-center justify-end gap-2 sticky bottom-0 bg-white">
          <Button onClick={onClose}>取消</Button>
          <Button
            type="primary"
            htmlType="submit"
            form="attr-form"
            loading={loading}
          >
            确认
          </Button>
        </div>
      }
      closeIcon={null}
      placement="bottom"
      open={open}
      height="auto"
      className="overflow-hidden rounded-t-8 [&_.ant-drawer-body]:max-h-[70vh] [&_.ant-drawer-body]:p-3  [&_.ant-drawer-footer]:px-3 [&_.ant-drawer-header]:px-3 [&_.ant-drawer-header]:py-[14px]"
    >
      <div className="h-full overflow-auto">
        <Form
          form={form}
          id="attr-form"
          onFinish={onFinish}
          layout="vertical"
          colon={false}
        >
          <Form.Item label="属性名称" required className="mb-4">
            <div>
              <Form.Item
                noStyle
                name="attrName"
                rules={[{ required: true, message: "请输入属性名称" }]}
              >
                <Input disabled={!!data?.proAttrId} placeholder="属性名称" />
              </Form.Item>
              <div
                className={clsx(
                  "text-12 text-black-3 mt-2",
                  data?.proAttrId ? "node" : "block",
                )}
              >
                新增后属性名称不可变更
              </div>
            </div>
          </Form.Item>
          <Form.Item label="属性值" required>
            <div>
              <div className="border rounded-4 border-gray-4 border-solid hover:border-blue-7 overflow-hidden">
                <Form.Item
                  noStyle
                  name="attrValue"
                  rules={[{ required: true, message: "请输入属性值" }]}
                >
                  <TextArea
                    autoSize
                    className="min-h-[200px] w-full block box-border px-3 py-2 resize-none flex-1 border-none outline-none"
                    maxLength={500}
                    placeholder="请输入属性值"
                  />
                </Form.Item>
                <div className="flex justify-end">
                  <UploadImage list={fileList} changeList={setFileList} />
                </div>
              </div>
            </div>
          </Form.Item>
        </Form>
      </div>
    </Drawer>
  );
};

export default AttrBottomSheet;
