import useWebSocket from "@/business/hooks/useWebsocket";
import {
  WsCmdReceive,
  WsCmdSend,
  type WsReceiveMessage,
} from "@/constant/protocol/index";
import { useLast } from "@workspace/ui";
import { useCallback, useEffect, useRef } from "react";
import { Outlet } from "react-router";
import { receptionStore } from "./store";

const ReceptionProvider = () => {
  const socket = useWebSocket();
  const getSocket = useLast(socket);
  const inited = useRef(false);

  const getStore = useCallback(() => {
    return receptionStore.getState();
  }, []);

  useEffect(() => {
    const socket = getSocket();
    if (!getStore()?.unsubscribe && socket) {
      const unsubscribe = socket?.on("message", (payload: WsReceiveMessage) => {
        switch (payload.cmd) {
          /** 会话列表 */
          case WsCmdReceive.ReceptionList: {
            getStore().addConversations(payload.data || []);
            break;
          }
          /** 新的会话 */
          case WsCmdReceive.NewReceptionConversation: {
            getStore().addConversations([payload.data]);
            break;
          }
          /** 移除会话 */
          case WsCmdReceive.ReceptionConversationRemove: {
            getStore().removeConversation(payload.data);
            break;
          }
          /** 更新会话  */
          case WsCmdReceive.ReceptionConversationUpdate: {
            if (payload.data.updateType === "RL") {
              getStore().updateConversation({
                ...payload.data,
                ...(!payload.data.isRead ? { isReply: true } : {}),
              });
            } else {
              getStore().updateConversation({
                isLight: payload.data.isLight,
                sessionKey: payload.data.sessionKey,
              });
            }
            break;
          }
          /** 获取快捷键配置 */
          case WsCmdReceive.ReceptionConfig: {
            getStore().setShortcut({
              openConversation:
                payload.data.aggregatedListOpenConversationShortcut,
            });
            break;
          }
          default:
            break;
        }
      });

      getStore().setSubscribe(unsubscribe);
    }

    return getStore().unsubscribe;
  }, [getStore, getSocket]);

  useEffect(() => {
    if (socket?.readyState === 1 && !inited.current) {
      inited.current = true;
      socket?.sendMessage({
        cmd: WsCmdSend.GetReceptionList,
        data: undefined,
      });
      socket.sendMessage({
        cmd: WsCmdSend.GetReceptionConfig,
        data: undefined,
      });
    }
    if (socket?.readyState !== 1) {
      inited.current = false;
    }
  }, [socket, socket?.readyState]);

  return <Outlet context={socket} />;
};

export default ReceptionProvider;
