const OSS = require("ali-oss");
const fs = require("node:fs");
const path = require("node:path");

const options = {
  region: process.env.OSS_REGION,
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_SECRET,
  bucket: process.env.OSS_BUCKET,
};

console.log("options", options);
console.log("process.env", process.env);
// 创建 OSS 客户端
const client = new OSS(options);

/**
 * 递归上传文件夹下的所有文件
 * @param {string} localPath 本地文件夹路径
 * @param {string} ossBasePath OSS中的基础路径
 */

const osetPath = process.env.OSS_BASE_DIR_CLIENT;

async function uploadDirectory(localPath, ossBasePath = osetPath) {
  try {
    // 读取本地目录
    const files = fs.readdirSync(localPath);

    // 遍历所有文件和文件夹
    for (const file of files) {
      const localFilePath = path.join(localPath, file);
      const stats = fs.statSync(localFilePath);

      if (stats.isDirectory()) {
        // 如果是文件夹，递归上传
        const nextOssPath = path.join(ossBasePath, file).replace(/\\/g, "/");
        await uploadDirectory(localFilePath, nextOssPath);
      } else {
        // 如果是文件，直接上传
        const ossPath = path.join(ossBasePath, file).replace(/\\/g, "/");
        try {
          const result = await client.put(ossPath, localFilePath);
          console.log(`Successfully uploaded: ${ossPath}`);
          console.log("OSS response:", result.res.status);
        } catch (err) {
          console.error(`Failed to upload ${ossPath}:`, err);
        }
      }
    }
  } catch (err) {
    console.error("Error reading directory:", err);
    throw err;
  }
}

/**
 * 上传指定路径下的所有文件
 * @param {string} localDirPath 本地文件夹路径
 * @param {string} ossBasePath OSS中的基础路径
 */
async function uploadAll(localDirPath, ossBasePath = "") {
  try {
    // 检查本地路径是否存在
    if (!fs.existsSync(localDirPath)) {
      throw new Error(`Local directory ${localDirPath} does not exist`);
    }

    console.log(
      `Starting upload from ${localDirPath} to OSS path ${ossBasePath}`,
    );
    await uploadDirectory(localDirPath, ossBasePath);
    console.log("Upload completed successfully");
  } catch (err) {
    console.error("Upload failed:", err);
    throw err;
  }
}

uploadAll(path.resolve(__dirname, "../dist"), osetPath);
