import AgentProvider from "@/business/components/AgentProvider";
import ConfigProvider from "@/business/components/ConfigProvider";
import DownloadProvider from "@/business/components/DowloadProvider";
import ReceptionProvider from "@/business/components/ReceptionProvider";
import ServiceProvider from "@/business/components/ServiceccountProvider";
import SessionProvider from "@/business/components/SessionProvider";
import WebsockectProvider from "@/business/components/WebsocketProvider";
import Provider from "@/components/AppProvider";
import ErrorBoundary from "@/components/ErrorBoundary";
import { getFullPath } from "@/utils/route";
import { createBrowserRouter, matchPath, redirect } from "react-router";
import { Paths } from "./path";

const router = createBrowserRouter([
  {
    Component: Provider,
    ErrorBoundary: ErrorBoundary,
    children: [
      {
        Component: WebsockectProvider,
        children: [
          {
            Component: ConfigProvider,
            children: [
              {
                Component: DownloadProvider,
                children: [
                  {
                    path: Paths.Root,
                    index: true,
                    loader: ({ request }) => {
                      console.log(request);
                      const url = new URL(request.url);
                      const searchParams = url.searchParams;
                      const pathMatch = matchPath(Paths.Root, url.pathname);

                      return redirect(
                        getFullPath(Paths.Login, {
                          query: Object.fromEntries(searchParams),
                          params: pathMatch?.params || { versionNo: "" },
                        }),
                      );
                    },
                  },

                  {
                    path: Paths.Login,
                    lazy: () => import("@/windows/Login"),
                  },
                  {
                    path: Paths.Console,
                    index: true,
                    loader: ({ request }) => {
                      const url = new URL(request.url);
                      const searchParams = url.searchParams;
                      const pathMatch = matchPath(Paths.Console, url.pathname);
                      if (pathMatch) {
                        return redirect(
                          getFullPath(Paths.Hosting, {
                            query: Object.fromEntries(searchParams),
                            params: pathMatch.params,
                          }),
                        );
                      }
                      return redirect(
                        getFullPath(Paths.Hosting, {
                          query: Object.fromEntries(searchParams),
                          params: { versionNo: "" },
                        }),
                      );
                    },
                  },
                  {
                    Component: ServiceProvider,
                    children: [
                      {
                        path: Paths.Hosting,
                        lazy: () => import("@/windows/Console/Hosting"),
                      },
                      {
                        path: Paths.ChatAgentVersion,
                        lazy: () =>
                          import("@/windows/Console/ChatAgentVersion"),
                      },
                    ],
                  },
                  {
                    path: Paths.SystemConfig,
                    lazy: () => import("@/windows/Console/SystemConfig"),
                  },
                ],
              },
            ],
          },

          {
            path: Paths.Side,
            loader: ({ request }) => {
              const url = new URL(request.url);
              const searchParams = url.searchParams;
              const pathMatch = matchPath(Paths.Side, url.pathname);
              if (pathMatch) {
                return redirect(
                  getFullPath(Paths.Agent, {
                    query: Object.fromEntries(searchParams),
                    params: pathMatch.params,
                  }),
                );
              }
              return redirect(
                getFullPath(Paths.Agent, {
                  query: Object.fromEntries(searchParams),
                  params: { versionNo: "" },
                }),
              );
            },
          },
          {
            Component: SessionProvider,
            children: [
              {
                Component: AgentProvider,
                children: [
                  {
                    path: Paths.Profile,
                    lazy: () => import("@/windows/Side/Profile"),
                  },
                  {
                    path: Paths.Agent,
                    lazy: () => import("@/windows/Side/Agent"),
                  },
                  {
                    path: Paths.WorkOrder,
                    lazy: () => import("@/windows/Side/WorkOrder"),
                  },
                ],
              },
            ],
          },
          {
            path: Paths.OldReception,
            loader: ({ request }) => {
              const url = new URL(request.url);
              const searchParams = url.searchParams;
              const pathMatch = matchPath(Paths.OldReception, url.pathname);
              if (pathMatch) {
                return redirect(
                  getFullPath(Paths.Reception, {
                    query: Object.fromEntries(searchParams),
                    params: pathMatch.params,
                  }),
                );
              }
              return redirect(
                getFullPath(Paths.Reception, {
                  query: Object.fromEntries(searchParams),
                  params: { versionNo: "" },
                }),
              );
            },
          },
          {
            Component: ReceptionProvider,
            children: [
              {
                path: Paths.Reception,
                lazy: () => import("@/windows/Reception"),
              },
            ],
          },
        ],
      },
      {
        path: Paths.Preview,
        lazy: () => import("@/windows/Preview"),
      },
    ],
  },
]);

export default router;
