{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["OSS_REGION", "OSS_ACCESS_KEY_ID", "OSS_ACCESS_SECRET", "OSS_BUCKET", "OSS_BASE_URL", "OSS_BASE_DIR_CLIENT"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "build:test": {"dependsOn": ["^build:test"], "outputs": ["dist/**", ".env.test", ".next/**", "!.next/cache/**"]}, "build:prod": {"dependsOn": ["^build:prod"], "outputs": ["dist/**", ".env.production", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}}}