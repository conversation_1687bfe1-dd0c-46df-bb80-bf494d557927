import useWebSocket from "@/business/hooks/useWebsocket";
import { Paths } from "@/config/path";
import { Keys } from "@/constant/localstorage";
import { WsCmdReceive, WsCmdSend, type WsMessage } from "@/constant/protocol";
import { tryCatch } from "@/utils";
import { getFullPath } from "@/utils/route";
import { useLast } from "@workspace/ui";
import { Tabs } from "antd";
import { useCallback, useEffect, useMemo, useRef } from "react";
import {
  Outlet,
  matchPath,
  useLocation,
  useNavigate,
  useSearchParams,
} from "react-router";
import AuthNotice from "../AuthNotice";
import ContentGuards from "../ContentGuards";
import { agentStore } from "./store";

const items = [
  {
    key: Paths.Agent,
    label: "智能体",
  },
  {
    key: Paths.WorkOrder,
    label: "智能工单",
  },
  {
    key: Paths.Profile,
    label: "画像",
  },
];

const AgentProvider = () => {
  const socket = useWebSocket();
  const [searchParams] = useSearchParams();
  const inited = useRef(false);

  const getSocket = useLast(socket);
  const getStore = useCallback(() => {
    return agentStore.getState();
  }, []);
  const navigate = useNavigate();
  const location = useLocation();

  const activeKey = useMemo(() => {
    if (matchPath(Paths.Agent, location.pathname)) return Paths.Agent;
    if (matchPath(Paths.Profile, location.pathname)) return Paths.Profile;
    return Paths.WorkOrder;
  }, [location.pathname]);

  const onChange = (key: string) => {
    const pathMatch = matchPath(Paths.Side, window.location.pathname);
    const params = pathMatch ? pathMatch.params : { version: "" };

    navigate(
      getFullPath(key, {
        query: Object.fromEntries(searchParams),
        params,
      }),
    );
  };

  useEffect(() => {
    const socket = getSocket();
    if (!getStore()?.unsubscribe && socket) {
      const unsubscribe = socket?.on("message", (data: WsMessage) => {
        switch (data.cmd) {
          case WsCmdReceive.AgentMsg: {
            const lastMsg = {
              ...data.data,
              parsedPayload:
                tryCatch(() => JSON.parse(data.data.payload)) || {},
            };
            getStore().setLastMsg(lastMsg);
            break;
          }
          case WsCmdReceive.BaseConfig: {
            getStore().setShortcut({
              reminderShortcut: data.data.reminderShortcut,
              groupSendShortcut: data.data.groupSendShortcut,
              singleSendShortcut: data.data.singleSendShortcut,
            });
            break;
          }
          default:
            break;
        }
      });

      getStore().setSubscribe(unsubscribe);
    }

    return getStore().unsubscribe;
  }, [getStore, getSocket]);

  useEffect(() => {
    if (socket?.readyState === 1 && !inited.current) {
      inited.current = true;
      socket?.sendMessage({
        cmd: WsCmdSend.GetBaseConfig,
        data: undefined,
      });
    }

    const eventHandler = (event: StorageEvent) => {
      if (event.key === Keys.ConfigBaseShortcut) {
        getStore().setShortcut({
          ...JSON.parse(event.newValue || "{}"),
        });
      }
    };

    window.addEventListener("storage", eventHandler);
    return () => {
      window.removeEventListener("storage", eventHandler);
    };
  }, [socket, socket?.readyState, getStore]);

  return (
    <ContentGuards>
      <div className="flex flex-col flex-1 min-h-0">
        <AuthNotice />
        <Tabs
          activeKey={activeKey}
          items={items}
          className="[&_.ant-tabs-nav]:mb-0 [&_.ant-tabs-nav]:px-2"
          onChange={onChange}
        />

        <div className="flex-1 min-h-0 flex flex-col">
          <Outlet context={socket} />
        </div>
      </div>
    </ContentGuards>
  );
};

export default AgentProvider;
