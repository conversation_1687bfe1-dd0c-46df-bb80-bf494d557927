import FlagFilled from "@/assets/svg/flag-filled.svg?react";
import FlagOutlined from "@/assets/svg/flag-outlined.svg?react";
import type { FormatedAgentMessageType } from "@/business/components/AgentProvider/store";
import useAgent from "@/business/hooks/useAgent";
import { type BatchSendMessageData, MsgContentMap } from "@/network/api/base";
import { AgentMessageStatusMap } from "@/network/api/copilot";
import { trimNewlines } from "@/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  useDebounce,
} from "@workspace/ui";
import { useContext, useMemo, useRef } from "react";
import QuestionCircleOutlined from "~icons/ant-design/question-circle-outlined";
import SendOutlined from "~icons/icon-park-outline/send";
import AgentContext from "../../context";
import { formatTime } from "../../utils";
import SkeletonWrapper from "../SkeletonWrapper";
import TextContent from "./TextContent";

export interface UrgeProps {
  data: FormatedAgentMessageType<"PURCHASE_PROMPT">;
  onSend: (id: string) => void;
  onMarkReview: (value: FormatedAgentMessageType<"PURCHASE_PROMPT">) => void;
  shortcutIndex?: number;
}

const Urge = ({ data, onSend, onMarkReview, shortcutIndex }: UrgeProps) => {
  const { store: groupSendShortcut } = useAgent(
    (state) => state.shortcut.groupSendShortcut,
  );
  const { sendMessages, shortcutIndexMap, updateMsg } =
    useContext(AgentContext);
  const localContent = useRef<string[]>(
    data.parsedPayload?.reply?.map((item) => trimNewlines(item.content)) || [],
  );
  const preData = useMemo(() => {
    return {
      ...data.parsedPayload,
      reply:
        data?.parsedPayload?.reply?.map((item) => ({
          ...item,
          content: trimNewlines(item.content),
        })) || [],
      time: formatTime(data.time),
      status: data.status,
      ifSend: data.ifSend,
      buyerAccount: data.buyerAccount,
      ifLabel: data.ifLabel,
      id: data.id,
    };
  }, [data]);

  const multMessageSendText = useDebounce(
    (items: BatchSendMessageData["msgContents"]) => {
      let editSend = undefined;
      const sourceContents = preData.reply.map((item) => item.content);
      items.forEach((item) => {
        if (
          item.type === MsgContentMap.text &&
          !sourceContents.includes(item.value)
        ) {
          editSend = "1";
        }
      });
      sendMessages({
        msgContents: items,
        feature: {
          // agent相关
          agent: "1",
          sidebar: "1",
          editSend,
          agentTopicId: data.id,
        },
        payload: JSON.stringify({
          ...data.parsedPayload,
          reply: data.parsedPayload.reply.map((item, index) => ({
            ...item,
            content: localContent.current[index],
          })),
        }),
        cardId: data.id,
        buyerAccount: preData.buyerAccount,
      }).then((ifSuccess: boolean) => {
        if (ifSuccess) {
          onSend(data.id);
        }
      });
    },
    500,
    { leading: true, trailing: false },
  );

  const handleSendAll = () => {
    multMessageSendText(
      localContent.current.map((item) => ({ type: 0, value: item })),
    );
  };

  const handleContentChange = (text: string, index: number) => {
    localContent.current[index] = text;
    updateMsg({
      ...data,
      parsedPayload: {
        ...data.parsedPayload,
        reply: data.parsedPayload.reply.map((item, i) => ({
          ...item,
          content: localContent.current[i],
        })),
      },
    });
  };
  const startIndex = shortcutIndexMap.get(preData.id)?.[0];

  return (
    <div className="bg-white overflow-hidden  rounded-8 [--card-theme:theme(colors.clove.7)]">
      <SkeletonWrapper.Header loading={!data.time}>
        <div className="px-2 pt-1 pb-0.5 leading-[22px] text-12 flex items-center bg-gradient-to-b from-violet-50 to-white">
          {shortcutIndex ? (
            <span className="text-white bg-clove-7 font-medium mr-1 rounded-sm w-4 h-4 flex items-center justify-center">
              {shortcutIndex}
            </span>
          ) : null}
          <span className="text-clove-7 flex-shrink-0 font-medium">催单</span>
          <span className="text-black-2 ml-2">{preData.time}</span>
          <div className="ml-auto flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger onClick={handleSendAll}>
                  {preData?.ifSend ? (
                    <button
                      type="button"
                      className="flex gap-0.5 border border-green-4 bg-green-1 rounded-4 items-center text-green-7 px-1 bg-transparent text-12 outline-none"
                    >
                      <SendOutlined className="text-14" />
                      已发送
                    </button>
                  ) : (
                    <div className="bg-transparent text-14 outline-none border-none text-blue-7 cursor-pointer p-1 hover:text-blue-5">
                      <SendOutlined />
                    </div>
                  )}
                </TooltipTrigger>
                <TooltipContent
                  collisionPadding={20}
                  className="max-w-[--radix-tooltip-content-available-width] whitespace-pre-wrap break-words bg-black-4"
                  avoidCollisions
                >
                  <span>
                    {shortcutIndex && groupSendShortcut
                      ? `发送（${groupSendShortcut} + ${shortcutIndex}）`
                      : "发送"}
                  </span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </SkeletonWrapper.Header>
      <div className="space-y-2 p-2 pt-0.5">
        <SkeletonWrapper.Content
          loading={
            !preData.reply?.length &&
            ![
              AgentMessageStatusMap.FAILED.value,
              AgentMessageStatusMap.COMPLETED.value,
            ].includes(preData.status)
          }
        >
          {preData.reply?.map((item, index) => (
            <TextContent
              ifSend={preData.ifSend}
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              key={index}
              text={item.content}
              onSend={(items) => multMessageSendText(items)}
              onContentChange={(text) => handleContentChange(text, index)}
              shortcutIndex={startIndex ? startIndex + index : undefined}
            />
          ))}
          {preData?.reply?.length ? (
            <div className="text-black-2 text-12 flex items-center">
              <QuestionCircleOutlined />
              <span>商详/聊天/采纳/全店</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      type="button"
                      className="py-0.5  ml-auto"
                      onClick={() => onMarkReview(data)}
                    >
                      {!preData.ifLabel ? (
                        <FlagOutlined className="text-black-2" />
                      ) : (
                        <FlagFilled className="text-blue-7" />
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent className="bg-black-4">
                    <div className="space-y-1">
                      <p>点击标记本次生成结果</p>
                      <p>可在调优工坊筛选查看</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          ) : null}
        </SkeletonWrapper.Content>
      </div>
    </div>
  );
};

export default Urge;
