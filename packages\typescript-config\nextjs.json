{"$schema": "https://json.schemastore.org/tsconfig", "display": "Next.js", "extends": "./base.json", "compilerOptions": {"plugins": [{"name": "next"}], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "jsx": "preserve", "noEmit": true, "declaration": false, "declarationMap": false, "incremental": true, "lib": ["dom", "dom.iterable", "esnext"], "resolveJsonModule": true, "strict": false, "target": "es5"}, "include": ["src", "next-env.d.ts"], "exclude": ["node_modules"]}