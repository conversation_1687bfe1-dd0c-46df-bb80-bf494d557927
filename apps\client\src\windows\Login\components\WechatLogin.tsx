import { useAccount } from "@/business/hooks/useAccount";
import useWebSocket from "@/business/hooks/useWebsocket";
import { AccountTypeMap, WsCmdSend } from "@/constant/protocol";
import useRemote from "@/hooks/useRemote";
import { getWechatQrCode, postCheckWxLogin } from "@/network/api/account";
import { Button, Checkbox, Form, Spin } from "antd";
import clsx from "clsx";
import { useState } from "react";

const WechatLogin = () => {
  const sockect = useWebSocket();
  const [form] = Form.useForm();
  const policy = Form.useWatch("policy", form);
  const { data, isLoading, mutate } = useRemote(
    () => (policy ? [getWechatQrCode.name, policy] : null),
    getWechatQrCode,
  );
  const [interval, setInterval] = useState(true);
  const account = useAccount();
  // 完成帐号登录
  useRemote(
    data?.scene ? [postCheckWxLogin.name, data?.scene] : null,
    ([, scene]: [string, string]) => postCheckWxLogin({ scene }),
    {
      refreshInterval: interval ? 1000 : undefined,
      onSuccess(data) {
        if (data) {
          setInterval(false);
          account.setToken(data);
          account.updateAccountType(AccountTypeMap.Wechat);
          localStorage.removeItem("accountMap");
          sockect?.sendMessage({
            cmd: WsCmdSend.AccountLogin,
            data: {
              userName: "微信用户",
              cookie: data as string,
              showUserName: "微信用户",
              accountType: AccountTypeMap.Wechat,
            },
          });
        }
      },
      onError(err) {
        console.log(err);
        mutate();
      },
    },
  );

  const toView = (url: string) => {
    sockect?.sendMessage({
      cmd: "open_url",
      data: {
        url: url,
      },
    });
  };
  return (
    <Form
      form={form}
      initialValues={{ policy: true }}
      className="flex flex-col items-center space-y-5"
    >
      <div className="relative overflow-hidden">
        <div className="border">
          <Spin spinning={isLoading}>
            <div className="w-[240px] h-[240px] ">
              <img
                className={clsx(
                  "w-full h-full object-contain",
                  data?.qrCodeUrl ? "" : "hidden",
                )}
                src={data?.qrCodeUrl}
                alt="wechat_qrcode"
              />
            </div>
          </Spin>
        </div>
        {!policy && (
          <div className="absolute inset-0 w-full h-full flex flex-col  items-center justify-center text-center">
            <span> 请先同意并勾选</span>
            <span>隐私政策和探域网络服务条款</span>
          </div>
        )}
      </div>
      <Form.Item>
        <div className="mt-auto space-x-3 text-center">
          <Form.Item noStyle name="policy" valuePropName="checked">
            <Checkbox />
          </Form.Item>
          <span className="text-14 text-black-5">
            我已阅读并同意{" "}
            <Button
              type="link"
              className="h-auto p-0"
              onClick={() => toView(import.meta.env.VITE_PRIVACY_POLICY)}
            >
              《隐私政策》
            </Button>
            与
            <Button
              type="link"
              className="h-auto p-0"
              onClick={() => toView(import.meta.env.VITE_NETWORK_SERVICE)}
            >
              《探域网络服务条款》
            </Button>
          </span>
        </div>
      </Form.Item>
    </Form>
  );
};

export default WechatLogin;
