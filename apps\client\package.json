{"name": "client", "private": true, "version": "*******", "type": "module", "scripts": {"dev": "vite", "lint": "biome check --write --no-errors-on-unmatched", "build:test": "tsc && vite build --mode development && NODE_ENV=test node ./scripts/upload.cjs", "build:prod": "tsc && vite build && NODE_ENV=production node ./scripts/upload.cjs", "preview": "vite preview"}, "dependencies": {"@lexical/link": "^0.24.0", "@lexical/markdown": "^0.24.0", "@lexical/react": "^0.24.0", "@lexical/utils": "^0.24.0", "@mantine/hooks": "^7.17.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-tabs": "^1.1.2", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-virtual": "^3.11.2", "@workspace/business": "workspace:*", "@workspace/design-token": "workspace:*", "@workspace/types": "workspace:*", "@workspace/ui": "workspace:*", "antd": "^5.24.2", "axios": "^1.7.9", "clsx": "^2.1.1", "core-js": "^3.43.0", "dayjs": "^1.11.13", "framer-motion": "^12.4.0", "lexical": "^0.24.0", "lodash-es": "^4.17.21", "loglevel": "^1.9.2", "nanoid": "^5.0.9", "qs": "^6.13.1", "rc-textarea": "^1.10.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router": "^7.0.2", "react-use": "^17.6.0", "sonner": "^1.7.4", "swr": "^2.2.5", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@iconify-json/ant-design": "^1.2.3", "@iconify-json/icon-park-outline": "^1.2.1", "@iconify-json/icon-park-solid": "^1.2.1", "@iconify-json/lucide": "^1.2.29", "@svgr/core": "^8.1.0", "@svgr/plugin-jsx": "^8.1.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.24", "@types/qs": "^6.9.17", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "ali-oss": "^6.22.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "unplugin-icons": "^0.21.0", "vite": "^6.0.1", "vite-plugin-ali-oss": "^2.1.0", "vite-plugin-svgr": "^4.3.0"}}