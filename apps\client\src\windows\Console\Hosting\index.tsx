import useConfig from "@/business/hooks/useConfig";
import { useLastApp } from "@/business/hooks/useLastApp";
import useServiceAccount from "@/business/hooks/useServiceAccount";
import { AppEnum, platformClientMap } from "@/constant/client";
import {
  type MessageToHostingServiceAccount,
  WsCmdSend,
} from "@/constant/protocol/index";
import { Platform } from "@workspace/business";
import { useDebounce } from "@workspace/ui";
import { Button, Switch } from "antd";
import { useEffect, useMemo } from "react";
import { toast } from "sonner";
import PlatformAccountMain from "./components/PlatformAccountMain";
import SQSButton from "./components/SQSButton";

export const Component: React.FC = () => {
  const { socket, store } = useServiceAccount();
  const { store: configStore } = useConfig();
  const { asyncUpdate } = useLastApp();

  const accountNums = useMemo(() => {
    return Object.values(store.accountMap).reduce(
      (acc, cur) => acc + cur?.length || 0,
      0,
    );
  }, [store.accountMap]);

  const handleHosting = useDebounce(() => {
    if (!accountNums) {
      toast.warning("没有需要托管的账号");
      return;
    }
    const data = Object.entries(store.accountMap).reduce(
      (acc, [key, value]) => {
        let values = value
          .filter(
            (item) => !item.ifLogin && item.versionSuit && item.status === 0,
          )
          .map((item) => item.sellerAccountForBackend);
        if (Number(key) === Platform.JD) {
          values = value
            .filter(
              (item) => !item.ifLogin && item.versionSuit && item.status === 0,
            )
            .map((item) => `${item.sellerAccountForBackend}:0`); // 京东平台特殊标识
        }
        return Object.assign(acc, {
          [platformClientMap[Number(key) as keyof typeof platformClientMap]
            .hostingKey]: values,
        });
      },
      {} as MessageToHostingServiceAccount["data"],
    );
    const count = Object.values(data).flatMap((list) => list.length);
    if (count) {
      const hostingAccountMap = Object.values(data)
        .flat()
        .reduce((acc, cur) => Object.assign(acc, { [cur]: true }), {});
      store.setHostingAccountMap(hostingAccountMap);
      socket?.sendMessage({
        cmd: WsCmdSend.HostingServiceAccount,
        data,
      });
    } else {
      toast.warning("没有需要托管的账号");
    }
  }, 500);

  const handleSwitch = useDebounce((checked: boolean) => {
    socket?.sendMessage({
      cmd: WsCmdSend.SetAutoBoot,
      data: {
        isAutoStart: checked,
      },
    });
  }, 500);

  useEffect(() => {
    if (configStore.others.version) {
      configStore.getAllPlatformSupportedClientsInfo();
    }
  }, [
    configStore?.getAllPlatformSupportedClientsInfo,
    configStore?.others.version,
  ]);

  useEffect(() => {
    if (configStore.others.version) {
      asyncUpdate({
        app: AppEnum.SQS,
        clientVersionNo: configStore.others.version as string,
      });
    }
  }, [asyncUpdate, configStore?.others.version]);

  return (
    <div className="flex flex-col h-screen">
      <div className="py-2 px-3 flex">
        <SQSButton />
        <Button
          type="primary"
          className="ml-auto"
          onClick={handleHosting}
          disabled={!accountNums}
        >
          一键托管
        </Button>
      </div>
      <div className="p-3 bg-gray-2 ov overflow-auto flex-1 border-t">
        <PlatformAccountMain />
      </div>

      <div className="flex items-center px-6 py-4 mt-auto bg-white justify-between  border-t">
        <span className="text-14 text-black-3">
          <span className="after:content-[':'] after:mr-2 ">版本</span>
          {configStore.others.version}
        </span>
        <div className="flex items-center text-14  text-black-3 gap-2">
          <span>开机自动启动</span>
          <Switch
            key={Number(configStore.others.isAutoBootStart)}
            defaultChecked={configStore.others.isAutoBootStart}
            onChange={handleSwitch}
          />
        </div>
      </div>
    </div>
  );
};

Component.displayName = "Hosting";
