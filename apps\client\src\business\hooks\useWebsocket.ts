import type WebSocketManager from "@/class/Ws";
import type { WsMessage } from "@/constant/protocol/index";
import { useMemo } from "react";
import { createStore, useStore } from "zustand";
interface WebSocketStore {
  socket: WebSocketManager | null;
  setWs: (socket: WebSocketManager | null) => void;
  readyState: number;
  setReadyState: (state: number) => void;
}

export const websocketStore = createStore<WebSocketStore>((set) => ({
  socket: null,
  setWs: (socket) => set({ socket }),
  readyState: 0,
  setReadyState: (state: number) => set({ readyState: state }),
}));

const useWebSocket = () => {
  const { socket, readyState } = useStore(websocketStore);

  const _socket = useMemo(() => {
    if (!socket) return;
    return {
      sendMessage: (payload: WsMessage) => {
        socket?.sendMessage(payload);
      },
      on: (...args: Parameters<WebSocketManager["on"]>) => socket?.on(...args),
      readyState,
    };
  }, [socket, readyState]);

  return _socket;
};

export default useWebSocket;
