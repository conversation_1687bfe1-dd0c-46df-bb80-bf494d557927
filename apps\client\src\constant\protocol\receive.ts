import type { AgentMessageType } from "@/network/api/copilot";
import type { ProductChangeType } from "@/network/api/product";
import type { AppTypeEnum } from "../client";
import type { SupportPlatform } from "../platform";
import type { WsProtocol } from "./base";

export const WsCmdReceive = {
  // init
  init: "init",
  /** 客户端基础配置 */
  BaseConfig: "basic_config",
  /** 设置快捷键返回 */
  SetReminderShortcutACK: "set_reminder_shortcut_callback",
  /** 设置卡片快捷键返回 */
  SetCardMsgShortcutACK: "set_card_msg_shortcut_callback",
  /** 设置单条信息快捷键返回 */
  SetSingleMsgShortcutACK: "set_single_msg_shortcut_callback",
  /** 客户端智能体配置 */
  AgentConfig: "agent_page_config",
  /** 应用设置  */
  AppConfig: "app_page_config",
  /** 智能体快捷键配置 */
  AgentShortcutConfig: "quick_send_shortcut",
  /** 智能体快捷键配置回调 */
  AgentShortcutConfigSetCallback: "set_quick_send_shortcut_callback",
  /** 聚合接待配置 */
  ReceptionConfig: "aggregated_list_config",
  /** 客户端页面配置 */
  ClientConfig: "client_page_config",
  /** copilot消息 */
  AgentMsg: "agent_msg",
  /** 客户端杂项配置 */
  ClientOthersConfig: "client_config",
  /** 自动托管 */
  AutoHosting: "auto_login_switch",
  /** 客服帐号列表 */
  ServiceAccountList: "account_list",
  /** 刷新客服帐号列表 */
  RefreshServiceAccountList: "refresh_account_list",
  /** 路由跳转到设置页面 */
  NavigateToSetting: "jump_to_setting_page",
  /** 本地平台客户端信息 */
  LocalPlatformClientsInfo: "local_platformform_list",
  /** 选中文件目录回调 */
  SelectedDirectory: "selected_directory",
  /** 文件保存路径 */
  InstallDirectory: "file_save_path",
  /** 文件下载进度 */
  DownloadProgress: "download_progress_callback",
  /** 删除本地客户端回调 */
  DeleteLocalFilesACK: "filesystem_delete_all_result",
  /** 快捷发送触发 */
  QuickSendTrigger: "quick_send_trigger",
  /** 快捷键列表返回回调 */
  QuickSendShortcut: "quick_send_shortcut",
  /* 焦点商品变更回调 */
  ProductChange: "product_change_msg",
  /** 账号自动登录 */
  AutoLogin: "tanyu_account_autoLogin",
  /** 客服帐号切换 */
  ServiceAccountChange: "change_current_account",
  /** 买家切换 */
  BuyerChange: "change_current_buyer",
  /** 聚合接待会话列表 */
  ReceptionList: "get_aggregate_list",
  /** 聚合接待新会话 */
  NewReceptionConversation: "add_aggregate_item",
  /** 聚合接待会话更新 */
  ReceptionConversationUpdate: "update_aggregate_item",
  /** 聚合接待会话删除 */
  ReceptionConversationRemove: "del_aggregate_item",
  /** 聚合接待打开会话快捷键更新 */
  ReceptionConversationShortcutUpdateACK:
    "set_aggregated_list_open_conversation_shortcut_callback",
  /** 聚合接待打开等待时间最长会话 */
  ReceptionOpenLongestWaitingTimeConversation:
    "aggregated_list_open_conversation",
  Logout: "tanyu_account_logout",
  /** 托管成功后，设置token */
  UpdateServerAccountToken: "send_account_token",
  /**  */
  SwitchReceptionShotcutForModeACK:
    "set_aggregated_list_alone_shortcut_callback",

  /** 催单触发 */
  ReminderTrigger: "reminder_trigger",
  /** 开片发送快捷键触发 */
  CardMsgTrigger: "card_msg_trigger",
  /** 单条信息快捷键触发 */
  SingleMsgTrigger: "single_msg_trigger",
  /** 帐号列表 */
  RememberAccountList: "remember_account_list",
} as const;

/** copilot消息 */
export type CopilotMessage = WsProtocol<
  typeof WsCmdReceive.AgentMsg,
  AgentMessageType
>;

/** 客户端基础信息消息 */
export type InitMessage = WsProtocol<
  typeof WsCmdReceive.init,
  {
    clientConfig: {
      //开机自启动状态
      isAutoStart: true;
      isDyRobotExist: boolean;
      isKsRobotExist: boolean;
      version: string;
    };
    // 平台软件信息
    platformSoftwareInfo: {
      platformType: SupportPlatform;
      isExist: true;
      version: string;
      is64Bit: boolean;
    }[];
    pluginInfo: {
      isExist: boolean;
      pluginType: AppTypeEnum;
      version: string;
    }[];
  }
>;

/** 客户端配置信息 */
export type ClientOthersConfigMessage = WsProtocol<
  typeof WsCmdReceive.ClientOthersConfig,
  {
    isAutoStart: true;
    isDyRobotExist: boolean;
    isKsRobotExist: boolean;
    version: string;
  }
>;

/** 基础设置 */
export type BaseConfigMessage = WsProtocol<
  typeof WsCmdReceive.BaseConfig,
  {
    /** 掉线通知开关 */
    offlineNotifySwitch: boolean;
    /** 更新通知开关 */
    updateNotifySwitch: boolean;
    /** 自动托管开关 */
    autoLoginSwitch: boolean;
    /** 催单快捷键 */
    reminderShortcut: string;
    /** 开片发送快捷键 */
    cardMsgShortcut: string;
    /** 单条信息快捷键 */
    singleMsgShortcut: string;
  }
>;

/** 设置快捷键返回 */
export type SetReminderShortcutACKMessage = WsProtocol<
  typeof WsCmdReceive.SetReminderShortcutACK,
  {
    isSuccess: boolean;
  }
>;
/** 设置卡片快捷键返回 */
export type SetCardMsgShortcutACKMessage = WsProtocol<
  typeof WsCmdReceive.SetCardMsgShortcutACK,
  {
    isSuccess: boolean;
  }
>;
/** 设置单条信息快捷键返回 */
export type SetSingleMsgShortcutACKMessage = WsProtocol<
  typeof WsCmdReceive.SetSingleMsgShortcutACK,
  {
    isSuccess: boolean;
  }
>;

/** 智能体设置 */
export type AgentConfigMessage = WsProtocol<
  typeof WsCmdReceive.AgentConfig,
  {
    oldSidebarSwitch: boolean;
  }
>;

/** 智能体设置 */
export type AppConfigMessage = WsProtocol<
  typeof WsCmdReceive.AppConfig,
  {
    fileSavePath: string;
    pluginInfo: Array<{
      is64Bit: boolean;
      pluginType: AppTypeEnum;
      pluginVersion: string;
      pluginPath: string;
    }>;
  }
>;

/** 智能体快捷键设置 */
export type AgentShortcutConfigMessage = WsProtocol<
  typeof WsCmdReceive.AgentShortcutConfig,
  {
    shortcutList: { number: number; shortcut: string }[];
  }
>;

/** 智能体快捷键设置回调 */
export type AgentShortcutConfigSetCallbackMessage = WsProtocol<
  typeof WsCmdReceive.AgentShortcutConfigSetCallback,
  {
    shortcutList: { number: number; shortcut: string }[];
  }
>;

/** 客户端页面配置 */
export type ClientConfigMessage = WsProtocol<
  typeof WsCmdReceive.ClientConfig,
  {
    fileSavePath: string;
    selectedPlatformVersion: {
      platformVersion: string;
      is64Bit: boolean;
      platformType: SupportPlatform;
    }[];
  }
>;

export type ReceptionConfigMessage = WsProtocol<
  typeof WsCmdReceive.ReceptionConfig,
  {
    /** 聚合接待列表展示开关 */
    aggregatedListSwitch: boolean;
    /** 聚合列表是否单独窗口，true: 单独窗口; false:吸附 */
    aggregatedListAloneStatus: boolean;
    /** 打开会话快捷方式 */
    aggregatedListOpenConversationShortcut: string;
    /** 切换模式快捷方式 */
    aggregatedListAloneShortcut: string;
  }
>;

/** 自动登录开关消息 */
export type AutoHostingMessage = WsProtocol<
  typeof WsCmdReceive.AutoHosting,
  {
    isSwitchOn: boolean;
  }
>;

/** 客服帐号信息 */
export type ServiceAccountLocalInfo = {
  platformType: SupportPlatform;
  /** 客服帐号 */
  account: string;
  accountId?: string;
  /** 接待中心是否已打开 */
  receptionIsOpen: boolean;
  /** 是否已托管 */
  ifLogin: boolean;
  processId: number;
  token: string;
  /** 平台版本是否适配 */
  versionSuit: boolean;
  /** 展示名称 */
  showAccount: string;
  shopId: string;
  platformAccountId: number;
};

/** 客服帐号列表消息 */
export type ServiceAccountListMessage = WsProtocol<
  typeof WsCmdReceive.ServiceAccountList,
  {
    jdAccountList: ServiceAccountLocalInfo[];
    ksAccountList: ServiceAccountLocalInfo[];
    pddAccountList: ServiceAccountLocalInfo[];
    dyAccountList: ServiceAccountLocalInfo[];
    dewuAccountList: ServiceAccountLocalInfo[];
    qnAccountList: ServiceAccountLocalInfo[];
  }
>;

/** 刷新客服帐号列表 */
export type RefreshServiceAccountListMessage = WsProtocol<
  typeof WsCmdReceive.RefreshServiceAccountList,
  {
    jdAccountList: ServiceAccountLocalInfo[];
    ksAccountList: ServiceAccountLocalInfo[];
    pddAccountList: ServiceAccountLocalInfo[];
    dyAccountList: ServiceAccountLocalInfo[];
    dewuAccountList: ServiceAccountLocalInfo[];
    qnAccountList: ServiceAccountLocalInfo[];
  }
>;

/** 路由到设置页面消息 */
export type NavigateToSettingMessage = WsProtocol<
  typeof WsCmdReceive.NavigateToSetting,
  undefined
>;

/** 本地平台客户端信息 */
export type LocalPlatformClientsInfoMessage = WsProtocol<
  typeof WsCmdReceive.LocalPlatformClientsInfo,
  {
    platformInfo: {
      platformList: {
        platformPath: string;
        platformVersion: string;
        is64Bit: boolean;
      }[];
      platformType: SupportPlatform;
    }[];
  }
>;

/** 选择的目录 */
export type SelectedDirectoryMessage = WsProtocol<
  typeof WsCmdReceive.SelectedDirectory,
  {
    directory: string;
  }
>;

/** 安装目录 */
export type InstallDirectoryMessage = WsProtocol<
  typeof WsCmdReceive.InstallDirectory,
  {
    path: string;
  }
>;

export const DownloadStatusMap = {
  NotScheduled: 0,
  Deleted: 1,
  Paused: 2,
  Waiting: 3,
  Downloading: 4,
  Failed: 5,
  Cancelled: 6,
  Completed: 7,
  Installing: 8,
  InstallFailed: 9,
  InstallSuccess: 10,
} as const;

/** 0:未下载 1:已删除 2:已暂停 3:等待中 4: 下载中 5: 下载失败 6: 下载取消 7: 下载完成 8: 安装中 9: 安装失败 10: 安装成功*/
export type DownloadStatus =
  (typeof DownloadStatusMap)[keyof typeof DownloadStatusMap];

/** 文件下载进度 */
export type DownloadProgressMessage = WsProtocol<
  typeof WsCmdReceive.DownloadProgress,
  {
    fileList: {
      /** 文件id，用于标识一个文件 */
      id: string;
      /** 文件名 */
      fileName: string;
      /** 文件下载地址 */
      fileUrl: string;
      /** 总字节数 */
      fileSize: number;
      /** 已下载字节数 */
      byteReceived: number;
      /** 下载状态 */
      status: DownloadStatus;
    }[];
  }
>;

/** 删除本地文件回调 */
export type DleteLocalFilesACKMessage = WsProtocol<
  typeof WsCmdReceive.DeleteLocalFilesACK,
  {
    path: string;
    isSuccess: boolean;
  }
>;

/** 快捷发送快捷键触发消息 */
export type QuickSendTriggerMessage = WsProtocol<
  typeof WsCmdReceive.QuickSendTrigger,
  {
    number: number;
  }
>;

export type ShortcutListItem = {
  number: number;
  shortcut: string;
};

export type QuickSendShortcutMessage = WsProtocol<
  typeof WsCmdReceive.QuickSendShortcut,
  {
    shortcutList: ShortcutListItem[];
  }
>;

export type ProductChangeMessage = WsProtocol<
  typeof WsCmdReceive.ProductChange,
  ProductChangeType
>;

export const AccountTypeMap = {
  Wechat: 1,
  Password: 0,
} as const;

/** 账号自动登录 */
export type AutoLoginMessage = WsProtocol<
  typeof WsCmdReceive.AutoLogin,
  {
    userName: string;
    cookie: string;
    showUserName: string;
    accountType: (typeof AccountTypeMap)[keyof typeof AccountTypeMap];
  }
>;

/** 客服帐号切换 */
export type ServiceAccountChangeMessage = WsProtocol<
  typeof WsCmdReceive.ServiceAccountChange,
  {
    platformType: SupportPlatform;
    currentAccount: {
      platform: SupportPlatform;
      account: string;
      currentBuyerNick: string;
      ifLogin: boolean;
      receptionIsOpen: boolean;
      token: string;
      display: string;
      showAccount: string;
      showCurrentBuyerNick: string;
    };
  }
>;

/** 买家切换 */
export type BuyerChangeMessage = WsProtocol<
  typeof WsCmdReceive.BuyerChange,
  {
    platformType: SupportPlatform;
    changeBuyer: {
      platform: SupportPlatform;
      account: string;
      currentBuyerNick: string;
      ifLogin: boolean;
      receptionIsOpen: boolean;
      token: string;
      display: string;
      showAccount: string;
      showCurrentBuyerNick: string;
    };
  }
>;

export type Conversation = {
  appCid: string;
  shopId: string;
  shopName: string;
  platformType: SupportPlatform;
  account: string;
  sellerNick: string;
  buyerNick: string;
  buyerId: string;
  msgTime: number;
  readTime: number;
  msgType: number;
  showContent: string;
  msgContent: string;
  /** 是否显示读秒 */
  isRead: boolean;
  /** 是否亮灯 */
  isLight: boolean;
  processId: string;
  sessionKey: string;
  /** 是否已回复 */
  isReply: boolean;
  platformSessionId: string;
  starId: string;
  starType: string;
  starTime: string;
};

/** 聚合接待会话列表消息 */
export type ReceptionListMessage = WsProtocol<
  typeof WsCmdReceive.ReceptionList,
  Conversation[]
>;

/** 聚合接待新会话 */
export type NewReceptionConversationMessage = WsProtocol<
  typeof WsCmdReceive.NewReceptionConversation,
  Conversation
>;

/** 聚合接待会话更新 */
export type ReceptionConversationUpdateMessage = WsProtocol<
  typeof WsCmdReceive.ReceptionConversationUpdate,
  {
    isLight: boolean;
    isRead: boolean;
    sessionKey: string;
    updateType: "RL" | "L";
  }
>;

/** 聚合接待会话移除 */
export type ReceptionConversationRemoveMessage = WsProtocol<
  typeof WsCmdReceive.ReceptionConversationRemove,
  {
    sessionKey: string;
    shopId: string;
    account: string;
  }
>;

/** 聚合接待打开会话快捷键更新 */
export type ReceptionConversationShortcutUpdateACKMessage = WsProtocol<
  typeof WsCmdReceive.ReceptionConversationShortcutUpdateACK,
  {
    isSuccess: boolean;
  }
>;

/** 聚合接待打开等待时间最长会话 */
export type ReceptionOpenLongestWaitingTimeConversationMessage = WsProtocol<
  typeof WsCmdReceive.ReceptionOpenLongestWaitingTimeConversation,
  undefined
>;
/**  */
export type SwitchReceptionShotcutForModeACKMessage = WsProtocol<
  typeof WsCmdReceive.SwitchReceptionShotcutForModeACK,
  { isSuccess: boolean }
>;
/** 退出登录 */
export type LogoutMessage = WsProtocol<typeof WsCmdReceive.Logout, undefined>;

/** 更新客服帐号token */
export type UpdateServerAccountTokenMessage = WsProtocol<
  typeof WsCmdReceive.UpdateServerAccountToken,
  {
    account: string;
    platformType: SupportPlatform;
    shopId: string;
    token: string;
  }
>;

/** 催单触发 */
export type ReminderTriggerMessage = WsProtocol<
  typeof WsCmdReceive.ReminderTrigger,
  undefined
>;
/** 开片发送快捷键触发 */
export type CardMsgTriggerMessage = WsProtocol<
  typeof WsCmdReceive.CardMsgTrigger,
  { number: number }
>;
/** 单条信息快捷键触发 */
export type SingleMsgTriggerMessage = WsProtocol<
  typeof WsCmdReceive.SingleMsgTrigger,
  { number: number }
>;
/** 帐号列表 */
export type RememberAccountListMessage = WsProtocol<
  typeof WsCmdReceive.RememberAccountList,
  { password: string; userName: string }[]
>;

export type WsReceiveMessage =
  | CopilotMessage
  | InitMessage
  | ClientOthersConfigMessage
  | AutoHostingMessage
  | ServiceAccountListMessage
  | BaseConfigMessage
  | SetReminderShortcutACKMessage
  | SetCardMsgShortcutACKMessage
  | SetSingleMsgShortcutACKMessage
  | AgentConfigMessage
  | AppConfigMessage
  | AgentShortcutConfigMessage
  | AgentShortcutConfigSetCallbackMessage
  | ReceptionConfigMessage
  | NavigateToSettingMessage
  | LocalPlatformClientsInfoMessage
  | SelectedDirectoryMessage
  | InstallDirectoryMessage
  | DownloadProgressMessage
  | DleteLocalFilesACKMessage
  | ClientConfigMessage
  | QuickSendTriggerMessage
  | QuickSendShortcutMessage
  | ProductChangeMessage
  | AutoLoginMessage
  | ServiceAccountChangeMessage
  | BuyerChangeMessage
  | ReceptionListMessage
  | NewReceptionConversationMessage
  | ReceptionConversationRemoveMessage
  | ReceptionConversationUpdateMessage
  | LogoutMessage
  | UpdateServerAccountTokenMessage
  | ReceptionConversationShortcutUpdateACKMessage
  | SwitchReceptionShotcutForModeACKMessage
  | ReceptionOpenLongestWaitingTimeConversationMessage
  | RefreshServiceAccountListMessage
  | ReminderTriggerMessage
  | RememberAccountListMessage
  | CardMsgTriggerMessage
  | SingleMsgTriggerMessage;
