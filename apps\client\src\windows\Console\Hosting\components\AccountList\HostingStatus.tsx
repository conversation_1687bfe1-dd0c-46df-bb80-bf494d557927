import type { ServiceAccount } from "@/business/components/ServiceccountProvider/store";
import { platformClientMap } from "@/constant/client";
import CloseCircleOutlined from "~icons/ant-design/close-circle-outlined";
import Loading3QuartersOutlined from "~icons/ant-design/loading-3-quarters-outlined";
import WifiOutlined from "~icons/icon-park-outline/wifi";

const StatusTypeMap: Record<
  string,
  {
    key: string;
    render: (props: { text?: string }) => JSX.Element;
  }
> = {
  Unmanaged: {
    key: "Unmanaged",
    render: ({ text = "未托管" }: { text?: string }) => (
      <span className="text-orange-7 flex items-center gap-2">
        <CloseCircleOutlined />
        <span>{text}</span>
      </span>
    ),
  },
  Unauthorized: {
    key: "Unauthorized",
    render: ({ text = "未授权" }: { text?: string }) => (
      <span className="text-red-7">{text}</span>
    ),
  },
  Managed: {
    key: "Managed",
    render: ({ text = "已托管" }: { text?: string }) => (
      <span className="text-green-7 flex items-center gap-2">
        <WifiOutlined />
        <span>{text}</span>
      </span>
    ),
  },
  Hosting: {
    key: "Hosting",
    render: ({ text = "托管中" }: { text?: string }) => (
      <span className="flex items-center gap-2 text-orange-7">
        <Loading3QuartersOutlined className="animate-spin" />
        <span>{text}</span>
      </span>
    ),
  },
  NotSupported: {
    key: "NotSupported",
    render: ({ text = "当前版本暂未支持" }: { text?: string }) => (
      <span className="text-red-7 flex items-center gap-2">
        <CloseCircleOutlined />
        <span>{text}</span>
      </span>
    ),
  },
  ChatWindowNotOpen: {
    key: "ChatWindowNotOpen",
    render: ({ text = "未打开聊天窗口" }: { text?: string }) => (
      <span className="text-red-7 flex items-center gap-2">
        <CloseCircleOutlined />
        <span>{text}</span>
      </span>
    ),
  },
};

const HostingStatus = ({
  status,
  data,
  isHosting,
}: {
  status: number;
  data: ServiceAccount;
  isHosting: boolean;
}) => {
  let type = StatusTypeMap.Unmanaged.key;
  if (!data.versionSuit) type = StatusTypeMap.NotSupported.key;
  else if (status === 1 || status === 2) type = StatusTypeMap.Unauthorized.key;
  else if (!data.receptionIsOpen) type = StatusTypeMap.ChatWindowNotOpen.key;
  else if (isHosting) type = StatusTypeMap.Hosting.key;
  else if (data.ifLogin) type = StatusTypeMap.Managed.key;

  const Element = StatusTypeMap[type].render;
  const props =
    type === StatusTypeMap.NotSupported.key
      ? {
          text: `当前${platformClientMap[data.platformType as keyof typeof platformClientMap].name}版本暂未支持`,
        }
      : {};

  return <Element {...props} />;
};

export default HostingStatus;
