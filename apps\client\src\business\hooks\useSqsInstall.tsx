import { AppEnum, AppTypeEnum } from "@/constant/client";
import {
  AccountTypeMap,
  DownloadStatusMap,
  WsCmdSend,
} from "@/constant/protocol";
import useRemote from "@/hooks/useRemote";
import { getWxGroupList } from "@/network/api/account";
import { useDebounce } from "@workspace/ui";
import { useCallback, useMemo } from "react";
import { useAccount } from "./useAccount";
import useConfig from "./useConfig";
import useFileDownload from "./useFileDownload";
import { useLastApp } from "./useLastApp";
import useServiceAccount from "./useServiceAccount";
import useTask from "./useTask";

export const useSqsInstall = () => {
  const { appMap } = useLastApp();
  const { socket } = useServiceAccount();
  const { files } = useFileDownload();
  const { tasks, addTasks, deletTask } = useTask();
  const { data: account, accountType } = useAccount();
  const { store: configStore } = useConfig();

  const appInfo = useMemo(() => {
    return appMap[AppEnum.SQS];
  }, [appMap]);

  const sqsTaskId = useMemo(
    () => `${AppTypeEnum.SQS}-${appInfo?.versionNo}`,
    [appInfo?.versionNo],
  );

  const currentSqsPluginInfo = useMemo(
    () =>
      configStore.appConfig.pluginInfo?.find(
        (someItem) => someItem.pluginType === AppTypeEnum.SQS,
      ),
    [configStore.appConfig.pluginInfo],
  );

  const handleUnInstallClient = useCallback(() => {
    const path = currentSqsPluginInfo?.pluginPath as string;
    if (path) {
      addTasks([path]);
      socket?.sendMessage({
        cmd: WsCmdSend.DeleteLocalPlatformClient,
        data: {
          path,
        },
      });
    }
  }, [socket, addTasks, currentSqsPluginInfo?.pluginPath]);

  const installClient = useCallback(() => {
    const payload = {
      id: sqsTaskId,
      fileUrl: appInfo?.rarObjectUrl as string,
      extra: appInfo?.objectUrl as string,
      isPlatformSoftware: false,
      isPlugin: true,
      is64Bit: appInfo?.rarObjectBit === "x64",
      platformVersion: appInfo?.versionNo,
      pluginType: AppTypeEnum.SQS,
    };
    socket?.sendMessage({
      cmd: WsCmdSend.DownloadFile,
      data: payload,
    });
  }, [socket, sqsTaskId, appInfo]);

  const cancelInstall = useDebounce(() => {
    socket?.sendMessage({
      cmd: WsCmdSend.CancelDownload,
      data: {
        id: sqsTaskId,
      },
    });
  }, 500);

  const handleInstallClient = useCallback(() => {
    addTasks([sqsTaskId]);
    installClient();
  }, [sqsTaskId, addTasks, installClient]);

  const handleCancelInstallClient = () => {
    deletTask(sqsTaskId);
    cancelInstall();
  };

  const isInstalling = tasks.has(sqsTaskId);

  const isDeleting = useMemo(
    () =>
      currentSqsPluginInfo?.pluginPath &&
      tasks.has(currentSqsPluginInfo?.pluginPath),
    [currentSqsPluginInfo?.pluginPath, tasks],
  );

  const { data: groupList } = useRemote(
    accountType === AccountTypeMap.Wechat
      ? [getWxGroupList.name, accountType]
      : null,
    () => getWxGroupList(),
  );

  let type = "default";
  if (
    (accountType === AccountTypeMap.Password && !account?.openSqs) ||
    (accountType === AccountTypeMap.Wechat &&
      groupList?.every((everyItem) => !everyItem.openSqs))
  ) {
    type = "disabled";
  } else if (
    !isInstalling &&
    !isDeleting &&
    (!files[sqsTaskId] ||
      ([DownloadStatusMap.InstallSuccess] as number[]).includes(
        files[sqsTaskId]?.status,
      ))
  ) {
    type = "install";
  } else if (
    isInstalling &&
    files[sqsTaskId]?.status !== DownloadStatusMap.Installing
  ) {
    type = "downloading";
  } else if (
    isInstalling &&
    files[sqsTaskId]?.status === DownloadStatusMap.Installing
  ) {
    type = "installing";
  } else if (files[sqsTaskId]?.status === DownloadStatusMap.InstallFailed) {
    type = "failed";
  } else if (isDeleting) {
    type = "deleting";
  }

  return {
    type,
    handleInstallClient,
    handleCancelInstallClient,
    handleUnInstallClient,
  };
};
