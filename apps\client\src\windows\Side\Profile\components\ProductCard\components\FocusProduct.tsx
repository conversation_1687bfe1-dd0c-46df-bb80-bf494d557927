import type { IProductItem } from "@/network/api/product";
import clsx from "clsx";
import { useEffect, useState } from "react";
import Seassion from "../../Seassion";
import ProductDetail from "./ProductDetail";

interface FocusProductProps {
  buyerNick: string;
  className?: string;
  data: IProductItem[];
}

const FocusProduct = ({ buyerNick, className, data }: FocusProductProps) => {
  const [currentProduct, setCurrentProduct] = useState<IProductItem>(data?.[0]);

  const changeCurrentProduct = (index: number) => {
    setCurrentProduct(data[index]);
  };

  useEffect(() => {
    if (data) {
      setCurrentProduct(data[0]);
    }
  }, [data]);

  return (
    <Seassion title="焦点商品" className={className}>
      <div className="flex items-center overflow-x-auto overflow-y-hidden">
        {data.map((product, index) => (
          <div
            key={product.productId}
            className={clsx(
              "py-[6px] px-1 border-solid shrink-0",
              currentProduct?.productId === product.productId
                ? "border border-b-transparent border-gray-4 bg-gray-2 rounded-tl-[2px] rounded-tr-[2px]"
                : "border-none",
            )}
            onClick={() => changeCurrentProduct(index)}
            onKeyDown={() => {}}
          >
            <img
              src={product.pic}
              alt=""
              className="w-[28px] h-[28px] rounded-[2px] object-cover block"
            />
          </div>
        ))}
      </div>
      <ProductDetail data={currentProduct} buyerNick={buyerNick} />
    </Seassion>
  );
};

export default FocusProduct;
