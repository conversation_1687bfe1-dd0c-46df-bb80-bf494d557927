import { Skeleton } from "@workspace/ui";

interface SkeletonWrapperProps {
  children: React.ReactNode;
  loading: boolean;
}

const Header = ({ children, loading }: SkeletonWrapperProps) => {
  if (!loading) return <>{children}</>;
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="w-3/4 h-4 mr-auto" />
    </div>
  );
};

const Content = ({ children, loading }: SkeletonWrapperProps) => {
  if (!loading) return <>{children}</>;
  return (
    <div className="flex flex-col space-y-3">
      <div className="space-y-2">
        <Skeleton className="h-4 w-2/3" />
      </div>
      <Skeleton className="h-[80px] w-full rounded-xl" />
    </div>
  );
};

const Footer = ({ children, loading }: SkeletonWrapperProps) => {
  if (!loading) return <>{children}</>;
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="w-20 h-4 mr-auto" />
      <Skeleton className="w-16 h-4" />
      <Skeleton className="w-16 h-4" />
    </div>
  );
};

const SkeletonWrapper = {
  Header,
  Footer,
  Content,
};

export default SkeletonWrapper;
