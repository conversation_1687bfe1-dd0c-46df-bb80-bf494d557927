import { Platform, PlatformMap } from "@workspace/business";

export type SupportPlatform = Exclude<
  Platform,
  Platform.ALI1688 | Platform.CHANNELS | Platform.YOUZAN | Platform.DEWU
>;

export const SupportedPlatforms = Object.fromEntries(
  Object.entries(PlatformMap).filter(
    ([key]) =>
      ![
        Platform.ALI1688,
        Platform.CHANNELS,
        Platform.YOUZAN,
        Platform.DEWU,
      ].includes(Number(key)),
  ),
) as Record<SupportPlatform, (typeof PlatformMap)[SupportPlatform]>;
