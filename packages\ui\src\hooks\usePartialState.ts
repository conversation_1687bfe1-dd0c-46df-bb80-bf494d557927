import { useCallback, useState } from "react";

const usePartialState = <T extends Record<string, unknown>>(
  initState: T,
): [T, (newState: Partial<T>) => void] => {
  const [state, setState] = useState(initState);
  const updateState = useCallback((newState: Partial<T>) => {
    setState((prev) => ({ ...prev, ...newState }));
  }, []);
  return [state, updateState];
};

export default usePartialState;
