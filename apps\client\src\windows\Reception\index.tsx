import emptyPlaceholder from "@/assets/images/placeholder/empty-1.png";
import {
  TabTypeMap,
  sortedMap,
} from "@/business/components/ReceptionProvider/store";
import useReception from "@/business/hooks/useReception";
import {
  type Conversation,
  WsCmdReceive,
  WsCmdSend,
} from "@/constant/protocol";
import {
  batchDeleteStarMark,
  deleteStarMark,
  saveStarMark,
} from "@/network/api/reception";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useDebounce } from "@workspace/ui";
import { Popconfirm } from "antd";
import clsx from "clsx";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import ClearOutlined from "~icons/ant-design/clear-outlined";
import UpOutlined from "~icons/ant-design/up-outlined";
import ConversationItem from "./components/ConversationItem";
import Filter, { type FilterType } from "./components/Filter";
import Sorter from "./components/Sorter";
import TabSelect from "./components/TabSelect";

type StarType = "red" | "yellow" | "blue" | "green";

export const Component: React.FC = () => {
  const [time, setTime] = useState<number>(new Date().getTime());
  const {
    store: {
      conversations,
      shortcut,
      clearConversations,
      removeConversation,
      updateConversation,
      filter,
      sorter,
      setFilter,
      setSorter,
      tabActive,
      setTabActive,
    },
    socket,
  } = useReception();

  const [fnKey, normalKey] = shortcut.openConversation?.split("+") || [];

  const timer = useRef<number>();

  const platforms = useMemo(
    () =>
      Object.entries(filter.platform)
        .filter(([, value]) => value)
        .map(([key]) => Number(key)),
    [filter],
  );

  const starList = useMemo(() => {
    //过滤平台、星标
    const filterData = conversations.filter((item) => {
      return (
        platforms.includes(item.platformType) &&
        filter.starType?.[item.starType as "red" | "yellow" | "blue" | "green"]
      );
    });

    // starTpye  starTime
    // red->yellow->blue->green
    const starPriorityMap: Record<StarType, number> = {
      red: 0,
      yellow: 1,
      blue: 2,
      green: 3,
    };

    filterData.sort((a, b) => {
      // 获取星标优先级
      const priorityA = starPriorityMap[a.starType as StarType];
      const priorityB = starPriorityMap[b.starType as StarType];

      // 按星标优先级排序
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }
      // 如果优先级相同，按时间排序（新创建的在前）
      return Number(b.starTime) - Number(a.starTime);
    });
    return filterData;
  }, [conversations, platforms, filter.starType]);

  const receptionList = useMemo(() => {
    const filtedConversations = conversations.filter(
      (item) =>
        platforms.includes(Number(item.platformType)) &&
        (item.isReply === filter.type.replyed ||
          !item.isReply === filter.type.unreplyed),
    );

    //超时等待列表
    const timeoutList: Conversation[] = [];
    //已回复列表
    const hasReplyList: Conversation[] = [];

    filtedConversations.forEach((item) => {
      if (item.isReply) {
        hasReplyList.push(item);
      } else {
        timeoutList.push(item);
      }
    });

    //已回复也根据等待时间进行排序
    const hasReplyOrderList = hasReplyList.sort(
      (a, b) => b.readTime - a.readTime,
    );

    const timeOrderList = timeoutList.sort((a, b) =>
      sorter === sortedMap.Ascending
        ? b.readTime - a.readTime
        : a.readTime - b.readTime,
    );
    return timeOrderList.concat(hasReplyOrderList);
  }, [conversations, filter, sorter, platforms]);

  const _conversations = useMemo(() => {
    const listMap = {
      [TabTypeMap.reception]: receptionList,
      [TabTypeMap.starMark]: starList,
    };
    return listMap[tabActive] ?? [];
  }, [tabActive, receptionList, starList]);

  const unreplyedNums =
    conversations?.filter((item) => !item.isReply)?.length || 0;

  const parentRef = useRef<HTMLDivElement>(null);

  const rowVirtualizer = useVirtualizer({
    count: _conversations.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 56,
    overscan: 5,
    gap: 4,
    getItemKey: (index) => _conversations[index].sessionKey,
  });

  const onOpenConversation = useCallback(
    (data: Conversation) => {
      socket?.sendMessage({
        cmd: WsCmdSend.OpenConversation,
        data: {
          shopName: data.shopName,
          currentBuyerUserId: data.buyerId || data.buyerNick,
          platformSessionId: data.platformSessionId,
          isTop: true,
          account: data.sellerNick,
          platformType: data.platformType,
          sessionKey: data.sessionKey,
          appCid: data?.appCid,
        },
      });
    },
    [socket],
  );

  const onRemoveConversation = (data: Conversation) => {
    socket?.sendMessage({
      cmd: WsCmdSend.DeleteReceptionOneConversation,
      data: {
        sessionKey: data.sessionKey,
        platformType: data.platformType,
      },
    });
    removeConversation(data);
  };

  const onStarChange = async (starType: string, data: Conversation) => {
    if (starType === "init") {
      const res = await deleteStarMark({ id: data.starId }).promise.catch(
        (error) => error,
      );
      if (res instanceof Error) return;
      if (res.data.success) {
        socket?.sendMessage({
          cmd: WsCmdSend.DelStarAggregateItem,
          data: {
            sessionKey: data.sessionKey,
            account: data.sellerNick,
            platformType: [data.platformType],
          },
        });
        updateConversation({
          starTime: data.starTime,
          starType,
          starId: data.starId,
          sessionKey: data.sessionKey,
        });
      }
    } else {
      const res = await saveStarMark({
        platform: data.platformType,
        sellerAccount: data.sellerNick,
        color: starType,
        buyerId: data.buyerId,
      }).promise.catch((error) => error);
      if (res instanceof Error) return;
      if (res.data.success) {
        const resData = res.data.data;
        //发送协议
        socket?.sendMessage({
          cmd: WsCmdSend.UpdateStarAggregateItem,
          data: {
            sessionKey: data.sessionKey,
            account: data.sellerNick,
            starType,
            starId: resData.id || data.starId,
          },
        });
        updateConversation({
          starTime: data.starTime,
          starType,
          starId: resData.id || data.starId,
          sessionKey: data.sessionKey,
        });
      }
    }
  };

  const handleTabChange = useDebounce((values: string) => {
    setTabActive(values);
  }, 300);

  const handleFilterChange = useDebounce((values: FilterType) => {
    setFilter(values);
  }, 300);

  const handleSorterChange = useDebounce((value: typeof sorter) => {
    setSorter(value);
  }, 300);

  const clearAllStarMark = async () => {
    const sellerAccounts = starList.map((item) => ({
      platform: item.platformType,
      sellerAccount: item.sellerNick,
    }));
    const selectStarType = Object.keys(filter.starType).filter(
      (filterItem) =>
        filter.starType?.[filterItem as "red" | "yellow" | "blue" | "green"],
    );
    socket?.sendMessage({
      cmd: WsCmdSend.DelStarAggregateItem,
      data: {
        starType: selectStarType,
        platformType: [...new Set(sellerAccounts.map((item) => item.platform))],
      },
    });
    const res = await batchDeleteStarMark({
      sellerAccounts,
      colors: selectStarType,
    }).promise.catch((error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      starList.forEach((item) => {
        updateConversation({
          sessionKey: item.sessionKey,
          starType: "init",
          starTime: `${Date.now()}`,
          starId: item.starId,
        });
      });
    }
  };

  const handleClearConversation = () => {
    if (tabActive === TabTypeMap.starMark) {
      clearAllStarMark();
    } else {
      clearConversations();
      socket?.sendMessage({
        cmd: WsCmdSend.DeleteReceptionConversations,
        data: undefined,
      });
    }
  };

  const handleMoveToTop = () => {
    rowVirtualizer.scrollToIndex(0, {
      behavior: "smooth",
    });
  };

  const handleOpenLongestWaitingConversation = useCallback(() => {
    const filteredList = _conversations.filter((item) => !item.isReply);
    const minItem = filteredList.reduce(
      (min, item) => (min.readTime > item.readTime ? item : min),
      filteredList[0],
    );
    const index = _conversations.findIndex(
      (item) => item.sessionKey === minItem.sessionKey,
    );
    rowVirtualizer.scrollToIndex(index, {
      behavior: "smooth",
    });
    updateConversation({
      ...minItem,
      isLight: false,
    });
    onOpenConversation(minItem);
  }, [_conversations, onOpenConversation, updateConversation, rowVirtualizer]);

  useEffect(() => {
    if (!timer.current) {
      timer.current = setInterval(() => {
        setTime(new Date().getTime());
      }, 1000);
    }
    return () => {
      if (timer.current) {
        clearInterval(timer.current);
        timer.current = undefined;
      }
    };
  }, []);

  useEffect(() => {
    return socket?.on("message", (data) => {
      if (
        data.cmd === WsCmdReceive.ReceptionOpenLongestWaitingTimeConversation
      ) {
        handleOpenLongestWaitingConversation();
      }
    });
  }, [socket, handleOpenLongestWaitingConversation]);

  return (
    <div className="flex-1 bg-gray-2 flex flex-col h-screen">
      <TabSelect onChange={handleTabChange} defaultValue={tabActive} />
      <div className="flex h-[38px] items-center gap-2 p-2 text-14 text-black-3">
        <Filter
          onChange={handleFilterChange}
          tabActive={tabActive}
          defaultValue={filter}
        />
        <Popconfirm
          title="确定要清除展示的会话吗？"
          onConfirm={handleClearConversation}
        >
          <button type="button" className="hover:text-blue-7">
            <ClearOutlined />
          </button>
        </Popconfirm>
        {tabActive === TabTypeMap.reception && (
          <>
            <span className="ml-auto mr-4 space-x-2 ">
              <span className="after:content-[':']">未回复</span>
              <span className="text-blue-7">{unreplyedNums}</span>
            </span>
            <Sorter onChange={handleSorterChange} defaultValue={sorter} />
          </>
        )}
      </div>
      {_conversations.length ? (
        <div
          className="overflow-auto flex-1 h-0 pb-4 px-1 flex flex-col"
          ref={parentRef}
        >
          <div
            className="relative w-full"
            style={{
              height: rowVirtualizer.getTotalSize(),
            }}
          >
            {rowVirtualizer.getVirtualItems().map((virtualRow) => {
              const item = _conversations[virtualRow.index];
              virtualRow.end;
              return (
                <div
                  key={virtualRow.key}
                  className="absolute top-0 left-0 w-full"
                  style={{
                    transform: `translateY(${virtualRow.start || 0}px)`,
                    zIndex: `${_conversations.length - virtualRow.index}`,
                  }}
                >
                  <ConversationItem
                    data={item}
                    time={time}
                    tabActive={tabActive}
                    onOpenConversation={onOpenConversation}
                    onRemoveConversation={onRemoveConversation}
                    onStarChange={(starType) => onStarChange(starType, item)}
                  />
                </div>
              );
            })}
          </div>

          {fnKey && normalKey ? (
            <div className="text-center space-x-2 text-12 text-black-3 p-2 mt-auto">
              <span>按</span>
              <span className="p-1 border rounded-8 border-black-3 min-w-8 inline-block">
                {fnKey}
              </span>
              <span>+</span>
              <span className="p-1 border rounded-8 border-black-3 min-w-8  inline-block">
                {normalKey}
              </span>
              <span>跳转到最新未回复的会话</span>
            </div>
          ) : null}
        </div>
      ) : (
        <div className="flex-1 flex flex-col h-0 justify-center items-center text-black-2 pb-9">
          <img src={emptyPlaceholder} className="w-full max-w-[400px]" alt="" />
          <p>暂无数据</p>
        </div>
      )}
      <button
        type="button"
        onClick={handleMoveToTop}
        className={clsx(
          "fixed bottom-4 transition-all right-4 shadow-1 border text-black-3 bg-white rounded-full p-2 hover:bg-blue-7 hover:text-white",
          rowVirtualizer.scrollOffset
            ? "opacity-100 scale-100"
            : "opacity-0 scale-0",
        )}
      >
        <UpOutlined />
      </button>
    </div>
  );
};

Component.displayName = "Reception";
