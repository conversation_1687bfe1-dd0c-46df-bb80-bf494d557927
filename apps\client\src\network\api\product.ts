import client from "@/network/client";
import type { APIResponse, PageResults } from "@workspace/types";

export interface IFocusProductInfo {
  buyerAccount?: string;
  detailUrl: string;
  imageUrl: string;
  productName: string;
  sellerAccount?: string;
  skuAttr?: string;
  skuId?: string;
  spuId?: string;
  thirdShopId?: string;
}

/** 获取话题/焦点商品信息 */
export const getFocusProductInfo = (params: {
  buyerAccount?: string;
  sellerAccount?: string;
}) => {
  return client<APIResponse<IFocusProductInfo>>(
    "/api/im/agent-focus-product/get",
    {
      method: "GET",
      params,
      shouldLog: true,
    },
  );
};

export type getProductData = {
  items: Array<{
    activities: [];
    customAttrs: Array<{
      code: string;
      createdAt: string;
      editable: boolean;
      name: string;
      proAttrId: string;
      updatedAt: string;
      userOwn: boolean;
      value: string;
    }>;
    isDelete: number;
    itemDetailImgs: Array<string>;
    name: string;
    pic: string;
    url: string;
    platformAttrs: Array<{
      code: string;
      createdAt: string;
      editable: boolean;
      name: string;
      proAttrId: string;
      updatedAt: string;
      userOwn: boolean;
      value: string;
    }>;
    productAttrs: Array<{
      code: string;
      createdAt: string;
      editable: boolean;
      name: string;
      proAttrId: string;
      updatedAt: string;
      userOwn: boolean;
      value: string;
    }>;
    userAttrs?: Array<{
      attrPic: string;
      attrType: number;
      code: string;
      name: string;
      proAttrId: string;
      userOwn: boolean;
      value: string;
    }>;
    productId: string;
    productStatus: number;
  }>;

  skuPropDetailGroups: Array<{
    pid: string;
    ptext: string;
    vidPropArray: Array<{
      pid: string;
      propImg: string;
      ptext: string;
      vid: string;
      vtext: string;
    }>;
    url: string;
  }>;
  pageSize: number;
  quesInclusive: boolean;
  skuList: [];
  total: number;
};

export type ProductInfo = getProductData["items"][number];

export type ProductDetailType = {
  attrs: {
    img: string;
    name: string;
    remark: string;
    value: string;
  }[];
  cid: string;
  deleted: number;
  detailUrl: string;
  goodsId: string;
  ifPromote: boolean;
  image: string;
  isDisplay: number;
  outerId: string;
  platform: number;
  /** sku价格（元） */
  price: number;
  sellerId: string;
  skuId: string;
  soldQuantity: number;
  standerAttrs: { img: string; name: string; remark: string; value: string }[];
  stock: number;
  title: string;
};

/** 更新话题商品 */
export const updateFocusProduct = (data: {
  buyerAccount: string;
  sellerAccount: string;
  skuId?: string;
  spuId: string;
}) => {
  return client<APIResponse<never>>("/api/im/agent-focus-product/change", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

export interface SearchProductDataParams {
  account: string;
  itemName?: string;
  pageIndex: string;
  pageSize: number;
  searchKey?: string;
  searchById?: string;
}

export interface SearchProductDataResultItem {
  activities: [];
  customAttrs: Array<{
    code: string;
    createdAt: string;
    editable: boolean;
    name: string;
    proAttrId: string;
    updatedAt: string;
    userOwn: boolean;
    value: string;
  }>;
  isDelete: number;
  itemDetailImgs: Array<string>;
  name: string;
  pic: string;
  url: string;
  platformAttrs: Array<{
    code: string;
    createdAt: string;
    editable: boolean;
    name: string;
    proAttrId: string;
    updatedAt: string;
    userOwn: boolean;
    value: string;
  }>;
  productAttrs: Array<{
    code: string;
    createdAt: string;
    editable: boolean;
    name: string;
    proAttrId: string;
    updatedAt: string;
    userOwn: boolean;
    value: string;
  }>;
  userAttrs?: Array<{
    attrPic: string;
    attrType: number;
    code: string;
    name: string;
    proAttrId: string;
    userOwn: boolean;
    value: string;
  }>;
  productId: string;
  productStatus: number;
}

//搜索商品
export function searchByName(data: SearchProductDataParams) {
  return client<APIResponse<PageResults<SearchProductDataResultItem>>>(
    "/api/im/agent-product/search-by-name",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
}

/** 获取商品SKU详情数组 */
export function getProductDetail(data: { goodsId: string }) {
  return client<APIResponse<ProductDetailType[]>>(
    "/api/im/agent-product/get-sku-detail",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
}

export type ProductChangeType = {
  account: string;
  platformType: number;
  buyerAccount: string;
  consultProduct: {
    spuId: string;
    skuId: string;
  };
  sessionProduct: {
    spuId: string;
    skuId: string;
  };
};

export interface IProductItem {
  activities: [];
  customAttrs: Array<{
    code: string;
    createdAt: string;
    editable: boolean;
    name: string;
    proAttrId: string;
    updatedAt: string;
    userOwn: boolean;
    value: string;
  }>;
  isDelete: number;
  itemDetailImgs: Array<string>;
  name: string;
  pic: string;
  url: string;
  platformAttrs: Array<{
    code: string;
    createdAt: string;
    editable: boolean;
    name: string;
    proAttrId: string;
    updatedAt: string;
    userOwn: boolean;
    value: string;
  }>;
  productAttrs: Array<{
    code: string;
    createdAt: string;
    editable: boolean;
    name: string;
    proAttrId: string;
    updatedAt: string;
    userOwn: boolean;
    value: string;
  }>;
  userAttrs?: Array<{
    attrPic: string;
    attrType: number;
    code: string;
    name: string;
    proAttrId: string;
    userOwn: boolean;
    value: string;
  }>;
  productId: string;
  productStatus: number;
}

export interface ISkuPropDetailGroupItem {
  pid: string;
  ptext: string;
  vidPropArray: Array<{
    pid: string;
    propImg: string;
    ptext: string;
    vid: string;
    vtext: string;
  }>;
  url: string;
}

export const getProductList = (data: { buyerNick: string }) => {
  return client<
    APIResponse<{
      items: IProductItem[];
      skuPropDetailGroups: ISkuPropDetailGroupItem[];
      pageSize: number;
      quesInclusive: boolean;
      skuList: [];
      total: number;
    }>
  >("/api/im/agent-product/list", {
    method: "GET",
    params: data,
    shouldLog: true,
  });
};

export interface IShopAttr {
  account: string;
  attrName: string;
  attrPic: string;
  attrType: number;
  attrValue: string;
  productId: string;
  shopId: string;
  userOwn: boolean;
  proAttrId: string;
}

//增加商品属性
export function addShopAttr(data: IShopAttr) {
  return client<APIResponse<unknown>>("/api/im/agent-product/add-attr", {
    method: "POST",
    data,
    shouldLog: true,
  });
}

//编辑商品属性
export function editShopAttr(data: IShopAttr) {
  return client<APIResponse<unknown>>("/api/im/agent-product/update-attr", {
    method: "POST",
    data,
    shouldLog: true,
  });
}

//删除商品属性
export function deleteShopAttr(data: { proAttrId: number | string }) {
  return client<APIResponse<unknown>>("/api/im/agent-product/delete-attr", {
    method: "GET",
    params: data,
    shouldLog: true,
  });
}
