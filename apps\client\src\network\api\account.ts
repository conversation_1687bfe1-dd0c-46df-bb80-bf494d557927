import client from "@/network/client";
import type { Platform } from "@workspace/business";
import type { APIResponse } from "@workspace/types";

/** 手机号码登录 */
export const postPhoneLogin = (data: {
  mobile: string;
  verifyCode: string;
  token: string;
}) => {
  return client<APIResponse<string>>("/api/gc/agent/auth/login-by-mobile", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 请求验证码 */
export const postVerifyCode = (data: { mobile: string }) => {
  return client<APIResponse<string>>(
    "/api/gc/authentication/verification-code",
    {
      method: "POST",
      data,
    },
  );
};

/** 获取微信登录二维码 */
export const getWechatQrCode = () => {
  return client<
    APIResponse<{
      qrCodeUrl: string;
      scene: string;
      shouldLog: true;
    }>
  >("/api/gc/wx/get-client-qr-code");
};

/** 查询微信扫描登录状态 */
export const postCheckWxLogin = (data: { scene: string }) => {
  return client<APIResponse<string>>(
    "/api/gc/agent-client/client-check-and-set-by-scene",
    {
      method: "POST",
      params: data,
      shouldLog: true,
    },
  );
};

/** 帐号登录 */
export const postPasswordLogin = (data: {
  account: string;
  password: string;
}) => {
  return client<APIResponse<string>>("/api/gc/agent-client/login-by-account", {
    data,
    method: "POST",
    shouldLog: true,
  });
};

export type AccountInfoType = {
  account: {
    account: string;
    chatbotAccountId: string;
    createAt: string;
    headImgUrl: string;
    id: string;
    /** 1-主账号; 2-智能客服默认子账号; 3-子账号	 */
    type: 1 | 2 | 3;
  };
  group: {
    /** 集团id */
    id: string;
    /** 智能客服集团ID */
    chatbotGroupId: string;
    /** 创建时间 */
    createAt: string;
    ifEnable: boolean;
    mobile: string;
    /** 集团名称 */
    name: string;
  };
  openSqs: boolean;
};

/** 获取帐号信息 */
export const getAccountInfo = () => {
  return client<APIResponse<AccountInfoType>>("/api/gc/agent-personal/detail", {
    shouldLog: true,
  });
};

/** 获取tanyu-group会话token */
export const getTanyuGroupSessionToken = (params: {
  thirdShopId: string;
  platForm: Platform;
}) => {
  return client<
    APIResponse<{
      token: string;
      groupId: string;
      accountId: string;
      wxOpenId: string;
    }>
  >("/api/gc/agent-client/switch-smart-workorder", {
    shouldLog: true,
    params,
  });
};

/** 获取tanyu-group会话token */
export const getWxGroupList = () => {
  return client<
    APIResponse<
      {
        accountId: string;
        groupId: string;
        groupName: string;
        mainOpenShopAgents: boolean;
        openAfterSales: boolean;
        openAiCC: boolean;
        openAiLabs: boolean;
        openChatbot: boolean;
        openCopilot: boolean;
        openShopAgents: boolean;
        openSqs: boolean;
      }[]
    >
  >("/api/gc/agent-client/list-group-brief", {
    shouldLog: true,
  });
};
