import { useAccount } from "@/business/hooks/useAccount";
import useSession from "@/business/hooks/useSession";
import { websocketStore } from "@/business/hooks/useWebsocket";
import WebSocketManager from "@/class/Ws";
import { Paths } from "@/config/path";
import {
  AccountTypeMap,
  WsCmdReceive,
  type WsMessage,
} from "@/constant/protocol/index";
import logger from "@/utils/log";
import { getFullPath } from "@/utils/route";
import { useEffect } from "react";
import { Outlet, useNavigate, useSearchParams } from "react-router";
import { useStore } from "zustand";
try {
  const { setReadyState, setWs } = websocketStore.getState();
  const searchParams = new URLSearchParams(window.location.search);
  const params = {
    port: searchParams.get("port")
      ? Number(searchParams.get("port"))
      : undefined,
    baseUrl: import.meta.env.VITE_WS_URL,
    query: searchParams.get("platformType")
      ? {
          platformType: searchParams.get("platformType") ?? "0",
        }
      : undefined,
  };
  logger.info("ws params:", window.location, params);
  const config = WebSocketManager.validConfig(params);
  const socket = new WebSocketManager({
    ...config,
    maxReconnectAttempts: undefined,
  });
  socket?.on("open", () => {
    setReadyState(socket.getWsState() || 1);
  });
  socket?.on("close", () => {
    setReadyState(socket.getWsState() || 3);
  });
  setWs(socket);
} catch (e) {
  logger.warn((e as Error).message);
}

const WebsocketProvider = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { setToken, asyncUpdate, updateAccountType, update } = useAccount();
  const { setState } = useSession();
  const socket = useStore(websocketStore, (state) => state.socket);

  useEffect(() => {
    const unsubscribe = socket?.on("message", (payload: WsMessage) => {
      switch (payload.cmd) {
        case WsCmdReceive.AutoLogin: {
          setToken(payload.data.cookie);
          if (payload.data.accountType !== AccountTypeMap.Wechat) {
            asyncUpdate();
          }
          break;
        }

        case WsCmdReceive.NavigateToSetting:
          navigate(
            getFullPath(Paths.SystemConfig, {
              query: Object.fromEntries(searchParams),
            }),
          );
          break;
        case WsCmdReceive.Logout:
          setToken("");
          setState({ token: undefined, groupId: undefined });
          update(undefined);
          updateAccountType(AccountTypeMap.Password);
          localStorage.removeItem("accountMap");
          break;
        default:
          break;
      }
    });
    return unsubscribe;
  }, [
    socket,
    searchParams,
    navigate,
    setToken,
    asyncUpdate,
    update,
    updateAccountType,
    setState,
  ]);

  return <Outlet context={socket} />;
};

export default WebsocketProvider;
