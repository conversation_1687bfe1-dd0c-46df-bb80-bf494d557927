import useConfig from "@/business/hooks/useConfig";
import { WsCmdSend } from "@/constant/protocol/index";
import { Form, type FormProps, Switch } from "antd";
import { useEffect } from "react";
import RecordHotKeys from "./RecordHotKeys";
import Section from "./Section";

const Base = () => {
  const { store, socket } = useConfig();
  const [form] = Form.useForm();

  const onValuesChange: FormProps["onValuesChange"] = (changes, allValues) => {
    if (
      // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
      changes.hasOwnProperty("offlineNotify") ||
      // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
      changes.hasOwnProperty("updateNotify") ||
      // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
      changes.hasOwnProperty("autoLogin")
    ) {
      socket?.sendMessage({
        cmd: WsCmdSend.SetBaseConfig,
        data: {
          offlineNotifySwitch: allValues.offlineNotify,
          updateNotifySwitch: allValues.updateNotify,
          autoLoginSwitch: allValues.autoLogin,
        },
      });
    }
    store.setConfig({
      base: {
        ...store.base,
        ...allValues,
      },
    });
  };

  useEffect(() => {
    form.setFieldsValue(store.base);
  }, [store.base, form]);

  return (
    <Form
      form={form}
      name="base"
      initialValues={store.base}
      onValuesChange={onValuesChange}
    >
      <Section title="掉线通知">
        <div className="flex items-center gap-2">
          <Form.Item noStyle name="offlineNotify">
            <Switch />
          </Form.Item>
          <span>开启后，客户端托管状态失效后将在前端通知</span>
        </div>
      </Section>
      <Section title="更新通知">
        <div className="flex items-center gap-2">
          <Form.Item noStyle name="updateNotify">
            <Switch />
          </Form.Item>
          <span>开启后，客户端有更新会弹窗提示进行升级</span>
        </div>
      </Section>
      <Section title="自动托管">
        <div className="flex items-center gap-2">
          <Form.Item noStyle name="autoLogin">
            <Switch />
          </Form.Item>
          <span>开启后，客户端扫描到可托管账号时会自动进行托管</span>
        </div>
      </Section>
      <Section title="快捷键设置">
        <div className="space-y-4">
          <div className="grid grid-cols-[150px_1fr] items-center">
            <div>生成催一下话术</div>
            <div>
              <Form.Item noStyle name="reminderShortcut">
                <RecordHotKeys
                  placeholder="按下组合键 例如 Alt + 1"
                  cmd={WsCmdSend.SetReminderShortcut}
                />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-[150px_1fr] items-center">
            <div>发送卡片话术</div>
            <div>
              <Form.Item noStyle name="cardMsgShortcut">
                <RecordHotKeys
                  placeholder="按下组合键 例如 Alt + Shift"
                  cmd={WsCmdSend.SetCardMsgShortcut}
                />
              </Form.Item>
              <span> + 序号</span>
            </div>
          </div>
          <div className="grid grid-cols-[150px_1fr] items-center">
            <div>发送单句话术</div>
            <div>
              <Form.Item noStyle name="singleMsgShortcut">
                <RecordHotKeys
                  placeholder="按下组合键 例如 Alt + Shift"
                  cmd={WsCmdSend.SetSingleMsgShortcut}
                />
              </Form.Item>
              <span> + 序号</span>
            </div>
          </div>
        </div>
      </Section>
    </Form>
  );
};

export default Base;
