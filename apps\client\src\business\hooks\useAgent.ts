import {
  type AgentState,
  agentStore,
} from "@/business/components/AgentProvider/store";
import useWebSocket from "@/business/hooks/useWebsocket";
import { useStore } from "zustand";

const defaultSelector = (state: AgentState) => state;

const useAgent = <T = AgentState>(
  selector: (state: AgentState) => T = defaultSelector as (
    state: AgentState,
  ) => T,
) => {
  const socket = useWebSocket();
  const store = useStore(agentStore, selector);
  return { socket, store };
};
export default useAgent;
