import useConfig from "@/business/hooks/useConfig";
import useFileDownload from "@/business/hooks/useFileDownload";
import { useLastApp } from "@/business/hooks/useLastApp";
import useServiceAccount from "@/business/hooks/useServiceAccount";
import { useSqsInstall } from "@/business/hooks/useSqsInstall";
import { AppEnum, AppTypeEnum } from "@/constant/client";
import { DownloadStatusMap, WsCmdSend } from "@/constant/protocol/index";
import { useDebounce } from "@workspace/ui";
import { Button, Popconfirm } from "antd";
import { useEffect, useMemo } from "react";
import CloseOutlined from "~icons/ant-design/close-outlined";

const SQSButton = () => {
  const { store: configStore } = useConfig();
  const { socket } = useServiceAccount();
  const { files } = useFileDownload();
  const { appMap } = useLastApp();
  const { type, handleInstallClient, handleCancelInstallClient } =
    useSqsInstall();

  const handleOpenSQS = useDebounce(
    () => {
      socket?.sendMessage({
        cmd: WsCmdSend.OpenPlugin,
        data: {
          pluginType: AppTypeEnum.SQS,
        },
      });
    },
    500,
    {
      leading: true,
      trailing: false,
    },
  );

  const appInfo = useMemo(() => {
    return appMap[AppEnum.SQS];
  }, [appMap]);

  const sqsPluginInfo = useMemo(
    () =>
      configStore.pluginInfo?.find(
        (someItem) => someItem.pluginType === AppTypeEnum.SQS,
      ),
    [configStore.pluginInfo],
  );

  const sqsTaskId = useMemo(
    () => `${AppTypeEnum.SQS}-${appInfo?.versionNo}`,
    [appInfo?.versionNo],
  );

  useEffect(() => {
    if (
      sqsPluginInfo?.version !== appInfo?.versionNo &&
      appInfo?.versionNo &&
      sqsPluginInfo?.isExist
    ) {
      handleInstallClient();
    }
  }, [sqsPluginInfo, appInfo, handleInstallClient]);

  const DownloadStatusRenderMap = {
    disabled: (
      <Button type="primary" title="未开通数据洞察产品" disabled>
        启动数据洞察
      </Button>
    ),
    default: (
      <Button type="primary" onClick={() => handleOpenSQS()}>
        启动数据洞察
      </Button>
    ),
    install: (
      <Button
        type="primary"
        disabled={!appInfo?.versionNo || !configStore.pluginInfo?.length}
        onClick={() => handleInstallClient()}
      >
        下载数据洞察
      </Button>
    ),
    downloading: (
      <Popconfirm
        title={<span>你确定要取消吗?</span>}
        onConfirm={() => handleCancelInstallClient()}
      >
        <Button
          color="primary"
          variant="outlined"
          onClick={() => handleInstallClient()}
        >
          <span className="border border-transparent">
            {sqsPluginInfo?.isExist ? "更新中" : "下载中"}{" "}
            {files[sqsTaskId]?.progress}
          </span>
          <CloseOutlined />
        </Button>
      </Popconfirm>
    ),
    installing: (
      <Button
        color="primary"
        variant="outlined"
        onClick={() => handleInstallClient()}
      >
        <span className="text-blue-7 border border-transparent">安装中...</span>
      </Button>
    ),
    failed: (
      <Button
        color="primary"
        variant="outlined"
        onClick={() => handleInstallClient()}
      >
        安装失败，请重试
      </Button>
    ),
    deleting: <Button danger>卸载中...</Button>,
  };

  let component = type
    ? DownloadStatusRenderMap[type as keyof typeof DownloadStatusRenderMap]
    : null;

  if (
    sqsPluginInfo?.isExist &&
    sqsPluginInfo?.version === appInfo?.versionNo &&
    (!files[sqsTaskId] ||
      ([DownloadStatusMap.InstallSuccess] as number[]).includes(
        files[sqsTaskId]?.status,
      ))
  ) {
    component = DownloadStatusRenderMap.default;
  }

  return <>{component}</>;
};

export default SQSButton;
