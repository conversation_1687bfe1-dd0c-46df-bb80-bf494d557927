import { useEffect, useState } from "react";

const blackList = ["backspace", "enter"];

const useRecordHotKeys = (initialHotkeys: { [key: string]: string[] } = {}) => {
  const [recordingFor, setRecordingFor] = useState<string | null>(null);
  const [hotkeys, setHotkeys] = useState<{ [key: string]: string[] }>(
    initialHotkeys,
  );
  // 录制新快捷键的 Effect
  useEffect(() => {
    if (!recordingFor) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      console.log("handleKeyDown");
      event.preventDefault();
      event.stopPropagation();

      let newKeys: Set<string> = new Set();
      if (event.ctrlKey) newKeys.add("Ctrl");
      if (event.altKey) newKeys.add("Alt");
      if (event.shiftKey) newKeys.add("Shift");
      if (event.metaKey) newKeys.add("Win");

      const hasModifierKey =
        event.ctrlKey || event.altKey || event.shiftKey || event.metaKey;
      const mainKey = event.key.toLowerCase();
      const modifierKeys = ["control", "alt", "shift", "meta"];
      if (mainKey.trim() === "backspace" && !hasModifierKey) {
        newKeys = new Set();
      } else if (blackList.includes(mainKey)) {
        return;
      } else if (!modifierKeys.includes(mainKey) && mainKey.trim() !== "") {
        newKeys.add(event.key.toUpperCase());
      }

      setHotkeys((prev) => ({ ...prev, [recordingFor]: Array.from(newKeys) }));
    };

    // 添加全局监听器以捕获按键
    window.addEventListener("keydown", handleKeyDown, true);

    // 清理函数
    return () => {
      window.removeEventListener("keydown", handleKeyDown, true);
    };
  }, [recordingFor]);

  return {
    recordingFor,
    setRecordingFor,
    hotkeys,
    setHotkeys,
  };
};

export default useRecordHotKeys;
