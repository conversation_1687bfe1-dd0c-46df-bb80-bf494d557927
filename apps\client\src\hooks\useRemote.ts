import type { APIResponse } from "@workspace/types";
import type { AxiosResponse } from "axios";
import useSWR, { type SWRConfiguration } from "swr";

const useRemote = <T>(
  key: Parameters<typeof useSWR>[0],
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  fetcher: (...args: any[]) => {
    promise: Promise<AxiosResponse<APIResponse<T>>>;
    controller?: AbortController;
  },
  options?: SWRConfiguration,
) => {
  return useSWR(
    key,
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    async (...args: any[]) => {
      const res = await fetcher(...args).promise.catch((error: Error) => error);
      if (res instanceof Error) throw res;
      if (res.data.success) {
        return res.data.data;
      }
      throw new Error(res.data.msg);
    },
    options,
  );
};

export default useRemote;
