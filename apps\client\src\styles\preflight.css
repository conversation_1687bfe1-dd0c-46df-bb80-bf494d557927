input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  /* 这里设置了一个非常长的动画时间（5000秒），以确保背景颜色的变化速度极慢到肉眼无法察觉。 */
  transition: background-color 50000s ease-in-out 0s;
}

:root {
  --scrollbar-color-thumb: #afafb2;
  --scrollbar-color-track: transparent;
  --scrollbar-width: thin;
  --scrollbar-width-legacy: 5px;
}

/* Modern browsers with `scrollbar-*` support */
@supports (scrollbar-width: auto) {
  * {
    scrollbar-color: var(--scrollbar-color-thumb) var(--scrollbar-color-track);
    scrollbar-width: var(--scrollbar-width);
  }
}

/* Legacy browsers with `::-webkit-scrollbar-*` support */
@supports selector(::-webkit-scrollbar) {
  *::-webkit-scrollbar-thumb {
    background: var(--scrollbar-color-thumb);
  }
  *::-webkit-scrollbar-track {
    background: var(--scrollbar-color-track);
  }
  *::-webkit-scrollbar {
    max-width: var(--scrollbar-width-legacy);
    max-height: var(--scrollbar-width-legacy);
  }
}
