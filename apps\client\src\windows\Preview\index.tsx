import logger from "@/utils/log";
import { useSearchParams } from "react-router";

export const Component = () => {
  const [searchParams] = useSearchParams();
  const href = searchParams.get("href");
  const type = searchParams.get("type");
  logger.info("preview", type, href);

  if (href) {
    if (type === "video") {
      return (
        <div className="w-screen h-screen">
          {/* biome-ignore lint/a11y/useMediaCaption: <explanation> */}
          <video src={href} className="w-full h-full" controls autoPlay />
        </div>
      );
    }
    if (type === "pic") {
      return (
        <div className="w-screen h-screen flex items-center justify-center bg-black-5">
          <img
            src={href}
            alt=""
            className="max-w-full max-h-full object-contain"
          />
        </div>
      );
    }
    return (
      <div className="w-screen h-screen">
        <iframe className="w-full h-full" title="preview" src={href} />
      </div>
    );
  }
  return null;
};
