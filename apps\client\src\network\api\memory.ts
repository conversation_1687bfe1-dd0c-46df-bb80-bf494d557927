import client from "@/network/client";
import type { APIResponse } from "@workspace/types";

/** 获取买家聊天摘要 */
export const getChatDigestByBuyerAccount = (params: {
  buyerAccount: string;
}) => {
  return client<APIResponse<{ content: string; createAt: string }>>(
    "/api/im/agent/memory/buyer-label/chat-digest",
    {
      params,
      shouldLog: true,
    },
  );
};

export type BuyerMemoryLabel = {
  createAt: string;
  id: string;
  label: string;
  labelMetaId: string;
  lastUpdatedAt: string;
  value: string;
};

/** 获取买家标签列表 */
export const getBuyerLabelList = (params: {
  buyerAccount: string;
}) => {
  return client<APIResponse<{ buyerLabels: BuyerMemoryLabel[] }>>(
    "/api/im/agent/memory/buyer-label/list",
    {
      params,
      shouldLog: true,
    },
  );
};

/** 删除买家标签 */
export const postDeleteBuyerLabel = (data: {
  id: string;
}) => {
  return client<APIResponse<boolean>>("/api/im/agent/memory/buyer-label/del", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 清空买家标签 */
export const postClearBuyerLabel = (data: {
  buyerAccount: string;
}) => {
  return client<APIResponse<boolean>>(
    "/api/im/agent/memory/buyer-label/clear",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
};

export type MemoryLabel = {
  des: string;
  groupId: string;
  id: string;
  label: string;
  labelGroupId: string;
  lastUpdateAt: string;
};

/** 获取可用标签 */
export const getAvailableLabelList = () => {
  return client<APIResponse<{ labelMetas: MemoryLabel[] }>>(
    "/api/im/agent/memory/buyer-label/label-meta-list",
    {
      shouldLog: true,
    },
  );
};

/** 保存买家标签 */
export const postSaveBuyerLabel = (data: {
  id: string;
  value: string;
}) => {
  return client<APIResponse<boolean>>(
    "/api/im/agent/memory/buyer-label/update",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
};

/** 添加买家标签 */
export const postAddBuyerLabel = (data: {
  buyerAccount: string;
  labelMetaId: string;
  value: string;
}) => {
  return client<APIResponse<boolean>>(
    "/api/im/agent/memory/buyer-label/create",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
};

/** 检查是否有记忆功能权限 */
export const getMemoryPermission = () => {
  return client<APIResponse<boolean>>(
    "/api/im/agent/memory/buyer-label/client-check-auth",
    {
      shouldLog: true,
    },
  );
};
