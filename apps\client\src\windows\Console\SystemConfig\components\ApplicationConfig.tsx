import useConfig from "@/business/hooks/useConfig";
import useFileDownload from "@/business/hooks/useFileDownload";
import { useLastApp } from "@/business/hooks/useLastApp";
import { useSqsInstall } from "@/business/hooks/useSqsInstall";
import { AppEnum, AppMap, AppTypeEnum } from "@/constant/client";
import {
  DownloadStatusMap,
  WsCmdReceive,
  WsCmdSend,
} from "@/constant/protocol";
import { useLast } from "@workspace/ui";
import { Button, Form, Input, Popconfirm, Progress, Tooltip } from "antd";
import { useEffect, useMemo } from "react";
import DeleteOutlined from "~icons/ant-design/delete-outlined";
import DownloadOutlined from "~icons/ant-design/download-outlined";
import LoadingOutlined from "~icons/ant-design/loading-outlined";
import ReloadOutlined from "~icons/ant-design/reload-outlined";
import StopOutlined from "~icons/ant-design/stop-outlined";
import Section from "./Section";

const AgentConfig = () => {
  const { store: configStore, socket } = useConfig();
  const getStore = useLast(configStore);
  const [form] = Form.useForm();
  const { files } = useFileDownload();
  const { appMap } = useLastApp();
  const {
    type,
    handleInstallClient,
    handleCancelInstallClient,
    handleUnInstallClient,
  } = useSqsInstall();

  const directory = Form.useWatch("directory", form);

  const handleSelectDirectory = () => {
    socket?.sendMessage({
      cmd: WsCmdSend.SelectDirectory,
      data: {
        windowTitle: "选择目录",
        defaultDir: directory,
      },
    });
    configStore.setConfig({
      client: {
        ...configStore.client,
        fielPath: directory,
      },
    });
  };

  useEffect(() => {
    const unsubscribe = socket?.on("message", (paylod) => {
      const path = paylod.data.directory;
      // 获取用户选择目录结果
      if (paylod.cmd === WsCmdReceive.SelectedDirectory && path) {
        form.setFieldsValue({
          directory: path,
        });
        socket?.sendMessage({
          cmd: WsCmdSend.SetPlatformClientsInstallDirectory,
          data: {
            path,
          },
        });
        getStore().setConfig({
          appConfig: {
            ...getStore().appConfig,
            fileSavePath: path,
          },
        });
      }
    });
    return unsubscribe;
  }, [socket, form, getStore]);

  /* 已安装插件 */
  const sqsPluginInfo = useMemo(
    () =>
      configStore.pluginInfo?.find(
        (someItem) => someItem.pluginType === AppTypeEnum.SQS,
      ),
    [configStore.pluginInfo],
  );

  const appInfo = useMemo(() => {
    return appMap[AppEnum.SQS];
  }, [appMap]);

  const sqsTaskId = useMemo(
    () => `${AppTypeEnum.SQS}-${appInfo?.versionNo}`,
    [appInfo?.versionNo],
  );

  const IconComponent = useMemo(
    () => AppMap[appInfo?.app as AppEnum]?.icon,
    [appInfo],
  );

  const defaultActions = () => (
    <DownloadOutlined
      className="text-black-2"
      onClick={() => handleInstallClient()}
    />
  );

  const DownloadStatusRenderMap = {
    disabled: <StopOutlined className="text-black-2" />,
    default: defaultActions(),
    install: defaultActions(),
    downloading: (
      <Popconfirm
        title={<span>你确定要取消吗?</span>}
        onConfirm={() => handleCancelInstallClient()}
      >
        <Progress
          type="circle"
          trailColor="#e6f4ff"
          percent={Number(files[sqsTaskId]?.progress?.replace("%", "") || 0)}
          strokeWidth={20}
          size={14}
        />
      </Popconfirm>
    ),
    installing: (
      <Progress
        type="circle"
        trailColor="#e6f4ff"
        percent={100}
        strokeWidth={20}
        size={14}
        format={() => "安装进行中"}
      />
    ),
    failed: (
      <Tooltip title="安装失败，请重试">
        <ReloadOutlined
          className="text-red-7"
          onClick={() => handleInstallClient()}
        />
      </Tooltip>
    ),
    success: (
      <Popconfirm
        title={<span>你确定要卸载吗?</span>}
        onConfirm={() => handleUnInstallClient()}
      >
        <DeleteOutlined className="text-black-2" />
      </Popconfirm>
    ),
    deleting: (
      <Tooltip title="卸载中...">
        <LoadingOutlined />
      </Tooltip>
    ),
  };
  let actionComponent = type
    ? DownloadStatusRenderMap[type as keyof typeof DownloadStatusRenderMap]
    : null;

  if (
    sqsPluginInfo?.isExist &&
    sqsPluginInfo?.version === appInfo?.versionNo &&
    (!files[sqsTaskId] ||
      ([DownloadStatusMap.InstallSuccess] as number[]).includes(
        files[sqsTaskId]?.status,
      ))
  ) {
    actionComponent = DownloadStatusRenderMap.success;
  }

  return (
    <Form
      form={form}
      initialValues={{
        directory: configStore.installDirectory,
      }}
      className="space-y-2 h-full flex flex-col"
    >
      <div className="space-y-2">
        <div className="after:content-[':'] after:mx-1">应用安装路径</div>
        <div className="flex items-center gap-2">
          <Form.Item noStyle name="directory">
            <Input readOnly />
          </Form.Item>
          <Button onClick={handleSelectDirectory}>选择</Button>
        </div>
        <p className="text-black-2 text-12">
          变更路径后系统将自动迁移已安装内容，并在下次启动本软件生效，半小时内请勿关机
        </p>
      </div>
      <Section
        title="应用管理"
        desc="开通产品后，可安装对应的应用，应用安装后有更新时自动升级"
      >
        <div>
          {appInfo?.versionNo && (
            <div className="flex items-center justify-between gap-4 shadow-sm py-2 px-4 w-fit border border-gray-2 rounded-4">
              <div className="flex items-center gap-2">
                <IconComponent className="w-6 h-6" />
                <span className="text-12">
                  {AppMap[appInfo?.app as AppEnum]?.name}({appInfo?.versionNo})
                </span>
              </div>
              <div>{actionComponent}</div>
            </div>
          )}
        </div>
      </Section>
    </Form>
  );
};
export default AgentConfig;
