import { sendMessages } from "@/network/api/base";
import {
  type IProductItem,
  type ProductDetailType,
  getProductDetail,
} from "@/network/api/product";
import { <PERSON><PERSON>, Drawer, type DrawerProps, Spin, Typography } from "antd";
import clsx from "clsx";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import CloseOutlined from "~icons/ant-design/close-outlined";
import { ProductContext } from "../context";

interface SKUBottomSheetProps extends DrawerProps {
  data?: IProductItem;
  buyerNick: string;
}

const SKUBottomSheet = ({
  open,
  onClose,
  data,
  buyerNick,
}: SKUBottomSheetProps) => {
  const [loading, setLoading] = useState(false);
  const [currentAttrs, setCurrentAttrs] = useState<
    Record<string, ProductDetailType["attrs"][number]>
  >({});
  const [state, setState] = useState<ProductDetailType[] | undefined>();
  const { closeSKU } = useContext(ProductContext);

  //发送属性
  const sendContent = async () => {
    const postData = {
      content: currentSKU?.detailUrl as string,
      buyerAccount: buyerNick,
      imageUrl: "",
    };
    const res = await sendMessages(postData).promise.catch((error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
    }
  };
  const getSKUDetail = useCallback(async () => {
    setLoading(true);
    const params = {
      goodsId: data?.productId as string,
    };
    const res = await getProductDetail(params).promise.catch(
      (error: Error) => error,
    );
    setLoading(false);
    if (res instanceof Error) return;
    if (res.data.success) {
      setState(res.data.data);
      setCurrentAttrs(
        res.data.data[0].attrs.reduce(
          (acc, cur) => Object.assign(acc, { [cur.name]: cur }),
          {},
        ),
      );
    }
  }, [data?.productId]);

  const attrsGroup = useMemo(() => {
    if (!state) return {};
    return state.reduce(
      (acc, cur) => {
        const attrs =
          cur.attrs?.reduce((ob, cur) => {
            if (!ob[cur.name]) {
              ob[cur.name] = [];
            }
            if (!ob[cur.name].find((item) => item.value === cur.value)) {
              ob[cur.name].push(cur);
            }
            return ob;
          }, acc) || {};
        return attrs;
      },
      {} as Record<string, Array<ProductDetailType["attrs"][number]>>,
    );
  }, [state]);

  const currentSKU = useMemo(() => {
    const sku = state?.find((item) =>
      item.attrs.every((item) => currentAttrs[item.name]?.value === item.value),
    );
    return sku;
  }, [currentAttrs, state]);

  const handleSwichSKU = (item: ProductDetailType["attrs"][number]) => {
    setCurrentAttrs({
      ...currentAttrs,
      [item.name]: item,
    });
  };

  useEffect(() => {
    if (open && data?.productId) {
      getSKUDetail();
    } else {
      setState(undefined);
      setCurrentAttrs({});
    }
  }, [getSKUDetail, open, data?.productId]);

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <span>商品SKU</span>
          <button
            type="button"
            className="bg-transparent outline-none border-none cursor-pointer "
            onClick={onClose}
          >
            <CloseOutlined />
          </button>
        </div>
      }
      footer={
        <div className="flex items-center justify-end gap-2">
          <Button onClick={closeSKU}>取消</Button>
          <Button type="primary" onClick={sendContent}>
            发送链接
          </Button>
        </div>
      }
      closeIcon={null}
      placement="bottom"
      open={open}
      height="auto"
      className=" overflow-hidden rounded-t-8 [&_.ant-drawer-body]:max-h-[70vh] [&_.ant-drawer-body]:p-3  [&_.ant-drawer-footer]:px-3 [&_.ant-drawer-header]:px-3 [&_.ant-drawer-header]:py-[14px]"
    >
      <Spin spinning={loading}>
        <div>
          <div className="flex gap-1 ">
            <img
              src={currentSKU?.image || data?.pic}
              alt=""
              className="w-16 h-16 rounded-[2px] flex-shrink-0 object-contain"
            />
            <div className="flex flex-col w-0 flex-1">
              <Typography.Paragraph
                ellipsis={{ rows: 2, tooltip: currentSKU?.title || data?.name }}
                className="!mb-0"
              >
                {currentSKU?.title || data?.name}
              </Typography.Paragraph>
              <div className="text-12 flex items-center justify-between mt-auto">
                <span className="text-black-2">
                  <span className="after:content-[':'] after:mr-0.5">库存</span>
                  <span>{currentSKU?.stock}</span>
                </span>
                <span className="text-orange-7 ">
                  {currentSKU?.price?.toLocaleString("zh-CN", {
                    currency: "CNY",
                    style: "currency",
                  })}
                </span>
              </div>
            </div>
          </div>
          <div className="space-y-4 mt-4">
            {Object.entries(attrsGroup)?.map(([name, items]) => (
              <div className="space-y-2" key={name}>
                <div className="text-black-4 text-12">{name}</div>
                <div className="flex items-center gap-2 flex-wrap">
                  {items.map((item) => (
                    <span
                      key={item.value}
                      onClick={() => handleSwichSKU(item)}
                      onKeyDown={() => {}}
                      className={clsx(
                        "border border-solid rounded-4 px-2 cursor-pointer  text-12",
                        currentAttrs[item.name]?.value === item.value
                          ? "border-blue-3 bg-blue-1 text-blue-7"
                          : "border-gray-4 text-black-4",
                      )}
                    >
                      {item.value}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </Spin>
    </Drawer>
  );
};

export default SKUBottomSheet;
