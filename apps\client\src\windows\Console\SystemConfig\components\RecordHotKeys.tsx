import useWebSocket from "@/business/hooks/useWebsocket";
import { WsCmdReceive, type WsCmdSend } from "@/constant/protocol";
import useRecordHotKeys from "@/hooks/useRecordHotKeys";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@workspace/ui";
import { useEffect, useRef, useState } from "react";

interface RecordHotKeysProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: React.ReactNode;
  cmd:
    | typeof WsCmdSend.SetReminderShortcut
    | typeof WsCmdSend.SetCardMsgShortcut
    | typeof WsCmdSend.SetSingleMsgShortcut
    | typeof WsCmdSend.SwitchReceptionShotcutForMode
    | typeof WsCmdSend.SetReceptionShotcutForOpenConversation;
}

const RecordHotKeys = ({
  cmd,
  value,
  onChange,
  placeholder,
}: RecordHotKeysProps) => {
  const socket = useWebSocket();
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const resolveRef = useRef<(result: boolean) => void>(console.log);
  const { recordingFor, setRecordingFor, hotkeys, setHotkeys } =
    useRecordHotKeys({
      hotkey: value ? value.split("+") : [],
    });
  const onConfirm = () => {
    const newValue = hotkeys.hotkey?.join("+") || "";
    resolveRef.current = (result: boolean) => {
      if (result) {
        onChange?.(newValue);
        setRecordingFor(null);
        setOpen(false);
      } else {
        setError(new Error("快捷键冲突，设置失败"));
        setTimeout(() => {
          setError(null);
        }, 5000);
      }
    };
    socket?.sendMessage({
      cmd,
      data: {
        shortcut: newValue,
      },
    });
  };

  useEffect(() => {
    if (open) {
      return socket?.on("message", (payload) => {
        const { cmd, data } = payload;
        switch (cmd) {
          case WsCmdReceive.SwitchReceptionShotcutForModeACK:
          case WsCmdReceive.ReceptionConversationShortcutUpdateACK:
          case WsCmdReceive.SetSingleMsgShortcutACK:
          case WsCmdReceive.SetCardMsgShortcutACK:
          case WsCmdReceive.SetReminderShortcutACK: {
            if (recordingFor && open) {
              resolveRef.current(data.isSuccess);
            }
            break;
          }
          default:
            break;
        }
      });
    }
  }, [socket, recordingFor, open]);

  useEffect(() => {
    if (open) {
      setHotkeys({
        hotkey: value ? value.split("+") : [],
      });
    } else {
      setError(null);
      setRecordingFor(null);
    }
  }, [value, open, setHotkeys, setRecordingFor]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="rounded-4 min-w-40  inline-block"
          onClick={() => setRecordingFor("hotkey")}
        >
          {value ? (
            <span className="text-black-4">{value}</span>
          ) : (
            <span className="text-black-2">未设置</span>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent
        className="w-96 outline-none"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>请直接在键盘上输入新的快捷键</DialogTitle>
        </DialogHeader>
        <div>
          <div className="min-h-28 flex items-center justify-center text-18 font-medium">
            {recordingFor && hotkeys[recordingFor]?.length ? (
              <span className="text-black-4 ">
                {hotkeys[recordingFor].join("  +  ")}
              </span>
            ) : (
              <span className="text-black-2">
                {placeholder || "请按下组合键，例如 Ctrl + Shift"}
              </span>
            )}
          </div>
          <div>
            {error && <p className="text-red-7 text-center">{error.message}</p>}
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" size="sm" className="rounded-md">
              取消
            </Button>
          </DialogClose>
          <Button
            type="button"
            size="sm"
            onClick={onConfirm}
            className="rounded-md"
          >
            确定
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RecordHotKeys;
