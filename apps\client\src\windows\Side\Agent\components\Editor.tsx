import { $createLinkNode, AutoLinkNode, LinkNode } from "@lexical/link";
import {
  AutoLinkPlugin,
  createLinkMatcherWithRegExp,
} from "@lexical/react/LexicalAutoLinkPlugin";
import {
  type InitialConfigType,
  LexicalComposer,
} from "@lexical/react/LexicalComposer";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { PlainTextPlugin } from "@lexical/react/LexicalPlainTextPlugin";
import { useLast } from "@workspace/ui";
import { $createParagraphNode, $createTextNode, $getRoot } from "lexical";
import { type MouseEventHandler, useEffect } from "react";

const theme = {
  // Theme styling goes here
  //...
  link: "text-blue-7 underline",
};

const URL_REGEX =
  /((https?:\/\/(www\.)?)|(www\.))[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)(?<![-.+():%])/;

const EMAIL_REGEX =
  /(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/;

const MATCHERS = [
  createLinkMatcherWithRegExp(URL_REGEX, (text) => {
    return text.startsWith("http") ? text : `https://${text}`;
  }),
  createLinkMatcherWithRegExp(EMAIL_REGEX, (text) => {
    return `mailto:${text}`;
  }),
];

const ComposeAction = ({
  children,
  onChange,
  onOpenUrl,
}: {
  children: React.ReactNode;
  onChange?: (value: string) => void;
  onOpenUrl?: (url: string) => void;
}) => {
  const [editor] = useLexicalComposerContext();
  const getOnChange = useLast(onChange);

  const handleClick: MouseEventHandler<HTMLDivElement> = (event) => {
    const anchor = (event.target as HTMLElement)?.closest("a");
    if (anchor) {
      event.preventDefault();
      const url = anchor.getAttribute("href");
      if (url) {
        onOpenUrl?.(url);
        return;
        // 在此添加自定义点击事件逻辑
      }
    }
  };

  useEffect(() => {
    editor.registerUpdateListener(() => {
      editor.getEditorState().read(() => {
        const root = $getRoot();
        getOnChange()?.(root.getTextContent());
      });
    });
  }, [editor, getOnChange]);

  return (
    // biome-ignore lint/a11y/useKeyWithClickEvents: <explanation>
    <div onClick={handleClick}>{children}</div>
  );
};

const createEditorStateFromText = (plainText: string) => {
  if (!plainText) return;
  const urlRegex = /(?:https?:\/\/|www\.)[^\s/$.?#].[^\s]*/gi;
  const root = $getRoot();
  if (root.getFirstChild() === null) {
    const paragraphs = plainText.split(/\n/);

    paragraphs.forEach((paragraphText) => {
      const paragraph = $createParagraphNode();
      let lastIndex = 0;
      let match = urlRegex.exec(paragraphText);
      while (match !== null) {
        const url = match[0];
        const textBefore = paragraphText.substring(lastIndex, match.index);

        if (textBefore) {
          paragraph.append($createTextNode(textBefore));
        }

        const fullUrl = url.startsWith("http") ? url : `https://${url}`;
        // 直接创建 linkNode 的 POJO
        const Link = $createLinkNode(fullUrl);
        Link.append($createTextNode(url));
        paragraph.append(Link);
        lastIndex = urlRegex.lastIndex;
        match = urlRegex.exec(paragraphText);
      }

      const textAfter = paragraphText.substring(lastIndex);
      if (textAfter) {
        paragraph.append($createTextNode(textAfter));
      }
      root.append(paragraph);
    });
  }
};

const Editor = ({
  content,
  onChange,
  onOpenUrl,
}: {
  content: string;
  onChange?: (value: string) => void;
  onOpenUrl?: (url: string) => void;
}) => {
  const initialConfig: InitialConfigType = {
    namespace: "Agent-reply",
    theme,
    onError: console.error,
    nodes: [LinkNode, AutoLinkNode],
    editable: true,
    editorState: () => createEditorStateFromText(content),
  };

  return (
    <LexicalComposer initialConfig={initialConfig}>
      <ComposeAction onChange={onChange} onOpenUrl={onOpenUrl}>
        <PlainTextPlugin
          contentEditable={
            <ContentEditable className="focus:outline-blue-7 p-1 " />
          }
          ErrorBoundary={LexicalErrorBoundary}
        />
        <AutoLinkPlugin matchers={MATCHERS} />
      </ComposeAction>
    </LexicalComposer>
  );
};

export default Editor;
