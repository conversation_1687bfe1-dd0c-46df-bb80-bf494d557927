import { safeStringify } from "@/utils";
import qs from "qs";
import { z } from "zod";
import Logger from "./Logger";

const logger = new Logger("WebSocketManager");

const WsConfigSchema = z.object({
  query: z.optional(z.record(z.string())),
  port: z.number(),
  baseUrl: z
    .string()
    .refine((url) => url.startsWith("wss://") || url.startsWith("ws://"), {
      message: "url must start with wss:// or ws://",
    }),
  reconnectInterval: z.optional(z.number()),
  maxReconnectAttempts: z.optional(z.number()),
  heartbeatInterval: z.optional(z.number()),
  heartbeatTimeout: z.optional(z.number()),
});

interface WsConfig {
  query?: Record<string, string>;
  /** 端口号 */
  port: number;
  /** ws地址 */
  baseUrl: string;
  /** 重连间隔 */
  reconnectInterval?: number;
  /** 最大重连尝试次数 */
  maxReconnectAttempts?: number;
  /** 心跳间隔 */
  heartbeatInterval?: number;
  /** 心跳响应超时 */
  heartbeatTimeout?: number;
}

class WebSocketManager {
  private instance: WebSocket | undefined;
  private config: WsConfig | undefined;
  private heartbeatTimer: ReturnType<typeof setTimeout> | null = null;
  private lastHeartbeatTime = 0;
  private missedHeartbeats = 0;
  private reconnectAttempts = 0;
  private listeners: Map<
    keyof WebSocketEventMap,
    Set<(payload: unknown) => void>
  > = new Map();
  private intentionalClose = false;
  constructor(config: WsConfig) {
    this.init(config);
  }
  private init(config: WsConfig) {
    try {
      const { port, baseUrl, query } = WebSocketManager.validConfig(config);
      this.config = {
        reconnectInterval: 3000, // 默认重连间隔 3 秒
        maxReconnectAttempts: 5, // 最大重连尝试次数
        heartbeatInterval: 30000, // 默认 30 秒心跳
        heartbeatTimeout: 10000, // 默认 10 秒超时
        ...config,
      };
      let _query = "";
      if (query) {
        _query = qs.stringify(query, {
          arrayFormat: "comma",
        });
      }
      if (this.instance) {
        this.intentionalClose = true; // 标记即将执行的close()为主动关闭
        this.instance.close();
        this.intentionalClose = false; // 重置标志位
      }
      logger.info(`初始化 WebSocket 连接${baseUrl}:${port}`);
      this.instance = new WebSocket(
        `${baseUrl}:${port}${_query ? `?${_query}` : ""}`,
      );
      this.handleOpen(this.instance);
      this.handleError(this.instance);
      this.handleClose(this.instance);
      this.handleMessage(this.instance);
    } catch (e) {
      logger.warn("初始化 WebSocket 连接失败", e, config);
    }
  }

  static validConfig(config: any) {
    const result = WsConfigSchema.safeParse(config);
    if (result.success) {
      return result.data;
    }
    logger.error(result.error.message);
    throw new Error(result.error.message);
  }

  private handleOpen(instance: WebSocket) {
    instance.addEventListener("open", (event) => {
      // 重置重连计数
      logger.info("WebSocket 连接成功,readyState:", this.instance?.readyState);
      this.reconnectAttempts = 0;
      // 客户端没有心跳机制
      // this.startHeartbeat();
      const callbackSet = this.listeners.get("open");
      callbackSet?.forEach((cb) => cb(event));
    });
  }

  private handleError(instance: WebSocket) {
    instance.addEventListener("error", (event) => {
      logger.error(event);
      const callbackSet = this.listeners.get("error");
      callbackSet?.forEach((cb) => cb(event));
    });
  }
  private handleClose(instance: WebSocket) {
    instance.addEventListener("close", (event) => {
      logger.warn("WebSocket 连接关闭,readyState:", this.instance?.readyState);
      const callbackSet = this.listeners.get("close");
      callbackSet?.forEach((cb) => cb(event));
      // 只有非主动关闭且非正常关闭时才尝试重连
      if (!this.intentionalClose && !event.wasClean) {
        this.attemptReconnect();
      }
    });
  }

  private handleMessage(instance: WebSocket) {
    instance.addEventListener("message", (event) => {
      logger.info("WebSocket 消息接收:", JSON.parse(event.data));
      const callbackSet = this.listeners.get("message");

      callbackSet?.forEach((cb) => cb(JSON.parse(event.data)));
    });
  }

  private attemptReconnect() {
    if (!this.config?.maxReconnectAttempts) {
      this.reconnectAttempts++;
      logger.warn(`尝试重连 (${this.reconnectAttempts})`);
      setTimeout(() => {
        this.config && this.init(this.config);
      }, this.config?.reconnectInterval);
      return;
    }
    if (this.reconnectAttempts < this.config?.maxReconnectAttempts!) {
      this.reconnectAttempts++;
      logger.warn(
        `尝试重连 (${this.reconnectAttempts}/${this.config?.maxReconnectAttempts})`,
      );

      setTimeout(() => {
        this.config && this.init(this.config);
      }, this.config?.reconnectInterval);
    } else {
      logger.warn("达到最大重连次数，停止重试");
    }
  }

  // 启动心跳机制
  // @ts-ignore
  private startHeartbeat() {
    // 清理之前的心跳定时器
    this.stopHeartbeat();

    // 定期发送 ping 并检查连接状态
    this.heartbeatTimer = setInterval(() => {
      if (this.instance && this.instance.readyState === WebSocket.OPEN) {
        // 发送 ping 消息
        this.sendPing();

        // 检查上次心跳时间
        const now = Date.now();
        if (now - this.lastHeartbeatTime > this.config?.heartbeatTimeout!) {
          this.missedHeartbeats++;

          // 如果连续多次心跳失败，触发重连
          if (this.missedHeartbeats >= 3) {
            logger.warn("心跳检测失败，尝试重新连接");
            this.attemptReconnect();
          }
        }
      }
    }, this.config?.heartbeatInterval);
  }

  // 停止心跳机制
  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 发送 ping 消息
  private sendPing() {
    const pingMessage = {
      type: "ping",
      timestamp: Date.now(),
    };
    try {
      this.sendMessage(pingMessage);
    } catch (error) {
      logger.error("发送 ping 消息失败", error, pingMessage);
    }
  }

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  on(event: keyof WebSocketEventMap, callback: (payload: any) => void) {
    const callbackSet = this.listeners.get(event) || new Set();
    callbackSet.add(callback);
    this.listeners.set(event, callbackSet);
    return () => {
      this.listeners.get(event)?.delete(callback);
    };
  }

  public sendMessage(data: unknown) {
    if (this.instance && this.instance.readyState === WebSocket.OPEN) {
      try {
        // 判断数据类型并进行相应处理
        if (
          typeof data === "string" ||
          data instanceof ArrayBuffer ||
          data instanceof Blob ||
          ArrayBuffer.isView(data)
        ) {
          // 如果是允许的类型，直接发送
          this.instance.send(data);
        } else {
          // 非允许类型，尝试转换为字符串
          const stringData = safeStringify(data);
          logger.info(`[start send message]${stringData}`);
          this.instance.send(stringData);
          logger.info(`[end send message]${stringData}`);
        }
      } catch (error) {
        logger.error("WebSocket 消息发送失败", error, safeStringify(data));
      }
    } else {
      logger.warn("WebSocket 未连接，消息发送失败", safeStringify(data));
    }
  }

  getWsState() {
    return this.instance?.readyState;
  }
}

export default WebSocketManager;
