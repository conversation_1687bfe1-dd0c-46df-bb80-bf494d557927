import { configStore } from "@/business/components/ConfigProvider/store";
import useWebSocket from "@/business/hooks/useWebsocket";
import { platformClientMap } from "@/constant/client";
import type { SupportPlatform } from "@/constant/platform";
import {
  type ServiceAccountLocalInfo,
  WsCmdReceive,
  WsCmdSend,
  type WsReceiveMessage,
} from "@/constant/protocol";
import {
  type ServiceAccountRemoteInfo,
  getScanAccountStateByIdAndPlatform,
} from "@/network/api/customer";
import { compare } from "@/utils";
import { Platform } from "@workspace/business";
import { useDebounce, useLast } from "@workspace/ui";
import { useCallback, useEffect } from "react";
import { Outlet } from "react-router";
import { type ServiceAccount, serviceAccountStore } from "./store";

const getAccountId = (data: ServiceAccountLocalInfo) => {
  switch (data.platformType) {
    case Platform.TAOBAO:
    case Platform.PDD:
    case Platform.KUAISHOU:
      return data.account;
    case Platform.JD:
    case Platform.DOUDIAN:
      return `${data.shopId}:${data.account}`;
    default:
      return data.account;
  }
};

const ServiceProvider = () => {
  const socket = useWebSocket();

  const getSocket = useLast(socket);
  const getStore = useCallback(() => {
    return serviceAccountStore.getState();
  }, []);

  const updateAccountMap = useDebounce(
    async (
      accountMap: Record<
        SupportPlatform,
        (ServiceAccountLocalInfo & { sellerAccountForBackend: string })[]
      >,
    ) => {
      const params = Object.values(accountMap)
        .flat()
        .map((account) => {
          return {
            accountId: account.accountId || "",
            platform: account.platformType,
            sellerAccount: account.sellerAccountForBackend,
            nick: account.showAccount,
          };
        });
      const res = await getScanAccountStateByIdAndPlatform({
        list: params,
      }).promise.catch((error: Error) => error);
      if (res instanceof Error || !res.data.success) {
        getStore().setAccountMap(accountMap);
        return accountMap;
      }
      const sellerAccountMap = res.data.data.list.reduce(
        (acc, cur) =>
          Object.assign(acc, {
            [cur.sellerAccount]: cur,
          }),
        {} as Record<string, ServiceAccountRemoteInfo>,
      );
      const _data = Object.entries(accountMap).reduce(
        (acc, cur) => {
          const [key, value] = cur;
          return Object.assign(acc, {
            [key]: value.map((item) => ({
              ...item,
              ...sellerAccountMap[getAccountId(item)],
              sellerAccountForBackend: getAccountId(item),
              shopId: item.shopId,
            })),
          });
        },
        {} as Record<
          SupportPlatform,
          (ServiceAccount & { sellerAccountForBackend: string })[]
        >,
      );

      getStore().setAccountMap(_data);
      const notAutoLoginAccountMap = Object.assign(
        {},
        getStore().notAutoLoginAccountMap,
      );
      let ifNeedReHost = false;
      const hostingAccountMap: Record<string, boolean> = Object.assign(
        {},
        getStore().hostingAccountMap,
      );
      const needReHost: Record<string, string[]> = {};
      Object.entries(_data).forEach(([key, value]) => {
        const _key =
          platformClientMap[key as unknown as keyof typeof platformClientMap]
            .hostingKey;
        const list: string[] = [];
        // 当前平台客户端版本支持，且未托管，且未手动推出托管的
        value.forEach(
          (
            item: ServiceAccount & {
              sellerAccountForBackend: string;
            },
          ) => {
            if (
              item.versionSuit &&
              item.receptionIsOpen &&
              !item.ifLogin &&
              !notAutoLoginAccountMap[item.sellerAccountForBackend] &&
              item.status === 0
            ) {
              hostingAccountMap[item.sellerAccountForBackend] = true;
              ifNeedReHost = true;
              list.push(item.sellerAccountForBackend);
            }
          },
        );
        if (_key === platformClientMap[Platform.JD].hostingKey) {
          needReHost[_key] = list.map((item) => `${item}:0`); // 京东平台特殊标识
        } else {
          needReHost[_key] = list;
        }
      });
      if (ifNeedReHost && configStore.getState()?.base?.autoLogin) {
        getStore().setHostingAccountMap(hostingAccountMap);
        getSocket()?.sendMessage({
          cmd: WsCmdSend.HostingServiceAccount,
          data: needReHost,
        });
      }
    },
    500,
  );

  useEffect(() => {
    const socket = getSocket();
    if (!getStore()?.unsubscribe && socket) {
      const unsubscribe = socket?.on("message", (payload: WsReceiveMessage) => {
        switch (payload.cmd) {
          case WsCmdReceive.RefreshServiceAccountList:
          case WsCmdReceive.ServiceAccountList: {
            const data = {
              [Platform.TAOBAO]: payload.data.qnAccountList.map((item) => ({
                ...item,
                sellerAccountForBackend: getAccountId(item),
              })),
              [Platform.JD]: payload.data.jdAccountList.map((item) => ({
                ...item,
                sellerAccountForBackend: getAccountId(item),
              })),
              [Platform.DOUDIAN]: payload.data.dyAccountList.map((item) => ({
                ...item,
                sellerAccountForBackend: getAccountId(item),
              })),
              [Platform.PDD]: payload.data.pddAccountList.map((item) => ({
                ...item,
                sellerAccountForBackend: getAccountId(item),
              })),
              [Platform.KUAISHOU]: payload.data.ksAccountList.map((item) => ({
                ...item,
                sellerAccountForBackend: getAccountId(item),
              })),
              [Platform.DEWU]: payload.data.dewuAccountList.map((item) => ({
                ...item,
                sellerAccountForBackend: getAccountId(item),
              })),
            };
            if (compare(getStore().sourceAccountMap, data)) return;

            getStore().setSourceAccountMap(data);
            updateAccountMap(data);

            break;
          }
          case WsCmdReceive.UpdateServerAccountToken: {
            const { platformType } = payload.data;
            const accountList = getStore().accountMap[
              platformType as SupportPlatform
            ].map((item) => {
              if (
                item.account === payload.data.account &&
                item.shopId === payload.data.shopId
              ) {
                return {
                  ...item,
                  ...payload.data,
                };
              }
              return item;
            });
            getStore().setAccountMap({
              ...getStore().accountMap,
              [platformType]: accountList,
            });
            break;
          }

          default:
            break;
        }
      });

      getStore().setSubscribe(unsubscribe);
    }

    return getStore().unsubscribe;
  }, [getStore, getSocket, updateAccountMap]);

  return <Outlet context={socket} />;
};

export default ServiceProvider;
