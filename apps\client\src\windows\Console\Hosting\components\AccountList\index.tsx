import emptyImage from "@/assets/images/placeholder/empty-content.png";
import type { ServiceAccount } from "@/business/components/ServiceccountProvider/store";
import { getFullPath } from "@/utils/route";
import { Platform } from "@workspace/business";
import {
  Too<PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from "@workspace/ui";
import { Button, ConfigProvider, Table, type TableColumnsType } from "antd";
import { useNavigate, useSearchParams } from "react-router";
import HostingStatus from "./HostingStatus";

interface AccountListProps {
  data: (ServiceAccount & { isHosting: boolean })[];
  platformClient: { name: React.ReactNode };
  onCancelHosting: (record: ServiceAccount) => void;
  onHosting: (record: ServiceAccount) => void;
  onPullImage: (record: ServiceAccount) => void;
}

const getAccountId = (data: {
  platformType: Platform;
  account: string;
  shopId: string;
}) => {
  switch (data.platformType) {
    case Platform.TAOBAO:
    case Platform.PDD:
    case Platform.KUAISHOU:
      return data.account;
    case Platform.JD:
    case Platform.DOUDIAN:
      return `${data.shopId}:${data.account}`;
    default:
      return data.account;
  }
};

const AccountList = ({
  data,
  platformClient,
  onCancelHosting,
  onHosting,
  onPullImage,
}: AccountListProps) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const columns: TableColumnsType<ServiceAccount & { isHosting: boolean }> = [
    {
      title: "账号",
      dataIndex: "showAccount",
      width: 280,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 200,
      render: (status, record) => {
        return (
          <HostingStatus
            status={status}
            data={record}
            isHosting={record.isHosting}
          />
        );
      },
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 110,
      // TODO: 优化枚举值
      render: (_, record) => {
        if (Number(record.status) !== 0) return null;
        return (
          <div className="flex items-center gap-2">
            {!record.ifLogin ? (
              <Button
                type="link"
                className="h-auto p-0"
                disabled={record.isHosting || !record.versionSuit}
                onClick={() => {
                  onHosting(record);
                }}
              >
                托管
              </Button>
            ) : (
              <Button
                danger
                type="link"
                className="h-auto p-0"
                onClick={() => onCancelHosting(record)}
              >
                退出
              </Button>
            )}

            <Button
              type="link"
              onClick={() => {
                navigate(
                  getFullPath("/console/chat-agent-version", {
                    query: {
                      ...Object.fromEntries(searchParams),
                      account: getAccountId({
                        platformType: record.platform as Platform,
                        account: record.account,
                        shopId: record.shopId,
                      }),
                    },
                  }),
                );
              }}
            >
              版本管理
            </Button>
            {record.ifLogin && record.platform === Platform.JD && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="link"
                      className="h-auto p-0"
                      onClick={() => onPullImage(record)}
                    >
                      获取商品主图
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="bg-black-4">
                    <div className="text-14">
                      <p>获取该店铺在售商品SKU主图信息学习归纳到商品知识库。</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBg: "transparent",
          },
        },
      }}
    >
      <Table
        columns={columns}
        dataSource={data}
        rowKey={(data) =>
          data?.account + data?.platformAccountId + data?.showAccount
        }
        pagination={false}
        locale={{
          emptyText: (
            <div className="flex flex-col items-center gap-4">
              <img
                className="w-[240px] object-contain"
                src={emptyImage}
                alt=""
              />
              <p className="text-black-2">{`请启动${platformClient.name}客户端，并打开接待页面后尝试刷新列表`}</p>
            </div>
          ),
        }}
      />
    </ConfigProvider>
  );
};

export default AccountList;
