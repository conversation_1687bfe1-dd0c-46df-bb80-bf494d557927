import {
  type ShortcutListItem,
  WsCmdReceive,
  type WsMessage,
} from "@/constant/protocol/index";
import type { TopicContentType } from "@/network/api/copilot";
import { useEffect } from "react";
import { useStore } from "zustand";
import { createStore } from "zustand";
import useWebSocket from "./useWebsocket";
interface CopilotState {
  lastMsg: TopicContentType | undefined;
  inputMsg: TopicContentType | undefined;
  context: TopicContentType["focusInfo"] | undefined;
  shortcutNum: number | undefined;
  shortcutList: ShortcutListItem[];
  shortcutTrigger: number | undefined;
  setContext: (context: TopicContentType["focusInfo"] | undefined) => void;
  setLastMsg: (data: TopicContentType | undefined) => void;
  setShortcutList: (data: ShortcutListItem[]) => void;
  setShortcutTrigger: (data: number | undefined) => void;
  setInputMsg: (data: TopicContentType | undefined) => void;
}

export const copilotStore = createStore<CopilotState>((set) => ({
  lastMsg: undefined,
  inputMsg: undefined,
  context: undefined,
  shortcutNum: undefined,
  shortcutList: [],
  shortcutTrigger: undefined,
  setLastMsg: (data: TopicContentType | undefined) => {
    set((state) => ({ ...state, lastMsg: data }));
  },
  setContext: (context: TopicContentType["focusInfo"] | undefined) => {
    set((state) => ({ ...state, context: context }));
  },
  setShortcutList: (data: ShortcutListItem[]) => {
    set((state) => ({ ...state, shortcutList: data }));
  },
  setShortcutTrigger: (data: number | undefined) => {
    set((state) => ({ ...state, shortcutTrigger: data }));
  },
  setInputMsg: (data: TopicContentType | undefined) => {
    set((state) => ({ ...state, inputMsg: data }));
  },
}));

const useCopilot = () => {
  const socket = useWebSocket();
  const store = useStore(copilotStore);

  useEffect(() => {
    const unsubscribe = socket?.on("message", (data: WsMessage) => {
      if (data.cmd === WsCmdReceive.CopilotMsg) {
        store.setLastMsg(data.data as TopicContentType);
        store.setInputMsg(data.data as TopicContentType);
      }
      if (data.cmd === WsCmdReceive.QuickSendShortcut) {
        store.setShortcutList(data.data.shortcutList);
      }
      if (data.cmd === WsCmdReceive.QuickSendTrigger) {
        store.setShortcutTrigger(data.data.number);
      }
    });
    return unsubscribe;
  }, [store, socket]);

  return { socket, store };
};

export default useCopilot;
