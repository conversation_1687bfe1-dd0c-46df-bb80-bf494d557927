import useSession from "@/business/hooks/useSession";
import useRemote from "@/hooks/useRemote";
import {
  getAutoReceptionConfig,
  getDeepLearn,
  postAutoReceptionConfig,
  postDeepLearnConfig,
} from "@/network/api/copilot";
import { getMemoryPermission } from "@/network/api/memory";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  useDebounce,
  useThrottle,
} from "@workspace/ui";
import { Switch } from "antd";
import clsx from "clsx";
import { useState } from "react";
import { toast } from "sonner";
import Lightning from "~icons/icon-park-outline/lightning";
import BrainSolid from "~icons/icon-park-solid/brain";
import Lightbulb from "~icons/lucide/lightbulb";
import MemoryBottomSheet from "./MemoryBottomSheet";

const BuyerInfo = () => {
  const { buyer, serviceAccount } = useSession();
  const [key, setKey] = useState(new Date().getTime());
  const [deepLearn, setDeep<PERSON>earn] = useState(false);
  const [openMemory, setOpenMemory] = useState(false);

  const { data: memoryPermission } = useRemote(
    [getMemoryPermission.name],
    getMemoryPermission,
  );

  const { data } = useRemote(
    buyer?.buyerNick ? [getAutoReceptionConfig.name, buyer?.buyerNick] : null,
    ([, buyerNick]) => {
      return getAutoReceptionConfig({ buyerAccount: buyerNick });
    },
    {
      onSuccess() {
        setKey(new Date().getTime());
      },
    },
  );

  const { mutate: deepLearnMutate } = useRemote(
    [getDeepLearn.name, serviceAccount?.account],
    getDeepLearn,
    {
      onSuccess(data) {
        setDeepLearn(!!data);
      },
    },
  );

  const onChange = useDebounce(async (e) => {
    const res = await postAutoReceptionConfig({
      buyerAccount: buyer?.buyerNick as string,
      ifAuto: e,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error || !res.data.success) {
      setKey(new Date().getTime());
    } else {
      toast.success("设置成功");
    }
  }, 500);

  const handleClickDeepLearn = useDebounce(async (e) => {
    const res = await postDeepLearnConfig({
      ifOpen: e,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error || !res.data.success) {
      deepLearnMutate();
      return;
    }
    if (res.data.success) {
      toast.success(`已切换至${e ? "深度思考模式" : "快速应答模式"}`);
    }
  }, 500);

  const onModeChange = useThrottle(
    (e: boolean) => {
      setDeepLearn(e);
      handleClickDeepLearn(e);
    },
    300,
    { leading: true, trailing: false },
  );

  return (
    <div className="flex items-center justify-between">
      <div className="flex gap-2 items-center">
        {memoryPermission && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger
                asChild
                className="text-blue-7 text-16  cursor-pointer"
                onClick={() => setOpenMemory(true)}
              >
                <BrainSolid />
              </TooltipTrigger>
              <TooltipContent className="bg-black-4 ">
                <p>查看记忆</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        <span className="text-14 text-black-4 font-bold">
          {buyer?.buyerNick || ""}
        </span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Switch
                size="small"
                key={key}
                defaultChecked={!!data}
                onChange={onChange}
              />
            </TooltipTrigger>
            <TooltipContent className="bg-black-4 ">
              <div className="space-y-2">
                <p className="">关闭后，当前客户智能体停止自动发送，</p>
                <p>跟单和 CRM 营销停止发送</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger
            className={clsx(
              "w-[38px] overflow-hidden border border-gray-3  rounded-full transition-all",
              deepLearn
                ? "text-blue-7 hover:border-blue-7 bg-blue-1"
                : "text-yellow-7 hover:border-yellow-7 bg-yellow-1",
            )}
            onClick={() => onModeChange(!deepLearn)}
            onKeyDown={() => {}}
          >
            <div
              className={clsx(
                "flex items-center w-fit transition-transform ease-linear duration-300",
                deepLearn ? "translate-x-0" : "-translate-x-1/2",
              )}
            >
              <span className="flex justify-center w-[38px] py-1 px-2">
                <Lightbulb />
              </span>
              <span className="flex justify-center w-[38px] py-1 px-2">
                <Lightning />
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent className="bg-black-4">
            {deepLearn ? (
              <span>深度思考(beta): 慢一点，更省心</span>
            ) : (
              <span>快速应答</span>
            )}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <MemoryBottomSheet
        open={openMemory}
        onClose={() => setOpenMemory(false)}
      />
    </div>
  );
};

export default BuyerInfo;
