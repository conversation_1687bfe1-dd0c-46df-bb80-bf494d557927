import EmptyPlacehoderImage1 from "@/assets/images/placeholder/empty-1.png";
import EmptyPlacehoderImage2 from "@/assets/images/placeholder/empty-2.png";
import useSession from "@/business/hooks/useSession";
import { motion } from "framer-motion";
import useWebSocket from "../hooks/useWebsocket";

const contentMap = {
  Unmanaged: {
    key: "Unmanaged",
    title: "未托管",
    desc: "请完成托管",
  },
  Managed: {
    key: "Managed",
    title: "托管成功",
    desc: "请选择买家进行接待",
  },
  Init: {
    key: "Init",
    title: "托管成功",
    desc: "请选择买家进行接待",
  },
} as const;

const ContentGuards = ({ children }: { children: React.ReactNode }) => {
  const sockect = useWebSocket();
  const { buyer, serviceAccount, thirdShopId } = useSession();

  const goTo = (url: string) => {
    sockect?.sendMessage({
      cmd: "open_url",
      data: {
        url: url,
      },
    });
  };

  if (serviceAccount && !thirdShopId) {
    return (
      <div className="w-full mx-auto p-6 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen flex items-center justify-center">
        <div className="w-full space-y-6 flex flex-col items-center mb-5">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <img
              src={EmptyPlacehoderImage1}
              className="w-full max-w-[240px] object-contain"
              alt=""
            />
            <div className="text-gray-600">
              <p>当前店铺未购买授权应用</p>
              <p>
                <button
                  type="button"
                  className="text-blue-7 cursor-pointer px-2"
                  onClick={() => goTo(import.meta.env.VITE_AGENT_URL)}
                >
                  前往购买
                </button>
                授权即可使用
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  const ifInit = !buyer && !serviceAccount;
  // 只有在获得买家信息之后，才展示agent内容

  if (!buyer?.buyerNick) {
    const { title, desc } =
      contentMap[
        ifInit
          ? contentMap.Init.key
          : serviceAccount?.ifLogin
            ? contentMap.Managed.key
            : contentMap.Unmanaged.key
      ];
    return (
      <div className="w-full mx-auto p-6 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen flex items-center justify-center">
        <div className="w-full space-y-6 flex flex-col items-center mb-5">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <img
              src={EmptyPlacehoderImage2}
              className="w-full max-w-[240px] object-contain"
              alt=""
            />
            <h1 className="text-2xl font-bold text-gray-800 mb-2">{title}</h1>
            <p className="text-gray-600">{desc}</p>
          </motion.div>
        </div>
      </div>
    );
  }

  return children;
};
export default ContentGuards;
