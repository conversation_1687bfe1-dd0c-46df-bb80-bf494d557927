import { TabTypeMap } from "@/business/components/ReceptionProvider/store";
const tabOptions = [
  {
    label: "接待列表",
    value: TabTypeMap.reception,
  },
  {
    label: "星标列表",
    value: TabTypeMap.starMark,
  },
];

interface TabProps {
  onChange?: (values: string) => void;
  defaultValue?: string;
}

const TabSelect = ({ defaultValue, onChange }: TabProps) => {
  return (
    <div className="flex w-full">
      {tabOptions.map((mapItem, mapIndex) => (
        <div
          key={mapItem.value}
          className={`w-full cursor-pointer text-13 border border-gray-4 border-t-0 border-l-0 flex items-center justify-center py-2 px-4 ${defaultValue === mapItem.value ? "text-blue-7" : "text-black-3"} ${mapIndex % 2 !== 0 ? "border-r-0" : "border-r-[1px] border-[#CECECE]"} `}
          onClick={() => onChange?.(mapItem.value)}
          onKeyDown={() => {}}
        >
          {mapItem.label}
        </div>
      ))}
    </div>
  );
};
export default TabSelect;
