import type { AgentMessageType, CopilotMedia } from "@/network/api/copilot";

import { createStore } from "zustand";

export const ContentTypeMap = {
  /** 知识库" */
  KNOWLEDGE: "KNOWLEDGE",
  /** 聊天记录 */
  CHAT: "CHAT",
  /** 文档 */
  DOCUMENT: "DOCUMENT",
  /** 所有 */
  ALL: "ALL",
} as const;

type ConsultProductPayload = {
  product: {
    /** spu ID */
    spuId: string;
    /** 商品名称 */
    productName: string;
    /** 缩略图链接 */
    imageUrl: string;
    /** 详情链接 */
    detailUrl: string;
    /** sku 属性 */
    skuAttr: string;
    /** 商品卖点 */
    sellingPoints: string;
  };
  msgContents: {
    /** 0-文本 1-图片 2-视频 */
    type: 0 | 1 | 2;
    value: string;
  }[];
};

type ConsultReplyPayload = {
  originQuestion: string;
  // 主题名称
  topicName: string;
  trickList: {
    content: string[];
    referenceType: keyof typeof ContentTypeMap;
  }[];
  // 媒体文件
  medias: CopilotMedia[];
  extBody: {
    logIsCredible: number;
    topicCategory: number;
    riskRating: number;
  };
};
type PurchasePromptPayload = {
  reply: { content: string }[];
  spuId: string;
  skuId: string;
};

type PayloadMap = {
  CONSULT_PRODUCT: ConsultProductPayload;
  CONSULT_REPLY: ConsultReplyPayload;
  PURCHASE_PROMPT: PurchasePromptPayload;
  TOOL_AGENT: ConsultReplyPayload;
};

export type FormatedAgentMessageType<
  T extends AgentMessageType["type"] = AgentMessageType["type"],
> = {
  [K in T]: AgentMessageType & {
    type: K;
    parsedPayload: PayloadMap[K];
  };
}[T];

export type AgentShortcut = {
  reminderShortcut: string;
  groupSendShortcut: string;
  singleSendShortcut: string;
};

export interface AgentState {
  lastMsg: FormatedAgentMessageType | undefined;
  setLastMsg: (data: FormatedAgentMessageType | undefined) => void;
  unsubscribe: (() => void) | undefined;
  setSubscribe: (unsubscribe: (() => void) | undefined) => void;
  shortcut: AgentShortcut;
  setShortcut: (data: AgentShortcut) => void;
}

export const agentStore = createStore<AgentState>((set, get) => ({
  lastMsg: undefined,
  setLastMsg: (data: FormatedAgentMessageType | undefined) => {
    set({
      lastMsg: data,
    });
  },
  shortcut: {
    reminderShortcut: "",
    groupSendShortcut: "",
    singleSendShortcut: "",
  },
  setShortcut: (data: AgentShortcut) => {
    set({
      shortcut: data,
    });
  },
  unsubscribe: undefined,
  setSubscribe: (unsubscribe: (() => void) | undefined) =>
    set({
      unsubscribe: () => {
        unsubscribe?.();
        set({ unsubscribe: undefined });
      },
    }),
}));
