import {
  type ServiceAccount,
  serviceAccountStore,
} from "@/business/components/ServiceccountProvider/store";
import useWebSocket from "@/business/hooks/useWebsocket";
import type { SupportPlatform } from "@/constant/platform";
import {
  WsCmdReceive,
  WsCmdSend,
  type WsMessage,
} from "@/constant/protocol/index";
import { useLast } from "@workspace/ui";
import { useCallback, useEffect } from "react";
import { Outlet } from "react-router";
import { sessionStore } from "./store";

const SessionProvider = () => {
  const socket = useWebSocket();

  const getSocket = useLast(socket);

  const getStore = useCallback(() => {
    return sessionStore.getState();
  }, []);

  useEffect(() => {
    const socket = getSocket();
    if (!getStore()?.unsubscribe && socket) {
      const unsubscribe = socket?.on("message", (payload: WsMessage) => {
        switch (payload.cmd) {
          case WsCmdReceive.ProductChange: {
            sessionStore.setState({
              productInfo: payload.data,
            });
            break;
          }

          case WsCmdReceive.BuyerChange: {
            const {
              account,
              receptionIsOpen,
              ifLogin,
              currentBuyerNick,
              display,
              token,
              showAccount,
              showCurrentBuyerNick,
            } = payload.data.changeBuyer;

            const current = sessionStore.getState();
            // 空列表新买家进线时，会触发一次切换客服的事件，需要过滤掉
            if (
              current.serviceAccount &&
              account !== current.serviceAccount.account
            )
              return;

            // 切换买家时更新tanyu-group的会话信息
            const accountMap = JSON.parse(
              localStorage.getItem("accountMap") || "{}",
            ) as Record<SupportPlatform, ServiceAccount[]>;
            const target = accountMap[payload.data.platformType].find(
              (item) => item.sellerAccountForBackend === account,
            );
            serviceAccountStore.getState().asyncUpdateTanyuGroupSessions({
              thirdShopId: target?.thirdShopId as string,
              platForm: payload.data.platformType,
              groupId: target?.groupId as string,
            });
            sessionStore.setState({
              serviceAccount: {
                account: account,
                receptionIsOpen,
                ifLogin,
              },
              buyer: {
                showCurrentBuyerNick,
                buyerNick: currentBuyerNick,
                display,
              },
              token,
              platformType: payload.data.platformType,
              thirdShopId: target?.thirdShopId,
              groupId: target?.groupId as string,
            });
            socket?.sendMessage({
              cmd: WsCmdSend.SetTitle,
              data: { title: showAccount },
            });
            break;
          }
          case WsCmdReceive.ServiceAccountChange: {
            const {
              account,
              receptionIsOpen,
              ifLogin,
              currentBuyerNick,
              display,
              token,
              showAccount,
              showCurrentBuyerNick,
            } = payload.data.currentAccount;

            // 切换客服时更新tanyu-group的会话信息
            const accountMap = JSON.parse(
              localStorage.getItem("accountMap") || "{}",
            ) as Record<SupportPlatform, ServiceAccount[]>;
            const target = accountMap[payload.data.platformType].find(
              (item) => item.sellerAccountForBackend === account,
            );
            serviceAccountStore.getState().asyncUpdateTanyuGroupSessions({
              thirdShopId: target?.thirdShopId as string,
              platForm: payload.data.platformType,
              groupId: target?.groupId as string,
            });
            sessionStore.setState({
              serviceAccount: {
                account,
                receptionIsOpen,
                ifLogin,
              },
              buyer: {
                showCurrentBuyerNick,
                buyerNick: currentBuyerNick,
                display,
              },
              token,
              platformType: payload.data.platformType,
              thirdShopId: target?.thirdShopId,
              groupId: target?.groupId as string,
            });
            socket?.sendMessage({
              cmd: WsCmdSend.SetTitle,
              data: { title: showAccount },
            });
            break;
          }
          case WsCmdReceive.UpdateServerAccountToken: {
            const { platformType, token, account } = payload.data;
            const { serviceAccount, platformType: platform } =
              sessionStore.getState();
            if (
              serviceAccount?.account === account &&
              platform === platformType
            ) {
              sessionStore.setState({
                token,
              });
            }
            break;
          }

          default:
            break;
        }
      });
      getStore().setSubscribe(unsubscribe);
    }

    return getStore().unsubscribe;
  }, [getStore, getSocket]);

  return <Outlet context={socket} />;
};

export default SessionProvider;
