import empty from "@/assets/svg/robot-empty.svg";
import SKUIcon from "@/assets/svg/sku.svg?react";
import useWebSocket from "@/business/hooks/useWebsocket";
import { WsCmdSend } from "@/constant/protocol";
import { sendMessages } from "@/network/api/base";
import type { IProductItem } from "@/network/api/product";
import { useResizeObserver } from "@mantine/hooks";
import { Button, Divider, Popover, Tag, Typography } from "antd";
import clsx from "clsx";
import { useContext, useMemo, useState } from "react";
import { toast } from "sonner";
import DeleteOutlined from "~icons/ant-design/delete-outlined";
import EditOutlined from "~icons/ant-design/edit-outlined";
import PlusOutlined from "~icons/ant-design/plus-outlined";
import SendOutlined from "~icons/icon-park-outline/send";
import { ProductContext } from "../context";
import Image from "./Image";

interface ProductDetailProps {
  data?: IProductItem;
  buyerNick: string;
}

type ArrtInfo = NonNullable<IProductItem["userAttrs"]>[number];

const ProductDetail = ({ data, buyerNick }: ProductDetailProps) => {
  const socket = useWebSocket();
  const { onEditArrt, openSKU, delectAttr } = useContext(ProductContext);
  const [isCompose, setIsCompose] = useState(true);
  const [tagsRef, rect] = useResizeObserver();
  //浏览器打开商品详情页
  const openDetail = (url: string) => {
    if (url) {
      socket?.sendMessage({
        cmd: WsCmdSend.OpenUrl,
        data: { url },
      });
    }
  };

  //发送属性
  const sendAttrContent = async (
    event: React.MouseEvent<HTMLSpanElement, MouseEvent>,
    attr: ArrtInfo,
  ) => {
    // 获取触发事件的元素
    const clickedElement = event.target as HTMLSpanElement;
    //是否是自己触发的
    const oneself = clickedElement?.classList?.contains("ant-tag");
    if (oneself) {
      const postData = {
        content: attr.value ? attr.value : "",
        buyerAccount: buyerNick,
        imageUrls: attr.attrPic ? [attr.attrPic] : [],
      };
      const res = await sendMessages(postData).promise.catch((error) => error);
      if (res instanceof Error) return;
      if (res.data.success) {
        toast.success("操作成功");
      }
    }
  };

  const sendUrl = async (data: { value: string }) => {
    // 获取触发事件的元素
    const postData = {
      content: data.value ? data.value : "",
      buyerAccount: buyerNick,
      imageUrl: "",
    };
    const res = await sendMessages(postData).promise.catch((error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
    }
  };

  const handleCompose = () => {
    setIsCompose((state) => !state);
  };

  const attrs = useMemo(() => {
    if (!data) return [];

    return [
      ...(data?.userAttrs?.map((item) => ({
        ...item,
        clssName: "",
        overlayClassName:
          "max-w-[calc(100vw-45px)]  [&_.ant-popover-title]:border-0 [&_.ant-popover-title]:border-b [&_.ant-popover-title]:border-gray-2 [&_.ant-popover-title]:border-solid",
        content: (
          <div className="max-h-[300px] overflow-auto">
            <div>{item.value}</div>
            {item?.attrPic && (
              <img
                loading="lazy"
                className="w-full block object-contain"
                src={item.attrPic}
                alt=""
              />
            )}
          </div>
        ),
        title: (
          <div className="flex items-center gap-2">
            <div className="mr-auto">{item.name}</div>
            <Button
              type="link"
              className="h-auto p-0"
              onClick={() => onEditArrt({ ...item, productId: data.productId })}
            >
              <EditOutlined />
            </Button>
            <Button danger type="link" className="h-auto p-0">
              <DeleteOutlined onClick={() => delectAttr(item.proAttrId)} />
            </Button>
          </div>
        ),
        _tagColor: "gold",
      })) || []),

      // 商品属性
      ...(data?.productAttrs?.map((item) => ({
        ...item,
        title: item.name,
        content: item.value,
        _tagColor: "blue",
        overlayClassName:
          "max-w-[calc(100vw-45px)]  [&_.ant-popover-title]:border-0 [&_.ant-popover-title]:border-b [&_.ant-popover-title]:border-gray-2 [&_.ant-popover-title]:border-solid",
        clssName: "",
      })) || []),
      // 后台商品自定义属性、卖点
      ...(data?.customAttrs?.map((item) => ({
        ...item,
        title: item.name,
        content: item.value,
        _tagColor: "green",
        overlayClassName:
          "max-w-[calc(100vw-45px)]  [&_.ant-popover-title]:border-0 [&_.ant-popover-title]:border-b [&_.ant-popover-title]:border-gray-2 [&_.ant-popover-title]:border-solid",
        clssName: "",
      })) || []),
    ];
  }, [data, onEditArrt, delectAttr]);

  const { dynaimcAttrs, showButton } = useMemo(() => {
    if (!tagsRef.current) return { dynaimcAttrs: attrs, showButton: false };
    let targetWidth = (rect.width - 16) * 2 - 30 - 68;
    const items = Array.from((tagsRef.current as HTMLDivElement).children).map(
      (element) => {
        return {
          width: (element as HTMLSpanElement).offsetWidth + 8,
        };
      },
    );
    let index = 0;
    for (const item of items) {
      targetWidth = targetWidth - item.width;
      if (targetWidth > 0) {
        index += 1;
      } else {
        break;
      }
    }
    return {
      dynaimcAttrs: attrs.map((item, i) => ({
        ...item,
        clssName: isCompose && i >= index ? "hidden" : "block",
      })),
      showButton: index < attrs.length - 1,
    };
  }, [attrs, rect, tagsRef, isCompose]);

  if (!data)
    return (
      <div
        className={clsx(
          "items-center justify-center flex-col w-full h-[192px] flex",
        )}
      >
        <img className="w-[240px]" src={empty} alt="" />
        <div className="text-14 text-black-2">暂无数据</div>
      </div>
    );
  return (
    <div className="bg-gray-2 border rounded-[2px] border-gray-4 !-mt-[1px] p-2 border-solid">
      {/* 头部商品简介 */}
      <div className="flex gap-1 h-16">
        <img
          src={data.pic}
          alt=""
          className="w-16 h-16 block object-contain rounded-4 flex-shrink-0"
        />
        <div className="w-0 flex-1 flex flex-col">
          <Typography.Paragraph
            ellipsis={{
              rows: 2,
              tooltip: data.name,
            }}
            className="text-12 text-black-4 !mb-0 cursor-pointer"
            onClick={() => {
              openDetail(data.url);
            }}
          >
            {data.name || ""}
          </Typography.Paragraph>
          <div className="flex items-center justify-end mt-auto gap-[6px]">
            <button
              type="button"
              className="bg-transparent outline-none border-none cursor-pointer p-[3px] flex item-center justify-center"
            >
              <SendOutlined
                className="text-black-3"
                onClick={() => sendUrl({ value: data.url })}
              />
            </button>
            <button
              type="button"
              className="bg-transparent outline-none border-none cursor-pointer p-[3px] flex item-center justify-center"
              onClick={() => openSKU(data)}
            >
              <SKUIcon />
            </button>
          </div>
        </div>
      </div>
      <Divider type="horizontal" className="my-2" />
      {/* 商品属性 */}
      <div className="shop-attribute">
        <div className="text-12  text-black-4 mb-2">商品属性</div>
        <div className="flex items-center flex-wrap gap-2" ref={tagsRef}>
          <button
            type="button"
            onClick={() => {
              onEditArrt?.({ productId: data.productId });
            }}
            className="bg-white border-dashed border border-gray-4 flex items-center gap-[2px] cursor-pointer px-2 rounded-4 outline-none w-fit"
          >
            <PlusOutlined className="text-10" />
            <span className="text-12">属性</span>
          </button>
          {dynaimcAttrs.map((attr) => (
            <Popover
              key={attr.code}
              title={attr.title}
              content={attr.content}
              overlayClassName={attr.overlayClassName}
            >
              <Tag
                className={clsx("mr-0 cursor-pointer", attr.clssName)}
                color={attr._tagColor}
                onClick={(e) => sendAttrContent(e, attr as ArrtInfo)}
              >
                {attr.name}
              </Tag>
            </Popover>
          ))}
          {showButton && (
            <Button
              type="link"
              className="h-auto p-0 ml-auto"
              onClick={handleCompose}
            >
              {isCompose ? "更多" : "折叠"}
            </Button>
          )}
        </div>
      </div>
      <Divider type="horizontal" className="my-2" />
      {/* 商品详情图片 */}
      <div>
        <div className="text-12  text-black-4 mb-2">商品详情</div>
        <div className="flex flex-wrap gap-[6px]">
          {data?.itemDetailImgs?.map((url) => (
            <Image key={url} url={url} buyerNick={buyerNick} />
          ))}
          <div
            className={clsx(
              "items-center justify-center flex-col w-full h-[192px]",
              data?.itemDetailImgs?.length ? "hidden" : "flex",
            )}
          >
            <img className="w-[240px]" src={empty} alt="" />
            <div className="text-14 text-black-2">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
