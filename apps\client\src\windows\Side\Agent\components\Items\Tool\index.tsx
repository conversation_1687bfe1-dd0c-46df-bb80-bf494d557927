import type { FormatedAgentMessageType } from "@/business/components/AgentProvider/store";
import { formatTime } from "../../../utils";
import OrderRemark from "./OrderRemark";

interface ToolProps {
  data: FormatedAgentMessageType<"TOOL_AGENT">;
}

const Tool = ({ data }: ToolProps) => {
  return (
    <div className="overflow-hidden bg-white rounded-[6px] relative">
      <div className="px-2 pt-1 pb-0.5 text-12 flex items-center bg-gradient-to-b from-teal-50 to-white">
        <span className="text-teal-400 flex-shrink-0 font-medium">
          工具Agent
        </span>
        <span className="text-black-2 ml-2">{formatTime(data.time)}</span>
      </div>
      <OrderRemark data={data} />
    </div>
  );
};

export default Tool;
