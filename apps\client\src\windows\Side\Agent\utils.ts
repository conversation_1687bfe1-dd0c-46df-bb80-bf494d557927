import {
  ContentTypeMap,
  type FormatedAgentMessageType,
} from "@/business/components/AgentProvider/store";
import { type BatchSendMessageData, MsgContentMap } from "@/network/api/base";
import { AgentCardTypeMap, type AgentMessageType } from "@/network/api/copilot";
import { trimNewlines } from "@/utils";
import dayjs from "dayjs";

export const formatTime = (time: number) => {
  const isValidTime = time && dayjs(Number(time)).isValid();
  const isYesterDay =
    isValidTime &&
    dayjs(Number(time)).isBefore(dayjs().startOf("day")) &&
    dayjs(Number(time)).isAfter(dayjs().subtract(1, "day").startOf("day"));
  const isBeforeYesterDay =
    isValidTime &&
    dayjs(Number(time)).isBefore(dayjs().subtract(1, "day").startOf("day"));
  return isValidTime
    ? isYesterDay
      ? "昨天"
      : isBeforeYesterDay
        ? dayjs(Number(time)).format("M月D日")
        : dayjs(Number(time)).format("HH:mm")
    : "";
};

export interface MsgContentPreprocessConfig<
  T extends AgentMessageType["type"],
> {
  getMsgContent: (
    data: FormatedAgentMessageType<T>,
    index?: number,
  ) => BatchSendMessageData["msgContents"];

  getSendPayload: (
    items: BatchSendMessageData["msgContents"],
    data: FormatedAgentMessageType<T>,
  ) => BatchSendMessageData;
}

export type MsgContentPreprocessType = {
  [K in AgentMessageType["type"]]: MsgContentPreprocessConfig<K>;
};

export const msgContentPreprocess: MsgContentPreprocessType = {
  [AgentCardTypeMap.PURCHASE_PROMPT.value]: {
    getMsgContent: (
      data: FormatedAgentMessageType<"PURCHASE_PROMPT">,
      index?: number,
    ) => {
      if (!data) return [];
      const msgContents =
        data.parsedPayload?.reply?.map((item) => ({
          value: trimNewlines(item.content),
          type: MsgContentMap.text,
        })) || [];
      if (typeof index !== "number") return msgContents;
      return [msgContents[index]];
    },
    getSendPayload: (
      items: BatchSendMessageData["msgContents"],
      data: FormatedAgentMessageType<"PURCHASE_PROMPT">,
    ) => {
      let editSend = undefined;
      const sourceContents = data.parsedPayload.reply.map(
        (item: { content: string }) => item.content,
      );
      items.forEach((item) => {
        if (
          item.type === MsgContentMap.text &&
          !sourceContents.includes(item.value)
        ) {
          editSend = "1";
        }
      });
      return {
        msgContents: items,
        feature: {
          // agent相关
          agent: "1",
          sidebar: "1",
          editSend,
          agentTopicId: data.id,
        },
        payload: JSON.stringify(data.parsedPayload),
        cardId: data.id,
        buyerAccount: data.buyerAccount,
      };
    },
  },
  [AgentCardTypeMap.CONSULT_REPLY.value]: {
    getMsgContent: (
      data: FormatedAgentMessageType<"CONSULT_REPLY">,
      index?: number,
    ) => {
      if (!data) return [];
      const contents = (data.parsedPayload?.trickList || [])
        ?.filter((item) => item.referenceType === ContentTypeMap.CHAT)
        ?.flatMap((item) => item?.content?.map((str) => trimNewlines(str)))
        ?.filter(Boolean);
      const msgContents = [
        ...contents.map((item) => ({
          type: MsgContentMap.text,
          value: item,
        })),
        ...(data.parsedPayload.medias || [])
          .map((item) => {
            switch (item.copilotMediaType) {
              case "pic":
                return { type: MsgContentMap.image, value: item.url };
              case "video":
                return { type: MsgContentMap.video, value: item.url };
              case "link":
                return { type: MsgContentMap.text, value: item.url };
              default:
                break;
            }
          })
          .filter(Boolean),
      ] as BatchSendMessageData["msgContents"];
      if (typeof index !== "number") return msgContents;
      return [msgContents[index]];
    },
    getSendPayload: (
      items: BatchSendMessageData["msgContents"],
      data: FormatedAgentMessageType<"CONSULT_REPLY">,
    ) => {
      let editSend = undefined;

      const sourceContents = (data.parsedPayload?.trickList || [])
        ?.filter(
          (item: { referenceType: keyof typeof ContentTypeMap }) =>
            item.referenceType === ContentTypeMap.CHAT,
        )
        ?.flatMap((item: { content: string[] }) =>
          item?.content?.map((str: string) => trimNewlines(str)),
        )
        ?.filter(Boolean);

      items.forEach((item) => {
        if (
          item.type === MsgContentMap.text &&
          !sourceContents.includes(item.value)
        ) {
          editSend = "1";
        }
      });
      return {
        msgContents: items,
        feature: {
          // agent相关
          agent: "1",
          sidebar: "1",
          editSend,
          agentTopicId: data.id,
          agentTopic: data.parsedPayload.topicName,
          logIsCredible: data.parsedPayload.extBody.logIsCredible,
        },
        payload: JSON.stringify(data.parsedPayload),
        cardId: data.id,
        buyerAccount: data.buyerAccount,
      };
    },
  },
  [AgentCardTypeMap.CONSULT_PRODUCT.value]: {
    getMsgContent: (
      data: FormatedAgentMessageType<"CONSULT_PRODUCT">,
      index?: number,
    ) => {
      if (!data) return [];
      const msgContents =
        data?.parsedPayload?.msgContents
          ?.filter((item) => item.type === 0)
          ?.map((item) => ({
            value: trimNewlines(item.value),
            type: MsgContentMap.text,
          })) || [];
      if (typeof index !== "number") return msgContents;
      return [msgContents[index]];
    },
    getSendPayload: (
      msgContents: BatchSendMessageData["msgContents"],
      data: FormatedAgentMessageType<"CONSULT_PRODUCT">,
    ) => {
      const sellingPoints = data?.parsedPayload?.msgContents
        ?.filter((item) => item.type === 0)
        ?.map((item) => trimNewlines(item.value));
      const sourceSellingPoints = data?.parsedPayload?.msgContents
        ?.filter((item: { type: number; value: string }) => item.type === 0)
        ?.map((item: { value: string }) => trimNewlines(item.value));

      return {
        msgContents,
        feature: {
          agent: "1",
          sidebar: "1",
          editSend: sellingPoints !== sourceSellingPoints ? "1" : undefined,
          agentTopicId: data.id,
        },
        payload: JSON.stringify({
          ...data.parsedPayload,
          sellingPoints,
        }),
        cardId: data.id,
        buyerAccount: data.buyerAccount,
      };
    },
  },
};
