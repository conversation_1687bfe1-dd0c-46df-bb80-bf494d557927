import type { FormatedAgentMessageType } from "@/business/components/AgentProvider/store";
import useAgent from "@/business/hooks/useAgent";
import useSession from "@/business/hooks/useSession";
import useWebSocket from "@/business/hooks/useWebsocket";
import {
  type MessageToPreviewResource,
  WsCmdReceive,
  WsCmdSend,
  type WsReceiveMessage,
} from "@/constant/protocol";
import {
  type BatchSendMessageData,
  batchSendMessages,
} from "@/network/api/base";
import {
  AgentCardTypeMap,
  AgentMessageStatusMap,
  postUrgeOrderMessage,
} from "@/network/api/copilot";
import logger from "@/utils/log";
import { useDebounce, useLast, usePartialState } from "@workspace/ui";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui";
import clsx from "clsx";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import LoadingFourOutlined from "~icons/icon-park-outline/loading-four";
import BuyerInfo from "./components/BuyerInfo";
import FocusProduct from "./components/FocusProduct";
import MessageList from "./components/MessageList";
import AgentContext, { type AgentState } from "./context";
import { type MsgContentPreprocessConfig, msgContentPreprocess } from "./utils";

// 左闭右开
const createShortcutIndexMap = (msgList: FormatedAgentMessageType[]) => {
  const shortcutIndexMap = new Map();
  let items = [0, 0];

  for (const item of msgList) {
    let num = 0;
    if (item.type === AgentCardTypeMap.CONSULT_PRODUCT.value) {
      num = item?.parsedPayload?.msgContents?.length || 0;
    } else if (item.type === AgentCardTypeMap.CONSULT_REPLY.value) {
      num =
        (item?.parsedPayload?.trickList?.flatMap((item) => item?.content)
          ?.length || 0) + (item?.parsedPayload?.medias?.length || 0);
    } else if (item.type === AgentCardTypeMap.PURCHASE_PROMPT.value) {
      num = item?.parsedPayload?.reply?.length || 0;
    }
    if (num) {
      const startIndex = items[1] === 0 ? 1 : items[1];
      items = [startIndex, startIndex + num];
      shortcutIndexMap.set(item.id, items);
    }
    if (items[1] >= 9) {
      break;
    }
  }

  return shortcutIndexMap;
};

export const Component: React.FC = () => {
  const socket = useWebSocket();
  const [state, setState] = usePartialState<AgentState>({
    settingOpen: false,
    topic: undefined,
  });

  const {
    store: { lastMsg, shortcut },
  } = useAgent();
  const [urging, setUrging] = useState(false);
  const [shortcutIndexMap, setShortcutIndexMap] = useState<
    Map<string, [number, number]>
  >(new Map<string, [number, number]>());
  const msgListRef = useRef<FormatedAgentMessageType[]>([]);
  const getShortcutIndexMap = useLast(shortcutIndexMap);

  const { buyer, serviceAccount, platformType, thirdShopId } = useSession();
  const urgeCache = useRef<{
    buyerAccount: string;
    thirdShopId: string;
    sellerAccount: string;
  }>();
  const getPlatformType = useLast(platformType);
  const sendMessages = useCallback(async (payload: BatchSendMessageData) => {
    if (!payload.buyerAccount) {
      toast.warning("买家信息不能为空");
      return Promise.resolve(false);
    }
    if (!payload.msgContents.length) {
      toast.warning("发送内容不能为空");
      return Promise.resolve(false);
    }
    const res = await batchSendMessages({
      ...payload,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error) return false;
    if (res.data.success) {
      toast.success("发送成功");
      return true;
    }
    return false;
  }, []);

  const updateShortcutIndexMap = useCallback(
    (data: FormatedAgentMessageType[]) => {
      const result = createShortcutIndexMap(data);
      msgListRef.current = data;
      setShortcutIndexMap(result);
    },
    [],
  );
  const updateMsg = useCallback((data: FormatedAgentMessageType) => {
    const index = msgListRef.current.findIndex((item) => item.id === data.id);
    if (index === -1) return;
    msgListRef.current[index] = data;
  }, []);
  const value = {
    sendMessages,
    preview: (data: MessageToPreviewResource["data"]) => {
      socket?.sendMessage({
        cmd: WsCmdSend.PreviewResource,
        data,
      });
    },
    insert: (content: string) => {
      if (!content) return;
      const _data = {
        cmd: WsCmdSend.InsertTextToPlatformClient,
        data: {
          content,
          platformType: getPlatformType(),
        },
      } as const;
      socket?.sendMessage(_data);
    },
    state,
    setState,
    shortcutIndexMap,
    updateShortcutIndexMap,
    updateMsg,
  };

  const handleUrge = useDebounce(
    async () => {
      if (!thirdShopId) {
        toast.warning("店铺id不能为空");
        return;
      }
      if (!buyer?.buyerNick) {
        toast.warning("买家信息不能为空");
        return;
      }
      if (!serviceAccount?.account) {
        toast.warning("客服信息不能为空");
        return;
      }
      const params = {
        buyerAccount: buyer?.buyerNick,
        thirdShopId,
        sellerAccount: serviceAccount?.account,
      };
      const isSameParams = !urgeCache.current
        ? false
        : JSON.stringify(urgeCache.current) === JSON.stringify(params);
      if (
        lastMsg?.type === AgentCardTypeMap.PURCHASE_PROMPT.value &&
        lastMsg.status === AgentMessageStatusMap.PROCESSING.value &&
        isSameParams
      ) {
        toast.warning("正在努力生成中....");
        return;
      }
      setUrging(true);

      urgeCache.current = params;
      const res = await postUrgeOrderMessage(params).promise.catch(
        (error: Error) => error,
      );
      setUrging(false);
      if (res instanceof Error) return;
      if (res.data.success) {
        toast.success("操作成功");
      }
    },
    500,
    { leading: true, trailing: false },
  );

  useEffect(() => {
    return socket?.on("message", (data: WsReceiveMessage) => {
      if (data.cmd === WsCmdReceive.ReminderTrigger) {
        handleUrge();
      } else if (data.cmd === WsCmdReceive.CardMsgTrigger) {
        try {
          const index = data.data.number - 1;
          if (index < 0) {
            logger.info("WsCmdReceive.CardMsgTrigger.inValidIndex", data);
            return;
          }
          const msg = msgListRef.current[index];
          const ob = msgContentPreprocess[
            msg?.type
          ] as MsgContentPreprocessConfig<typeof msg.type>;
          const contents = ob.getMsgContent(msg);
          const payload = ob.getSendPayload(contents, msg);
          logger.info("WsCmdReceive.CardMsgTrigger.sendMessages", payload);
          sendMessages(payload as BatchSendMessageData);
        } catch (e) {
          logger.warn("WsCmdReceive.CardMsgTrigger", e);
        }
      } else if (data.cmd === WsCmdReceive.SingleMsgTrigger) {
        try {
          const [key, numbers] =
            Array.from(getShortcutIndexMap().entries()).find(
              ([, value]) =>
                value[0] <= data.data.number && value[1] > data.data.number,
            ) || [];

          const msg = msgListRef.current.find((item) => item.id === key);
          if (!msg || !numbers) {
            logger.warn(
              "WsCmdReceive.SingleMsgTrigger",
              "msg or numbers is undefined",
              {
                msgListRefCurrent: msgListRef.current,
                shortcutIndexMap: Array.from(getShortcutIndexMap().entries()),
                data,
              },
            );
            return;
          }
          const index = data.data.number - numbers[0];
          const ob = msgContentPreprocess[
            msg.type
          ] as MsgContentPreprocessConfig<typeof msg.type>;
          const contents = ob.getMsgContent(msg, index);
          const payload = ob.getSendPayload(contents, msg);
          logger.info("WsCmdReceive.SingleMsgTrigger.sendMessages", payload);
          sendMessages(payload as BatchSendMessageData);
        } catch (e) {
          logger.warn("WsCmdReceive.SingleMsgTrigger", e);
        }
      }
    });
  }, [socket, handleUrge, getShortcutIndexMap, sendMessages]);

  return (
    <AgentContext.Provider value={value}>
      <div className="bg-gray-3 flex flex-col min-h-0 flex-1 w-full overflow-hidden">
        <div className="p-2 bg-white relative z-10 border-b border-gray-100 shadow-sm space-y-2 rounded-b-4">
          <BuyerInfo />
          <FocusProduct />
        </div>
        <MessageList />
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              onClick={handleUrge}
              className="fixed bottom-2 right-2 hover:bg-blue-6 transition-all text-white bg-blue-7   px-2.5 py-2.5 min-w-10 h-10  rounded-full flex items-center justify-center gap-2 shadow-lg"
            >
              {urging ? (
                <span
                  className={clsx(
                    "flex items-center justify-center  transition-opacity opacity-100",
                  )}
                >
                  <LoadingFourOutlined className="animate-spin" />
                </span>
              ) : (
                <span
                  className={clsx(
                    "transition-opacity inline-flex items-center gap-2",
                  )}
                >
                  <span>催</span>
                  {shortcut?.reminderShortcut ? (
                    <kbd className="inline-flex items-center  text-xs font-sans font-semibold text-gray-100  rounded-md">
                      {shortcut?.reminderShortcut}
                    </kbd>
                  ) : null}
                </span>
              )}
            </TooltipTrigger>
            <TooltipContent className="bg-black-4">
              <span>主动出击推动客户下单</span>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </AgentContext.Provider>
  );
};

Component.displayName = "Agent";
