import TextLogo from "@/assets/logo/tanyu-text.svg?react";
import IconLogo from "@/assets/logo/tanyu.svg?react";
import { ConfigProvider, Tabs } from "antd";
import PasswordLogin from "./components/PasswordLogin";
// import PhoneLogin from "./components/PhoneLogin";
import WechatLogin from "./components/WechatLogin";

const items = [
  // { key: "phone", label: "手机登录", children: <PhoneLogin /> },
  { key: "wechat", label: "微信登录", children: <WechatLogin /> },
  { key: "account", label: "帐号登录", children: <PasswordLogin /> },
];

export const Component: React.FC = () => {
  return (
    <div className="flex flex-col items-center gap-[30px] w-[460px]  ">
      <div className="flex items-center gap-2">
        <IconLogo />
        <TextLogo />
      </div>
      <ConfigProvider
        theme={{
          components: {
            Form: {
              itemMarginBottom: 16,
            },
          },
        }}
      >
        <Tabs
          centered
          className="[&_.ant-tabs-nav:before]:hidden [&_.ant-tabs-tab]:font-medium [&_.ant-tabs-tab]:text-20"
          items={items}
        />
      </ConfigProvider>
    </div>
  );
};
