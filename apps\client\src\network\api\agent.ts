import client from "@/network/client";
import type { APIResponse } from "@workspace/types";

export enum ChatAgentChannel {
  DEV = 0, // 开发
  STABLE = 1, // 稳定
}

export const ChatAgentChannelMap = {
  [ChatAgentChannel.DEV]: {
    label: "开发版",
    value: ChatAgentChannel.DEV,
  },
  [ChatAgentChannel.STABLE]: {
    label: "稳定版",
    value: ChatAgentChannel.STABLE,
  },
} as const;

export enum ChatAgentStatus {
  DISABLED = 0, // 禁用
  ENABLED = 1, // 可用
}

export const ChatAgentStatusMap = {
  [ChatAgentStatus.DISABLED]: {
    label: "已下线",
    value: ChatAgentStatus.DISABLED,
  },
  [ChatAgentStatus.ENABLED]: {
    label: "可用",
    value: ChatAgentStatus.ENABLED,
  },
} as const;

export interface IChatAgentItem {
  botId: string; // 机器人ID
  channel: ChatAgentChannel; // 频道：1（稳定）或 0（开发）
  createdAt: string; // 创建时间
  id: string; // 版本ID
  ifDefault: boolean; // 是否为当前稳定版最新版本
  lastUpdatedAt: string; // 最后更新时间
  lastUpdatedBy: string; // 最后更新人
  remark: string; // 内部备注
  status: ChatAgentStatus; // 状态：1（可用）或 0（禁用）
  type: string; // 频道类型
  versionDesc: string; // 外显介绍
  versionName: string; // 外显版本名
}

/** 获取所有未下线的版本列表 */
export const getVersionList = () => {
  return client<APIResponse<IChatAgentItem[]>>(
    "/api/im/agent-flow-versions/stable/list",
  );
};

/** 获取有效的稳定版本 */
export const getStableVersion = () => {
  return client<APIResponse<IChatAgentItem>>(
    "/api/im/agent-flow-versions/stable",
  );
};

/** 绑定用户到指定工作流版本 */
export const bindUserToVersion = (data: {
  account: string;
  ifLatestStableVersion?: number; // 1为最新文档版
  type: number; //	类型账 0-客服账号 1-店铺 2-集团
  workFlowVersionId?: string;
}) => {
  return client<APIResponse<null>>("/api/im/agent-flow-versions/bind", {
    method: "POST",
    data,
  });
};

/** 获取该客户当前绑定的版本 */
export const getBindVersion = (data: {
  account: string;
  type: number; //	类型账 0-客服账号 1-店铺 2-集团
}) => {
  return client<APIResponse<IChatAgentItem>>(
    "/api/im/agent-flow-versions/detail",
    {
      method: "POST",
      data,
    },
  );
};
