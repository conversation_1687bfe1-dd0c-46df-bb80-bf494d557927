import PlatformIcon from "@/business/components/PlatformIcon";
import { TabTypeMap } from "@/business/components/ReceptionProvider/store";
import type { Conversation } from "@/constant/protocol";
import { Tooltip } from "antd";
import clsx from "clsx";
import { useState } from "react";
import CloseOutlined from "~icons/ant-design/close-outlined";
import StarMark from "./StarMark";

const getTimeDifference = (timestamp1: number, timestamp2: number) => {
  // 确保时间戳是数字类型
  const time1 = Number(timestamp1);
  const time2 = Number(timestamp2);

  // 计算时间差的绝对值（单位：毫秒）
  const difference = Math.abs(time1 - time2);

  // 将毫秒转换为秒
  const totalSeconds = Math.floor(difference / 1000);

  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  // 构建结果字符串
  const result = [];
  if (hours > 0) result.push(`${hours.toString()}小时`);
  if (minutes > 0) result.push(`${minutes.toString()}分钟`);
  if (seconds > 0) result.push(`${seconds.toString()}秒`);

  // 如果所有单位都为0，返回0秒
  const text = result.length === 0 ? undefined : result.join("");
  return {
    text,
    totalSeconds,
    hours,
    minutes,
    seconds,
  };
};

const ConversationItem = ({
  className,
  data,
  time,
  tabActive,
  onOpenConversation,
  onRemoveConversation,
  onStarChange,
}: {
  className?: string;
  data: Conversation;
  time: number;
  tabActive: string;
  onOpenConversation?: (value: Conversation) => void;
  onRemoveConversation?: (value: Conversation) => void;
  onStarChange?: (starType: string) => void;
}) => {
  const { text, totalSeconds } = getTimeDifference(time, data.msgTime);
  const isTimeout = totalSeconds > 180;
  const waitTime = totalSeconds > 60 ? `已等待${text}` : text;
  const [starType, setStarType] = useState<string>(data.starType || "init");

  const handleOpenConversation = () => {
    onOpenConversation?.(data);
  };

  const hanldeRemoveConversation = () => {
    onRemoveConversation?.(data);
  };

  const handleStarChange = (starType: string) => {
    //星标变化
    onStarChange?.(starType);
    setStarType(starType);
  };

  return (
    <div
      className={clsx(
        "flex items-center  text-left rounded-8 w-full text-12   group ",
        data.isLight ? "bg-[#FAEEC8]" : "bg-white",
        className,
      )}
    >
      <button
        type="button"
        className="block flex-1 w-0  p-2 text-left"
        onClick={handleOpenConversation}
      >
        <div>
          <span className="flex items-center gap-2">
            <Tooltip title={data.shopName} placement="topRight">
              <span>
                <PlatformIcon
                  className="w-4 h-4"
                  platform={data.platformType}
                />
              </span>
            </Tooltip>
            <span className="truncate flex-1 w-0 font-medium">
              {data.buyerNick}
            </span>
            {tabActive === TabTypeMap.reception && data.isRead && (
              <span
                className={clsx(
                  "ml-auto flex-shrink-0 ",
                  isTimeout ? "text-red-7" : "text-black-3",
                )}
              >
                {waitTime}
              </span>
            )}
            {tabActive === TabTypeMap.starMark && (
              <span className="ml-auto text-[12px] text-black-2">
                {data.sellerNick}
              </span>
            )}
          </span>
        </div>
        <div className="flex justify-between gap-4 items-center">
          <div className="truncate text-black-3">{data.showContent}</div>
          <div className="w-[23px] h-[23px]">
            <div
              className={`${data.starType === "init" || !data.starType ? "!hidden" : ""} group-hover:!block`}
            >
              <StarMark
                starType={starType}
                onStarTypeChange={handleStarChange}
              />
            </div>
          </div>
        </div>
      </button>
      <button
        type="button"
        onClick={hanldeRemoveConversation}
        className="transition-all duration-300 w-0  overflow-hidden hover:text-black-4 group-hover:w-[20px]   group-hover:opacity-100 text-black-2 "
      >
        <CloseOutlined className="text-[10px]" />
      </button>
    </div>
  );
};

export default ConversationItem;
