import type { FormatedAgentMessageType } from "@/business/components/AgentProvider/store";
import { useMemo } from "react";
import QuestionCircleOutlined from "~icons/ant-design/question-circle-outlined";
import { formatTime } from "../../../utils";

interface OrderRemarkProps {
  data: FormatedAgentMessageType<"TOOL_AGENT">;
}

const OrderRemark = ({ data }: OrderRemarkProps) => {
  const preData = useMemo(() => {
    return {
      ...data.parsedPayload,
      time: formatTime(data.time),
      status: data.status,
      buyerAccount: data.buyerAccount,
      id: data.id,
      ifLabel: data.ifLabel,
    };
  }, [data]);

  const contentList = useMemo(
    () => [
      { label: "订单号", value: preData.id },
      { label: "内容", value: preData.originQuestion },
      { label: "标旗", value: preData.originQuestion },
      { label: "执行结果", value: preData.originQuestion },
      { label: "失败原因", value: preData.originQuestion },
    ],
    [preData],
  );

  return (
    <div className="px-2 pb-2 text-12">
      <div className="flex pt-1 flex-col gap-1.5 text-black-3">
        {contentList.map((item) => (
          <div key={item.label}>
            <span className="text-black-4 font-medium after:content-[':'] after:mx-1">
              {item.label}
            </span>
            <span>{item.value}</span>
          </div>
        ))}
      </div>
      <div className="pt-1.5 text-black-2 flex items-center">
        <QuestionCircleOutlined />
        <span className="pl-0.5">订单备注</span>
      </div>
    </div>
  );
};

export default OrderRemark;
