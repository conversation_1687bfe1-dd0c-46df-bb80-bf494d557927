import PlaceholderImg from "@/assets/images/placeholder/error.png";
import Logger from "@/class/Logger";
import { useRouteError } from "react-router";

const logger = new Logger("React");

const ErrorBoundary = () => {
  const error = useRouteError();
  logger.error(error);

  return (
    <div className="flex h-full w-full flex-1 items-center justify-center ">
      <img
        className="w-full max-w-[550px] object-contain"
        width={550}
        height={320}
        src={PlaceholderImg}
        alt="error"
      />
    </div>
  );
};

export default ErrorBoundary;
