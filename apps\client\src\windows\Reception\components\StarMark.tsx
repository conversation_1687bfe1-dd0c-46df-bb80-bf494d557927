import blueStar from "@/assets/images/reception/star-blue.png";
import greenStar from "@/assets/images/reception/star-green.png";
import initStar from "@/assets/images/reception/star-init.png";
import redStar from "@/assets/images/reception/star-red.png";
import yellowStar from "@/assets/images/reception/star-yellow.png";
import { useEffect, useState } from "react";

interface StarMarkProps {
  starType: string;
  onStarTypeChange: (type: string) => void;
}
const StarMark: React.FC<StarMarkProps> = (props: StarMarkProps) => {
  const { starType, onStarTypeChange } = props;
  const [currentStar, setCurrentStar] = useState<string>(starType || "init"); // 默认选中第一个tab

  const changeCurrentType = (starType: string) => {
    // setCurrentStar(starType)
    onStarTypeChange(starType);
  };
  const handleStarClick = () => {
    if (!currentStar || currentStar === "init") {
      changeCurrentType("yellow");
    } else {
      changeCurrentType("init");
    }
  };

  const starMap = {
    "": initStar,
    init: initStar,
    red: redStar,
    yellow: yellowStar,
    blue: blueStar,
    green: greenStar,
  };

  useEffect(() => {
    console.log(props.starType, "props.starType");
    setCurrentStar(props.starType);
  }, [props.starType]);

  const starList = [
    {
      value: "red",
      backgroundColor: "#FF574D",
      borderColor: "#F6B0AB",
    },
    {
      value: "yellow",
      backgroundColor: "#FFB340",
      borderColor: "#FFB345",
    },
    {
      value: "blue",
      backgroundColor: "#669EFF",
      borderColor: "#669EFF",
    },
    {
      value: "green",
      backgroundColor: "#6CCC3D",
      borderColor: "#6CCC3D",
    },
  ];

  // 调用函数执行
  return (
    <div
      className="flex items-center group relative"
      onKeyDown={() => {}}
      onClick={(e) => {
        e.stopPropagation();
        handleStarClick();
      }}
    >
      <img
        src={starMap[currentStar as keyof typeof starMap]}
        className="w-[23px] h-[23px]"
        alt=""
      />
      <div
        className="select-color absolute left-1/2 z-[9999] top-[23px] hidden group-hover:block"
        style={{ transform: "translateX(-50%)" }}
      >
        {starList.map((star) => (
          <div
            key={star.value}
            className="color-item w-[33px] h-[25px] bg-[#fff] flex items-center justify-center"
            onKeyDown={() => {}}
            onClick={(e) => {
              e.stopPropagation();
              changeCurrentType(star.value);
            }}
          >
            <div
              className={`border-box flex items-center justify-center border-[1px] w-[14px] h-[14px] rounded-full ${
                star.value === currentStar ? "border-solid" : ""
              }`}
              style={{ borderColor: star.borderColor }}
            >
              <div
                className="color w-[8px] h-[8px] rounded-full"
                style={{ backgroundColor: star.backgroundColor }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StarMark;
