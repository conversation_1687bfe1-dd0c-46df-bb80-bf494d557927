import client from "@/network/client";
import type { Platform } from "@workspace/business";
import type { APIResponse, PageResults } from "@workspace/types";

export type FocusInfo = {
  /** 详细网址 */
  detailUrl: string;
  /** 图片网址 */
  imageUrl: string;
  /** 标题 */
  productName: string;
  /** SKU属性 */
  skuAttr?: string;
  skuId?: string;
  spuId?: string;
};

export type CopilotMedia = {
  copilotMediaType: "pic" | "video" | "link";
  copilotMultimediaId: string;
  desc: string;
  url: string;
  coverUrl?: string;
  fileName: string;
};

export const TopicStatusMap = {
  WAIT: "WAIT",
  TOPIC_FINISH: "TOPIC_FINISH",
  FINISH: "FINISH",
  FAIL: "FAIL",
};

export type TopicStatusType = keyof typeof TopicStatusMap;

export const TrickTypeMap = {
  /** 知识库" */
  KNOWLEDGE: "KNOWLEDGE",
  /** 聊天记录 */
  CHAT: "CHAT",
  /** 文档 */
  DOCUMENT: "DOCUMENT",
  /** 所有 */
  ALL: "ALL",
} as const;

export type TrickType = keyof typeof TrickTypeMap;

export const ReplyTypeMap: Record<string, string> = {
  log: "聊天记录",
  size: "尺码推荐",
  detail: "商品详情",
  detLog: "商品详情、聊天记录",
};

export type TopicContentType = {
  /** 买家昵称 */
  buyerAccount: string;
  /** 上下文 */
  content: string;
  /** 不赞的数量 */
  dislikeNum: string;
  /** 错误 */
  errMsg: {
    /** 错误类型 商品不存在 | AI没有返回 | 系统错误 */
    errorType: "PRODUCT_NOT_FIND" | "AI_NOT_CREATE" | "OTHER";
    /** 系统错误信息 */
    error: string;
    /** 错误信息描述 */
    errorDesc: string;
  };
  /** 焦点信息 */
  focusInfo: FocusInfo;
  /** 赞的数量 */
  likeNum: string;
  /** 消息时间 */
  msgTime: string;
  /** 原始问题 */
  originQuestion: string;
  /** 引用ID */
  quoteId: string;
  /** 买家ID */
  sellerAccount: string;
  /** 卖家昵称 */
  sellerNick: string;
  thirdShopId: string;
  topicId: string;
  /** 主题 */
  topicName: string;
  /** 类型（0: 商品 1：话术） */
  type: 0 | 1;
  /** 内容 */
  trick: string;
  /** 状态:WAIT:待开始 TOPIC_FINISH:已生成主题 FINISH:结束 FAIL:失败 */
  topicStatus: TopicStatusType;
  /** json字符串 */
  ext: string;
  /** 售前:PRE_SALE/发货前:BEFORE_DELIVERY/发货后:AFTER_DELIVERY */
  orderStages?: string;
  /** 是否已采纳回复 */
  ifAcceptedReply: boolean;
  /** 是否自动发送 */
  ifAutoSend: boolean;
  /** 是否首次采纳 */
  ifFirstAcceptedReply: boolean;
  /** 是否上次发过 */
  ifLastSend: boolean;
  /** 使用次数 */
  useNum: number;
  trickList: {
    content: string[];
    referenceType: TrickType;
  }[];
  /** trickList的旧字段 */
  tickList: {
    content: string[];
    referenceSourceType: TrickType;
  }[];
};

export const AgentCardTypeMap = {
  CONSULT_PRODUCT: {
    label: "咨询商品",
    value: "CONSULT_PRODUCT",
  },
  CONSULT_REPLY: {
    label: "咨询回复",
    value: "CONSULT_REPLY",
  },
  PURCHASE_PROMPT: {
    label: "催单话术",
    value: "PURCHASE_PROMPT",
  },
  TOOL_CALL: {
    label: "自定义Agent",
    value: "TOOL_CALL",
  },
} as const;

export const AgentMessageStatusMap = {
  PROCESSING: {
    label: "处理中",
    value: "PROCESSING",
  },
  COMPLETED: {
    label: "已完成",
    value: "COMPLETED",
  },
  FAILED: {
    label: "失败",
    value: "FAILED",
  },
};

export type AgentMessageType = {
  /** 业务 */
  busi: "RECEPTION" | "MARKETING" | "AFTER_SALES";
  /** 买家 */
  buyerAccount: string;
  id: string;
  payload: string;
  platform: Platform;
  /** 客服 */
  sellerAccount: string;
  /** 状态 */
  status: keyof typeof AgentMessageStatusMap;
  thirdShopId: string;
  time: number;
  type: keyof typeof AgentCardTypeMap;
  ifSend: boolean;
  sendType: string;
  reviewStatus: 1 | 2 | 0;
  ifLabel: boolean;
};

/** 获取话题列表 */
export function getAgentMessageList(params: {
  /** 买家昵称 */
  buyerAccount?: string;
  pageIndex?: number;
  pageSize?: number;
}) {
  return client<APIResponse<PageResults<AgentMessageType>>>(
    "/api/im/agent/card/paginate",
    {
      params,
      shouldLog: true,
    },
  );
}

export const postMarkAgentMessageApproval = (data: {
  cardId: string;
  thirdShopId: string;
  ifLabel?: boolean;
}) => {
  return client<APIResponse<boolean>>("/api/im/agent/card/label", {
    data,
    method: "POST",
    shouldLog: true,
  });
};

/** 获取话题引用内容 */
export const getQuoteList = (params: { topicId: string }) => {
  return client<
    APIResponse<
      {
        content: string;
        documentName: string;
      }[]
    >
  >("/api/copilot/shop-copilot/quote-list", {
    method: "GET",
    params,
    shouldLog: true,
  });
};

/** 重新生成话术 */
export const postReGenerate = (data: { topicId: string }) => {
  return client<APIResponse<never>>("/api/copilot/shop-copilot/recreate", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 重新生成话术 */
export const postUnlike = (data: {
  topicId: string;
  ifLike: boolean | null;
}) => {
  return client<APIResponse<never>>("/api/copilot/shop-copilot/save/like", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 编辑话术 */
export const updateTopicTrick = (data: {
  /** 聊天记录Id */
  topicId: string;
  /** 自定义话术 */
  customerTrick: string;
}) => {
  return client<APIResponse<never>>("/api/copilot/shop-copilot/update", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 清除话术并重新生成 */
export const cleanTopicTrick = (data: {
  /** 聊天记录Id */
  topicId: string;
  /** 自定义话术 */
  customerTrick: string;
}) => {
  return client<APIResponse<never>>("/api/copilot/shop-copilot/clean", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 保存话术配置 */
export const saveTopicConfig = (data: {
  /** 聊天记录Id */
  topicId: string;
  /** 订单阶段,逗号隔开,AFTER_DELIVERY,BEFORE_DELIVERY,PRE_SALE,ALL */
  orderStages: string;
}) => {
  return client<APIResponse<never>>(
    "/api/copilot/shop-copilot/save-topic-config",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
};

/** 删除回复 */
export const removeTopicTrick = (data: {
  /** 聊天记录Id */
  topicId: string;
}) => {
  return client<
    APIResponse<{
      copilotUpdateType: string;
      id: string;
    }>
  >("/api/copilot/shop-copilot/delete", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 删除视频/图片/链接 */
export const removeMedias = (data: {
  /** 聊天记录Id */
  topicId: string;
  /** 绑定方式 1绑定 2解除 */
  bindType: number;
  copilotMultimediaIds: string[];
}) => {
  return client<APIResponse<never>>("/api/copilot/shop-copilot/bind-media", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 获取自动接待配置 */
export const getAutoReceptionConfig = (params: { buyerAccount: string }) => {
  return client<APIResponse<boolean>>("/api/im/agent-account/if-auto", {
    params,
    shouldLog: true,
  });
};

/** 设置自动接待配置 */
export const postAutoReceptionConfig = (data: {
  buyerAccount: string;
  ifAuto: boolean;
}) => {
  return client<APIResponse<never>>("/api/im/agent-account/set-auto-status", {
    method: "POST",
    data,
    shouldLog: true,
  });
};

/** 获取深度思考 */
export const getDeepLearn = () => {
  return client<APIResponse<boolean>>(
    "/api/im/agent-account/if-deep-thinking",
    {
      shouldLog: true,
    },
  );
};

/** 设置深度思考配置 */
export const postDeepLearnConfig = (params: { ifOpen: boolean }) => {
  return client<APIResponse<never>>("/api/im/agent-account/set-deep-thinking", {
    method: "POST",
    params,
    shouldLog: true,
  });
};

/** 请求催促下单话术 */
export const postUrgeOrderMessage = (data: {
  buyerAccount: string;
  thirdShopId: string;
  sellerAccount: string;
}) => {
  return client<APIResponse<never>>(
    "/api/copilot/agent-shop-copilot/active-marketing",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
};
