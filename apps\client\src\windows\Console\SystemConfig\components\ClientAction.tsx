import type { SupportedClient } from "@/business/components/ConfigProvider/store";
import useFileDownload from "@/business/hooks/useFileDownload";
import useTask from "@/business/hooks/useTask";
import { DownloadStatusMap } from "@/constant/protocol/index";
import { <PERSON><PERSON>, <PERSON>confirm } from "antd";
import CloseOutlined from "~icons/ant-design/close-outlined";

export type ClientType = SupportedClient & {
  localClient?: {
    platformPath: string;
    platformVersion: string;
    is64Bit: boolean;
    _id: string;
  };
  currentUsed: boolean;
};

interface ClientActionProps {
  data: ClientType;
  onDelete: (data: ClientType) => void;
  onInstall: (data: ClientType) => void;
  onCancel: (data: ClientType) => void;
}

const ClientAction = ({
  data,
  onDelete,
  onInstall,
  onCancel,
}: ClientActionProps) => {
  const { files, removeFile } = useFileDownload();
  const { tasks, addTasks, deletTask } = useTask();

  const isDeleting =
    data.localClient?.platformPath && tasks.has(data.localClient?.platformPath);
  const isInstalling =
    tasks.has(data._id) &&
    !(
      [
        DownloadStatusMap.InstallSuccess,
        DownloadStatusMap.InstallFailed,
      ] as number[]
    ).includes(files[data._id]?.status);

  const handleDeleteClient = (data: ClientType) => {
    addTasks([data.localClient?.platformPath as string]);
    removeFile(data._id);
    onDelete(data);
  };

  const handleInstallClient = (record: ClientType) => {
    addTasks([data._id]);
    onInstall(record);
  };

  const handleCancelInstallClient = (record: ClientType) => {
    deletTask(data._id);
    onCancel(record);
  };

  const DownloadStatusRenderMap = {
    delete: (
      <Button
        danger
        type="link"
        className="h-auto p-0"
        onClick={() => handleDeleteClient(data)}
        disabled={data.currentUsed}
      >
        删除
      </Button>
    ),
    install: (
      <Button
        type="link"
        className="h-auto p-0"
        onClick={() => handleInstallClient(data)}
      >
        立即安装
      </Button>
    ),
    downloading: (
      <Popconfirm
        title={<span>你确定要取消吗?</span>}
        onConfirm={() => handleCancelInstallClient(data)}
      >
        <button type="button" className="flex items-center gap-2 text-blue-7">
          <span className="border border-transparent">
            下载中 {files[data._id]?.progress}
          </span>
          <CloseOutlined />
        </button>
      </Popconfirm>
    ),
    installing: (
      <span className="text-blue-7 border border-transparent">安装中...</span>
    ),
    failed: (
      <Button
        type="link"
        className="h-auto p-0"
        onClick={() => handleInstallClient(data)}
      >
        安装失败，请重试
      </Button>
    ),
    deleting: (
      <span className="text-red-7 border border-transparent">正在删除...</span>
    ),
  };

  let type: keyof typeof DownloadStatusRenderMap | undefined = undefined;
  if (
    !data.localClient &&
    !isInstalling &&
    !isDeleting &&
    (!files[data._id] ||
      ([DownloadStatusMap.InstallSuccess] as number[]).includes(
        files[data._id]?.status,
      ))
  ) {
    type = "install";
  } else if (data.localClient && !isInstalling && !isDeleting) {
    type = "delete";
  } else if (
    !data.localClient &&
    isInstalling &&
    files[data._id]?.status !== DownloadStatusMap.Installing
  ) {
    type = "downloading";
  } else if (
    !data.localClient &&
    isInstalling &&
    files[data._id]?.status === DownloadStatusMap.Installing
  ) {
    type = "installing";
  } else if (
    !data.localClient &&
    files[data._id]?.status === DownloadStatusMap.InstallFailed
  ) {
    type = "failed";
  } else if (isDeleting) {
    type = "deleting";
  }

  const component = type ? DownloadStatusRenderMap[type] : null;

  return <>{component}</>;
};

export default ClientAction;
