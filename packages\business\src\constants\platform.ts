import Ali1688Icon from "../assets/icons/logo/ali1688.svg?react";
import ChannelIcon from "../assets/icons/logo/channel.svg?react";
import DEWUIcon from "../assets/icons/logo/dewu.svg?react";
import DoudianIcon from "../assets/icons/logo/doudian.svg?react";
import JdIcon from "../assets/icons/logo/jd.svg?react";
import KuaishouIcon from "../assets/icons/logo/kuaishou.svg?react";
import PDDIcon from "../assets/icons/logo/pdd.svg?react";
import TaobaoIcon from "../assets/icons/logo/taobao.svg?react";
import YouzanIcon from "../assets/icons/logo/youzan.svg?react";

export enum Platform {
  TAOBAO = 0,
  PDD = 1,
  YOUZAN = 2,
  KUAISHOU = 4,
  DOUDIAN = 5,
  JD = 7,
  CHANNELS = 8,
  DEWU = 9,
  ALI1688 = 10,
}

export const PlatformMap: Record<
  Platform,
  {
    label: string;
    icon: React.FunctionComponent<
      globalThis.React.SVGProps<SVGSVGElement> & {
        title?: string | undefined;
      }
    >;
    value: Platform;
  }
> = {
  [Platform.TAOBAO]: {
    label: "淘宝",
    icon: TaobaoIcon,
    value: Platform.TAOBAO,
  },
  [Platform.PDD]: {
    label: "拼多多",
    icon: PDDIcon,
    value: Platform.PDD,
  },
  [Platform.YOUZAN]: {
    label: "有赞",
    icon: YouzanIcon,
    value: Platform.YOUZAN,
  },
  [Platform.KUAISHOU]: {
    label: "快手",
    icon: KuaishouIcon,
    value: Platform.KUAISHOU,
  },
  [Platform.DOUDIAN]: {
    label: "抖店",
    icon: DoudianIcon,
    value: Platform.DOUDIAN,
  },
  [Platform.JD]: {
    label: "京东",
    icon: JdIcon,
    value: Platform.JD,
  },
  [Platform.DEWU]: {
    label: "得物",
    icon: DEWUIcon,
    value: Platform.DEWU,
  },
  [Platform.CHANNELS]: {
    label: "视频号",
    icon: ChannelIcon,
    value: Platform.CHANNELS,
  },

  [Platform.ALI1688]: {
    label: "1688",
    icon: Ali1688Icon,
    value: Platform.ALI1688,
  },
} as const;
