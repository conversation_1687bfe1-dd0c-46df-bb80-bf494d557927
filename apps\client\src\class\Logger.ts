import { safeStringify } from "@/utils";
import dayjs from "dayjs";

const logLevelMap = {
  info: {
    value: 1,
    text: "info",
  },
  warn: {
    value: 3,
    text: "warn",
  },
  error: {
    value: 4,
    text: "error",
  },
  debug: {
    value: 2,
    text: "debug",
  },
} as const;

type LogLevel = keyof typeof logLevelMap;

export const LogLevelEnums = Object.entries(logLevelMap).reduce(
  (acc, cur) => {
    return Object.assign(acc, {
      [cur[0]]: cur[1].value,
    });
  },
  {} as Record<LogLevel, number>,
);

type LogEntry = {
  timestamp: string;
  level: LogLevel;
  message: string;
};

export class Logger {
  private logEntry: string;
  constructor(logEntry: string) {
    this.logEntry = logEntry;
  }
  private log(level: LogLevel, ...args: unknown[]) {
    const message = args.map(safeStringify).join(" ");

    const logEntry = this.createLogEntry(level, message);

    // 输出到控制台
    console[level](this.formatLogEntry(logEntry));
    try {
      window.QCefViewClient?.invokeMethod(
        "printLog",
        LogLevelEnums[level],
        this.formatLogEntry(logEntry),
      );
    } catch (e) {
      console.error(e);
    }
  }

  private createLogEntry(level: LogLevel, message: string) {
    return {
      timestamp: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      level,
      message: message,
    };
  }

  private formatLogEntry(logEntry: LogEntry) {
    return `[${logEntry.timestamp}] [Client-${window.__CLIENT_VERSION__}::Web-${window.__WEB_VERSION__}] [${this.logEntry}] [${logEntry.level.toLocaleUpperCase()}] ${logEntry.message}`;
  }

  // 解析调用栈信息的辅助函数
  private getCallerInfo(stack?: string): string | string[] {
    if (!stack) return "Unknown";

    // 分割调用栈，并过滤掉内部方法
    const stackLines = stack
      .split("\n")
      .filter(
        (line) =>
          line.includes("at ") &&
          !line.includes("LoggerDecorator") &&
          !line.includes("getCallerInfo"),
      );

    // 返回调用者信息
    return stackLines.map((line) => line.trim() || "Unknown");
  }

  private logStack() {
    const stack = new Error().stack;
    const callerInfo = this.getCallerInfo(stack);

    // 输出调用日志
    this.log(logLevelMap.error.text, {
      caller: callerInfo,
      timestamp: new Date().toISOString(),
    });
  }

  // 不同日志等级的方法
  info(...args: unknown[]) {
    this.log(logLevelMap.info.text, ...args);
  }

  warn(...args: unknown[]) {
    this.log(logLevelMap.warn.text, ...args);
  }

  error(...args: unknown[]) {
    this.logStack();
    this.log(logLevelMap.error.text, ...args);
  }

  debug(...args: unknown[]) {
    this.log(logLevelMap.debug.text, ...args);
  }
}

export default Logger;
