{"name": "agent-client", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "prepare": "husky"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write --no-errors-on-unmatched"]}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.2.5", "turbo": "^2.3.3", "typescript": "5.5.4"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}