import clsx from "clsx";

const Section = ({
  children,
  title,
  desc,
  stickyHeader,
  ...others
}: {
  children: React.ReactNode;
  title: React.ReactNode;
  desc?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  stickyHeader?: boolean;
}) => {
  return (
    <section {...others}>
      <div
        className={clsx(
          "py-4 flex justify-between",
          stickyHeader ? "sticky top-0 z-10 bg-white" : "",
        )}
      >
        <h2 className="flex items-center gap-2 font-medium text-14 text-black-5 before:h-3.5 before:w-[3px] before:rounded-full before:overflow-hidden before:bg-blue-7">
          {title}
        </h2>
        {desc && <span className="text-black-2 text-12">{desc}</span>}
      </div>
      {children}
    </section>
  );
};

export default Section;
