import useSession from "@/business/hooks/useSession";
import useRemote from "@/hooks/useRemote";
import { type IAuthItem, getShopAuth } from "@/network/api/base";
import { useMemo, useState } from "react";

const isDateWithin30Days = (dateString: string): boolean => {
  // 将日期字符串转换为 Date 对象
  const givenDate: Date = new Date(dateString.replace(/ /, "T")); // 将空格替换为 T 以符合 ISO 格式
  const today: Date = new Date(); // 获取当前日期
  const timeDiff: number = givenDate.getTime() - today.getTime(); // 计算时间差（以毫秒为单位）

  // 将时间差转换为天数
  const daysDiff: number = timeDiff / (1000 * 60 * 60 * 24);
  console.log(daysDiff, "时间差");
  // 判断是否小于 30 天
  return daysDiff > 1 && daysDiff < 30;
};

const AuthNotice = () => {
  const { buyer } = useSession();
  const [localCloseNotice, setLocalCloseNotice] = useState<string>(
    localStorage.getItem("isCloseNotice") || "false",
  );
  const { data: authData } = useRemote(
    buyer?.buyerNick ? [getShopAuth.name, buyer.buyerNick] : null,
    getShopAuth,
  );

  const showNoticeEnable = useMemo(() => {
    const isCloseNotice = localCloseNotice === "true";
    return (
      authData?.auths?.some((item: IAuthItem) => {
        return isDateWithin30Days(item.expireAt);
      }) && !isCloseNotice
    );
  }, [authData, localCloseNotice]);

  const closeNotice = () => {
    setLocalCloseNotice("true");
    localStorage.setItem("isCloseNotice", "true");
  };

  return (
    <>
      {showNoticeEnable ? (
        <div className="p-2 mx-1 my-2 bg-orange-1 justify-between flex text-[11px] items-center rounded-[5px] text-black-3">
          <div className="whitespace-nowrap">
            此店铺应用有效期剩余不足1个月，请及时续约
          </div>
          <div
            className="close text-blue-7 cursor-pointer"
            onClick={() => {
              closeNotice();
            }}
            onKeyDown={() => {}}
          >
            不再提醒
          </div>
        </div>
      ) : null}
    </>
  );
};
export default AuthNotice;
