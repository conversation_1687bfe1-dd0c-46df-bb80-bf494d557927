import FlagFilled from "@/assets/svg/flag-filled.svg?react";
import FlagOutlined from "@/assets/svg/flag-outlined.svg?react";
import type { FormatedAgentMessageType } from "@/business/components/AgentProvider/store";
import type { BatchSendMessageData } from "@/network/api/base";
import {
  AgentMessageStatusMap,
  type CopilotMedia,
} from "@/network/api/copilot";
import { trimNewlines } from "@/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  useDebounce,
} from "@workspace/ui";
import { Typography, Divider } from "antd";
import { useContext, useMemo, useRef } from "react";
import QuestionCircleOutlined from "~icons/ant-design/question-circle-outlined";
import SendOutlined from "~icons/icon-park-outline/send";
import ShoppingBagOneOutelined from "~icons/icon-park-outline/shopping-bag-one";
import AgentContext from "../../context";
import { formatTime } from "../../utils";
import SkeletonWrapper from "../SkeletonWrapper";
import MeidaContent from "./MediaContent";
import TextContent from "./TextContent";
import useAgent from "@/business/hooks/useAgent";
import TextSelect from "@/assets/svg/text-select.svg?react";

const Product = ({
  data,
  onSend,
  onMarkReview,
  shortcutIndex,
}: {
  data: FormatedAgentMessageType<"CONSULT_PRODUCT">;
  onSend: (id: string) => void;
  onMarkReview: (value: FormatedAgentMessageType<"CONSULT_PRODUCT">) => void;
  shortcutIndex?: number;
}) => {
  const { sendMessages, shortcutIndexMap, updateMsg } =
    useContext(AgentContext);

  const { store: groupSendShortcut } = useAgent(
    (state) => state.shortcut.groupSendShortcut,
  );

  const preData = useMemo(() => {
    const unknown = !data.parsedPayload?.product?.productName;
    return {
      ...data.parsedPayload?.product,
      // sellingPoints:  trimNewlines(data.parsedPayload?.product?.sellingPoints),
      sellingPoints: data?.parsedPayload?.msgContents
        ?.filter((item) => item.type === 0)
        ?.map((item) => trimNewlines(item.value)),
      time: formatTime(data.time),
      unknown,
      desc: unknown ? "未找到商品信息，请联系客服人员帮您处理" : "",
      status: data.status,
      ifSend: data.ifSend,
      buyerAccount: data.buyerAccount,
      id: data.id,
      ifLabel: data.ifLabel,
      medias: (data?.parsedPayload?.msgContents
        ?.filter((item) => item.type !== 0)
        .map((item) => ({
          copilotMediaType:
            item.type === 1 ? "pic" : item.type === 2 ? "video" : "link",
          url: item.value,
        })) || []) as CopilotMedia[],
    };
  }, [data]);
  const localContent = useRef(preData.sellingPoints || []);

  const handleSend = useDebounce(
    (msgContents: BatchSendMessageData["msgContents"]) => {
      sendMessages({
        msgContents,
        feature: {
          agent: "1",
          sidebar: "1",
          editSend:
            localContent.current !== preData.sellingPoints ? "1" : undefined,
          agentTopicId: data.id,
        },
        payload: JSON.stringify({
          ...data.parsedPayload,
          sellingPoints: localContent.current,
        }),
        cardId: data.id,
        buyerAccount: preData.buyerAccount,
      }).then((ifSuccess) => {
        if (ifSuccess) {
          onSend(data.id);
        }
      });
    },
    500,
  );

  const handleEdit = (text: string, index: number) => {
    localContent.current[index] = text;
    updateMsg({
      ...data,
      parsedPayload: {
        ...data.parsedPayload,
        msgContents: data.parsedPayload?.msgContents?.map((item, i) => {
          if (item.type === 0) {
            return {
              ...item,
              value: localContent.current[i],
            };
          }
          return item;
        }),
      },
    });
  };

  const handleSendAll = () => {
    handleSend(localContent.current.map((item) => ({ type: 0, value: item })));
  };

  const startIndex = shortcutIndexMap.get(preData.id)?.[0];

  return (
    <div className="bg-white overflow-hidden rounded-lg [--card-theme:theme(colors.orange.7)]">
      <div className="px-2 pt-1 pb-0.5 leading-[22px] text-12 flex items-center bg-gradient-to-b from-orange-50 to-white">
        {shortcutIndex ? (
          <span className="text-white bg-orange-7 font-medium mr-1 rounded-sm w-4 h-4 flex items-center justify-center">
            {shortcutIndex}
          </span>
        ) : null}
        <span className="text-orange-7 font-medium text-12">商品卖点</span>
        <span className="text-black-2 ml-2">{preData.time}</span>
        <div className="ml-auto flex items-center gap-2 text-12">
          <div
            title="插入到文本框"
            className="text-blue-7 text-12 hover:text-blue-5"
          >
            <TextSelect />
          </div>
          <Divider type="vertical" className="!mx-0" />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger onClick={handleSendAll}>
                {preData?.ifSend ? (
                  <button
                    type="button"
                    className="flex gap-0.5 border border-green-4 bg-green-1 rounded-4 items-center text-green-7 px-1 bg-transparent text-12 outline-none"
                  >
                    <SendOutlined className="text-14" />
                    已发送
                  </button>
                ) : (
                  <div className="bg-transparent text-14 outline-none border-none text-blue-7 cursor-pointer p-1 hover:text-blue-5">
                    <SendOutlined />
                  </div>
                )}
              </TooltipTrigger>
              <TooltipContent
                collisionPadding={20}
                className="max-w-[--radix-tooltip-content-available-width] whitespace-pre-wrap break-words bg-black-4"
                avoidCollisions
              >
                <span>
                  {shortcutIndex && groupSendShortcut
                    ? `发送（${groupSendShortcut} + ${shortcutIndex}）`
                    : "发送"}
                </span>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
      <div className="p-2 space-y-2 pt-0.5">
        <div className="flex gap-2">
          {preData.imageUrl ? (
            <img
              className="w-8 h-8 object-cover rounded-4 self-center flex-shrink-0"
              src={preData.imageUrl}
              alt={preData.productName}
            />
          ) : (
            <div className="w-8 h-8 bg-gray-3 rounded-4 self-center flex-shrink-0 flex items-center justify-center">
              <ShoppingBagOneOutelined className="text-black-2" />
            </div>
          )}
          <div className="w-0 flex-1 flex flex-col">
            <Typography.Text
              ellipsis={{ tooltip: true }}
              className="text-black-4 font-medium text-12"
            >
              {preData.productName}
            </Typography.Text>
            <Typography.Text
              ellipsis={{ tooltip: true }}
              className="text-black-3  text-12"
            >
              {preData?.unknown ? preData.desc : preData.skuAttr}
            </Typography.Text>
          </div>
        </div>
        <SkeletonWrapper.Content
          loading={
            !preData.sellingPoints &&
            ![
              AgentMessageStatusMap.FAILED.value,
              AgentMessageStatusMap.COMPLETED.value,
            ].includes(preData.status)
          }
        >
          {preData.sellingPoints ? (
            <div className="space-y-2 ">
              {preData.sellingPoints?.map((content, index) => (
                <TextContent
                  // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                  key={index}
                  ifSend={preData.ifSend}
                  text={content}
                  onContentChange={(text) => handleEdit(text, index)}
                  onSend={handleSend}
                  shortcutIndex={startIndex ? startIndex + index : undefined}
                />
              ))}
            </div>
          ) : null}
          {preData.medias.length > 0 ? (
            <MeidaContent
              data={preData.medias}
              buyerAccount={preData.buyerAccount}
              id={preData.id}
              shortcutIndex={
                startIndex
                  ? startIndex + preData.sellingPoints.length
                  : undefined
              }
            />
          ) : null}
          {preData?.sellingPoints ? (
            <div className="text-black-2 text-12 flex items-center">
              <QuestionCircleOutlined />
              <span>商详/聊天/采纳/全店</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      type="button"
                      className="py-0.5  ml-auto"
                      onClick={() => onMarkReview(data)}
                    >
                      {!preData.ifLabel ? (
                        <FlagOutlined className="text-black-2" />
                      ) : (
                        <FlagFilled className="text-blue-7" />
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent className="bg-black-4">
                    <div className="space-y-1">
                      <p>点击标记本次生成结果</p>
                      <p>可在调优工坊筛选查看</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          ) : null}
        </SkeletonWrapper.Content>
      </div>
    </div>
  );
};

export default Product;
