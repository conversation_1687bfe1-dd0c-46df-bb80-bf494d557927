import { websocketStore } from "@/business/hooks/useWebsocket";
import { platformClientMap } from "@/constant/client";
import type { SupportPlatform } from "@/constant/platform";
import {
  type ServiceAccountLocalInfo,
  WsCmdSend,
} from "@/constant/protocol/index";
import { getTanyuGroupSessionToken } from "@/network/api/account";
import {
  type ServiceAccountRemoteInfo,
  getScanAccountStateByIdAndPlatform,
} from "@/network/api/customer";
import dayjs from "dayjs";
import { type DebouncedFunc, debounce } from "lodash-es";
import { createStore } from "zustand";

export type ServiceAccount = ServiceAccountLocalInfo &
  Partial<ServiceAccountRemoteInfo> & {
    sellerAccountForBackend?: string;
  };

export const getAccountId = (account: ServiceAccountLocalInfo) => {
  //拼接规则 已经确认都为：拼接 如果没通过:拼接则手动拼接
  const [shopId, serviceAccount] = account.account.split(":");
  return shopId && serviceAccount
    ? `${shopId}:${serviceAccount}`
    : `${account.shopId}:${account.account}`;
};
export interface ServiceAccountState {
  /** ws原始数据 */
  sourceAccountMap: Record<SupportPlatform, ServiceAccountLocalInfo[]>;
  /** ws原始数据合并服务端数据 */
  accountMap: Record<SupportPlatform, ServiceAccount[]>;
  notAutoLoginAccountMap: Record<string, ServiceAccount>;
  hostingAccountMap: Record<string, boolean>;
  setSourceAccountMap: (
    customerAccount: Record<SupportPlatform, ServiceAccountLocalInfo[]>,
  ) => void;
  setAccountMap: (
    customerAccount: Record<SupportPlatform, ServiceAccount[]>,
  ) => void;
  setNotAutoHostAccountMap: (value: Record<string, ServiceAccount>) => void;
  setHostingAccountMap: (value: Record<string, boolean>) => void;
  unsubscribe: (() => void) | undefined;
  setSubscribe: (unsubscribe: (() => void) | undefined) => void;
  updateValidData: (
    accountMap: Record<SupportPlatform, ServiceAccountLocalInfo[]>,
    refresh?: boolean,
  ) => Promise<void>;
  tanyuGroupSessions: Record<
    string,
    {
      token: string;
      groupId: string;
      accountId: string;
      wxOpenId: string;
      expiredAt: number;
    }
  >;
  asyncUpdateTanyuGroupSessions: DebouncedFunc<
    (
      data: Parameters<typeof getTanyuGroupSessionToken>[0] & {
        groupId: string;
      },
    ) => Promise<void>
  >;
}

export const serviceAccountStore = createStore<ServiceAccountState>(
  (set, get) => ({
    accountMap: Object.entries(platformClientMap).reduce(
      (acc, [key]) => Object.assign(acc, { [key]: [] }),
      {} as Record<SupportPlatform, ServiceAccount[]>,
    ),
    sourceAccountMap: {} as Record<SupportPlatform, ServiceAccountLocalInfo[]>,
    notAutoLoginAccountMap: {} as Record<string, ServiceAccount>,
    hostingAccountMap: {},
    unsubscribe: undefined,
    setSubscribe: (unsubscribe: (() => void) | undefined) =>
      set({
        unsubscribe: () => {
          unsubscribe?.();
          set({ unsubscribe: undefined });
        },
      }),
    setAccountMap: (accountMap: Record<SupportPlatform, ServiceAccount[]>) => {
      const _accountMap = {
        ...get().accountMap,
        ...accountMap,
      };
      localStorage.setItem("accountMap", JSON.stringify(_accountMap));
      set({
        accountMap: _accountMap,
      });
    },
    setSourceAccountMap: (
      accountMap: Record<SupportPlatform, ServiceAccountLocalInfo[]>,
    ) => {
      set({
        sourceAccountMap: {
          ...get().sourceAccountMap,
          ...accountMap,
        },
      });
    },
    setNotAutoHostAccountMap: (accountMap: Record<string, ServiceAccount>) => {
      set({
        notAutoLoginAccountMap: {
          ...get().notAutoLoginAccountMap,
          ...accountMap,
        },
      });
    },
    setHostingAccountMap: (accountMap: Record<string, boolean>) => {
      set({
        hostingAccountMap: {
          ...get().hostingAccountMap,
          ...accountMap,
        },
      });
      setTimeout(() => {
        set({
          hostingAccountMap: {
            ...get().hostingAccountMap,
            ...Object.keys(accountMap).reduce(
              (acc, cur) => Object.assign(acc, { [cur]: false }),
              {},
            ),
          },
        });
      }, 10 * 1000);
    },
    updateValidData: async (
      accountMap: Record<SupportPlatform, ServiceAccountLocalInfo[]>,
      refresh?: boolean,
    ) => {
      const params = Object.values(accountMap)
        .flat()
        .map((account) => {
          return {
            accountId: account.accountId,
            platform: account.platformType,
            sellerAccount: getAccountId(account),
          };
        });
      const res = await getScanAccountStateByIdAndPlatform({
        list: params,
        refresh,
      }).promise.catch((error: Error) => error);
      if (res instanceof Error || !res.data.success) {
        get().setAccountMap(accountMap);
      } else {
        const sellerAccountMap = res.data.data.list.reduce(
          (acc, cur) =>
            Object.assign(acc, {
              [cur.sellerAccount]: cur,
            }),
          {} as Record<string, ServiceAccountRemoteInfo>,
        );
        const _data = Object.entries(accountMap).reduce(
          (acc, cur) => {
            const [key, value] = cur;
            return Object.assign(acc, {
              [key]: value.map((item) => ({
                ...item,
                ...sellerAccountMap[item.account],
              })),
            });
          },
          {} as Record<SupportPlatform, ServiceAccount[]>,
        );

        get().setAccountMap(_data);
      }
    },

    tanyuGroupSessions: {},
    asyncUpdateTanyuGroupSessions: debounce(
      async (data) => {
        const target = get().tanyuGroupSessions[data.groupId];
        if (target && target.expiredAt > dayjs().unix())
          return Promise.resolve();
        if (!data.thirdShopId) return Promise.resolve();
        const res = await getTanyuGroupSessionToken(data).promise.catch(
          (error: Error) => error,
        );
        if (res instanceof Error) return;
        if (res.data.success) {
          set({
            tanyuGroupSessions: {
              ...get().tanyuGroupSessions,
              [data.groupId]: {
                ...res.data.data,
                expiredAt: dayjs().add(1, "hour").unix(),
              },
            },
          });
          if (res.data.data?.token) {
            websocketStore.getState().socket?.sendMessage({
              cmd: WsCmdSend.SendTanyuGroupAccount,
              data: {
                groupAccount: res.data.data.token,
              },
            });
          }
        }
      },
      200,
      { leading: true },
    ),
  }),
);
