import EmptyPlacehoder from "@/assets/images/placeholder/empty-content.png";
import {
  type IProductItem,
  type SearchProductDataResultItem,
  searchByName,
} from "@/network/api/product";
import { Spin } from "antd";
import clsx from "clsx";
import { debounce } from "lodash-es";
import {
  type ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import CloseCircleFilled from "~icons/ant-design/close-circle-filled";
import SearchOutlined from "~icons/ant-design/search-outlined";

interface SearchProps {
  sellerAccount: string;
  onSelect: (data: IProductItem) => void;
}

const ShopSearch: React.FC<SearchProps> = ({ sellerAccount, onSelect }) => {
  const [text, setText] = useState("");
  const [pages, setPages] = useState<SearchProductDataResultItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const isComposition = useRef(false);
  const controllerRef = useRef<AbortController>();

  const handleClear = () => {
    setText("");
    setPages([]);
  };

  const _onSearch = useCallback(
    debounce(
      async ({ value }) => {
        if (isComposition.current) return;
        if (controllerRef.current) {
          controllerRef.current.abort();
          controllerRef.current = undefined;
        }
        if (!value) {
          setPages([]);
          setVisible(false);
          return;
        }
        setLoading(true);
        setVisible(true);
        const params = {
          itemName: value,
          account: sellerAccount,
          //第几页
          pageIndex: "1",
          //一页展示多少条
          pageSize: 999,
        };
        const { promise, controller } = searchByName(params);
        controllerRef.current = controller;
        const res = await promise.catch((error: Error) => error);
        setLoading(false);
        if (res instanceof Error) return;
        if (res.data.success) {
          if (res.data.data) {
            setPages(res.data.data?.results ?? []);
          } else {
            setPages([]);
          }
        }
      },
      500,
      { leading: false, trailing: true },
    ),
    [],
  );

  const onChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const value = e.target.value.trim();
    setText(e.target.value);
    setPages([]);
    setVisible(false);
    _onSearch({
      value: value,
    });
  };

  const results = useMemo(() => {
    const _results = pages.map((item) => ({ ...item, content: item.name }));
    if (!text) return _results;
    const regex = new RegExp(text, "g");
    return _results.map((item) => {
      const match = regex.exec(item.name);
      const chunks = [];
      while (match !== null) {
        const start = match.index;
        const end = regex.lastIndex;
        if (!chunks.length && start > 0) {
          chunks.push({ start: 0, end: start, highlight: false });
        }
        if (chunks.length && chunks[chunks.length - 1].end < start) {
          chunks.push({
            start: chunks[chunks.length - 1].end,
            end: start,
            highlight: false,
          });
        }
        if (end > start) {
          chunks.push({ start, end, highlight: true });
        }
        if (match.index === regex.lastIndex) {
          regex.lastIndex++;
        }
      }
      const lastEnd = chunks[chunks.length - 1]?.end;
      if (lastEnd && lastEnd < item.name.length - 1) {
        chunks.push({
          start: lastEnd,
          end: item.name.length,
          highlight: false,
        });
      }
      let content: string | ReactElement[] = item.name;
      if (chunks.length) {
        content = chunks.map((chunk, index) => {
          if (chunk.highlight) {
            return (
              <span
                key={`${index} + ${chunk.highlight}`}
                className="text-blue-7"
              >
                {item.name.substring(chunk.start, chunk.end)}
              </span>
            );
          }
          return (
            <span key={`${index} + ${chunk.highlight}`}>
              {item.name.substring(chunk.start, chunk.end)}
            </span>
          );
        });
      }
      return {
        ...item,
        content,
      };
    });
  }, [pages, text]);

  const _onSelect = (data: IProductItem) => {
    onSelect?.(data);
    setVisible(false);
  };
  const onFocus = () => {
    results.length > 0 && setVisible(true);
  };

  useEffect(() => {
    const onClick = (e: MouseEvent) => {
      if (containerRef.current) {
        const isInside = containerRef.current.contains(e.target as Node);
        if (!isInside) {
          setVisible(false);
        }
      }
    };
    document.addEventListener("click", onClick);
    return () => {
      document.removeEventListener("click", onClick);
    };
  }, []);

  return (
    <div className="relative" ref={containerRef}>
      <div className="flex items-center bg-white py-1 rounded-t-4 pt-3 border-b border-gray-3">
        <input
          placeholder="商品标题/ID/编号/货号"
          type="text"
          value={text}
          onChange={onChange}
          className="border-none outline-none px-3 flex-1 w-0"
          onFocus={onFocus}
          onCompositionStart={() => {
            isComposition.current = true;
          }}
          onCompositionEnd={() => {
            isComposition.current = false;
          }}
        />
        {text ? (
          <button
            type="button"
            className="text-black-1 text-14 h-[26px] leading-none border-none border-gray-3 outline-none bg-transparent cursor-pointer px-2.5 py-1"
            onClick={handleClear}
          >
            <CloseCircleFilled />
          </button>
        ) : (
          <span className="px-2.5 py-1 text-gray-5">
            <SearchOutlined />
          </span>
        )}
      </div>
      <div
        style={{ transform: "translateY(100%)" }}
        className={clsx(
          "z-10 bg-white absolute w-full -bottom-[1px] left-0 right-0 rounded-4 shadow-1 overflow-auto",
          visible ? "block" : "hidden",
        )}
      >
        <Spin spinning={loading}>
          <div className="min-h-[100px] max-h-[354px]">
            {results.length > 0 && !loading && (
              <div>
                {results.map((item) => {
                  return (
                    <div
                      key={item.productId}
                      className="res-item cursor-pointer py-[5px] px-3 hover:bg-blue-1 flex items-enter"
                      onClick={() => _onSelect(item)}
                      onKeyDown={() => {}}
                    >
                      <img
                        className="rounded-10px w-[28px] h-[28px] object-contain mr-[5px]"
                        src={item.pic}
                        alt=""
                      />
                      <div className="text-14 font-medium line-clamp-2">
                        {item.content || item.name}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
            {!results?.length && !loading && (
              <div
                className={clsx(
                  "items-center justify-center flex-col w-full h-[192px] flex",
                )}
              >
                <img src={EmptyPlacehoder} className="w-[240px]" alt="" />
                <div className="text-14 text-black-2">暂无数据</div>
              </div>
            )}
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default ShopSearch;
