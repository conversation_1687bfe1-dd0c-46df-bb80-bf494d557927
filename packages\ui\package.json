{"name": "@workspace/ui", "version": "1.1.1", "private": true, "description": "", "main": "index.ts", "types": "index.ts", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "tsc": "tsc -p ./tsconfig.json --noEmit"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@repo/typescript-config": "workspace:*", "@workspace/types": "workspace:*", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash-es": "^4.17.21", "lucide-react": "^0.475.0", "react": "^18.3.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@turbo/gen": "^1.12.4", "@types/big.js": "^6.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.24", "@types/react": "18.3.0", "autoprefixer": "^10.4.20", "big.js": "^6.2.2", "globals": "^15.13.0", "postcss": "^8.4.49", "tailwindcss": "3", "typescript": "5.5.4", "typescript-eslint": "^8.18.1"}, "peerDependencies": {"axios": "^1.7.9", "big.js": "^6.2.2", "react": "^19.0.0"}}