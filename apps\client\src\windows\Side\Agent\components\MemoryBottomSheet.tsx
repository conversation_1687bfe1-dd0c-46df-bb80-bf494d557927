import useSession from "@/business/hooks/useSession";
import useRemote from "@/hooks/useRemote";
import {
  type BuyerMemoryLabel,
  getAvailableLabelList,
  getBuyerLabelList,
  getChatDigestByBuyerAccount,
  postAddBuyerLabel,
  postClearBuyerLabel,
  postDeleteBuyerLabel,
  postSaveBuyerLabel,
} from "@/network/api/memory";
import {
  Button,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui";
import { Drawer, type DrawerProps, Empty, Input, Select, Spin } from "antd";
import clsx from "clsx";
import dayjs from "dayjs";
import { useRef, useState } from "react";
import Markdown from "react-markdown";
import { toast } from "sonner";
import CloseOutlined from "~icons/ant-design/close-outlined";
import AddSolid from "~icons/icon-park-solid/add";
import BrainCircuit from "~icons/lucide/brain-circuit";
import Eraser from "~icons/lucide/eraser";
import Tag from "~icons/lucide/tag";
import Trash2 from "~icons/lucide/trash-2";

interface MemoryItemProps {
  memory: BuyerMemoryLabel;
  onUpdate: (id: string, data: { content: string }) => void;
  onDelete: (id: string) => void;
}

// --- Child Component for each Memory Item ---
const MemoryItem = ({ memory, onUpdate, onDelete }: MemoryItemProps) => {
  const [content, setContent] = useState(memory.value);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocus, setIsFocus] = useState(false);

  const handleBlur = () => {
    setIsFocus(false);
    const trimContent = content.trim();
    // Save only if content has actually changed
    if (trimContent && trimContent !== memory.value) {
      onUpdate(memory.id, { content: trimContent });
    } else if (!trimContent) {
      setContent(memory.value);
    }
  };

  const dateInfo = memory?.lastUpdatedAt
    ? dayjs(memory.lastUpdatedAt)
    : undefined;
  const isToday = dateInfo?.isSame(dayjs(), "day");

  return (
    <div
      className={clsx(
        "bg-white p-1.5 rounded-lg border transition-shadow hover:shadow-md ",
        isFocus ? "ring-2 ring-blue-7" : "",
      )}
    >
      <div className="flex gap-2 items-center ">
        <div className="text-blue-7 px-2 py-0.5 rounded-full bg-blue-1 flex-shrink-0">
          {memory.label}
        </div>
        {dateInfo ? (
          <span className="flex-shrink-0 ml-auto text-gray-400">
            {isToday
              ? dateInfo?.format("HH:mm:ss")
              : dateInfo?.format("YYYY-MM-DD")}
          </span>
        ) : null}
        <button
          type="button"
          onClick={() => onDelete(memory.id)}
          className="text-gray-400 hover:text-red-500 transition-colors"
        >
          <Trash2 />
        </button>
      </div>
      <Input.TextArea
        variant="borderless"
        placeholder="输入记忆内容..."
        className="p-0.5"
        ref={textareaRef}
        value={content}
        onChange={(e) => setContent(e.target.value)}
        onBlur={handleBlur}
        onFocus={() => setIsFocus(true)}
        autoSize={{ minRows: 1, maxRows: 10 }}
      />
    </div>
  );
};

const Summary = ({
  chatDigest,
}: { chatDigest: { content: string } | undefined }) => {
  if (!chatDigest?.content) return null;
  return (
    <div className="space-y-2">
      <div className="flex items-center text-black-4">
        <BrainCircuit className="mr-2 text-blue-7" />
        <h3 className="font-semibold text-14">AI 速览</h3>
      </div>
      <div className="prose prose-sm prose-slate max-w-full">
        <Markdown>{chatDigest.content}</Markdown>
      </div>
    </div>
  );
};

interface MemoryBottomSheetProps extends DrawerProps {
  open: boolean;
  onClose: () => void;
  data?: any;
  onOk?: () => void;
}
const MemoryBottomSheet = ({
  open,
  onClose,
  data,
  onOk,
  ...others
}: MemoryBottomSheetProps) => {
  const { buyer } = useSession();
  const [loading, setLoading] = useState(false);
  const [newItem, setNewItem] = useState<{
    value: string | undefined;
    label: string | undefined;
  } | null>(null);
  const { data: chatDigest } = useRemote(
    buyer?.buyerNick && open
      ? [getChatDigestByBuyerAccount.name, { buyerAccount: buyer?.buyerNick }]
      : null,
    ([, params]) => getChatDigestByBuyerAccount(params),
  );

  const {
    data: buyerLabelList,
    isLoading,
    mutate: mutateBuyerLabelList,
  } = useRemote(
    buyer?.buyerNick && open
      ? [getBuyerLabelList.name, { buyerAccount: buyer?.buyerNick }]
      : null,
    ([, params]) => getBuyerLabelList(params),
    {
      onSuccess() {
        setLoading(false);
        setNewItem(null);
      },
      onError() {
        setLoading(false);
        setNewItem(null);
      },
    },
  );

  const { data: allLabelList } = useRemote(
    open ? [getAvailableLabelList.name] : null,
    () => getAvailableLabelList(),
  );

  const handleRemove = async (id: string) => {
    const res = await postDeleteBuyerLabel({ id }).promise.catch(
      (error: Error) => error,
    );
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      mutateBuyerLabelList();
    }
  };

  const handleClearAll = async () => {
    if (!buyer?.buyerNick) {
      toast.error("buyerNick不能为空");
      return;
    }
    const res = await postClearBuyerLabel({
      buyerAccount: buyer?.buyerNick,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      mutateBuyerLabelList();
    }
  };

  const handleCreateMemory = async () => {
    if (!newItem?.value?.trim() || !newItem?.label) return;
    if (!buyer?.buyerNick) {
      toast.error("buyerNick不能为空");
      return;
    }
    setLoading(true);
    const res = await postAddBuyerLabel({
      buyerAccount: buyer?.buyerNick,
      labelMetaId: newItem.label,
      value: newItem.value?.trim(),
    }).promise.catch((error: Error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      mutateBuyerLabelList();
    }
  };

  const handleUpdate = async (id: string, data: { content: string }) => {
    const res = await postSaveBuyerLabel({
      id,
      value: data.content,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      mutateBuyerLabelList();
    }
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between ">
          <span className="flex items-center gap-1 text-14">
            <span>记忆</span>
            <span className="text-12 text-black-2 font-normal">
              更新略慢于对话，不影响意图识别
            </span>
          </span>
          <button
            type="button"
            className="bg-transparent outline-none border-none cursor-pointer text-14"
            onClick={onClose}
          >
            <CloseOutlined />
          </button>
        </div>
      }
      footer={null}
      closeIcon={null}
      placement="bottom"
      open={open}
      height="80%"
      className="[&_.ant-drawer-header]:py-1.5 overflow-hidden rounded-t-8  [&_.ant-drawer-body]:p-0   [&_.ant-drawer-header]:px-3 "
      {...others}
    >
      <Spin spinning={isLoading}>
        <div className="space-y-2 p-2">
          <Summary chatDigest={chatDigest} />
          <div className="flex justify-between items-center ">
            <h3 className="text-14 flex font-semibold items-center gap-2 ">
              <Tag className="text-14 text-orange-7 " />
              记忆标签
            </h3>
            <div className="flex items-center gap-4">
              {buyerLabelList?.buyerLabels?.length ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={handleClearAll}
                        className="flex items-center w-7 h-7 text-xs rounded-md text-gray-500 hover:text-red-500 transition-colors"
                        aria-label="Clear all memories"
                      >
                        <Eraser />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent className="bg-black-4 z-[1001]">
                      <div className="text-14">
                        <p>清空全部记忆</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : null}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        if (newItem) return;
                        setNewItem({
                          value: "",
                          label: "",
                        });
                      }}
                      className="flex items-center text-blue-7 hover:text-blue-7  w-7 h-7 rounded-md  transition-colors "
                    >
                      <AddSolid />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="bg-black-4 z-[1001]">
                    <div>添加记忆标签</div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className="space-y-2">
            {newItem && (
              <Spin spinning={loading}>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Select
                      className="w-full"
                      placeholder="选择标签"
                      options={allLabelList?.labelMetas.map((item) => ({
                        label: item.label,
                        value: item.id,
                      }))}
                      onChange={(e) => setNewItem({ ...newItem, label: e })}
                    />
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => setNewItem(null)}
                      className="rounded-md"
                    >
                      取消
                    </Button>
                  </div>
                  <div className="border border-gray-200 rounded-md p-2">
                    <Input.TextArea
                      variant="borderless"
                      autoSize={{ minRows: 4, maxRows: 10 }}
                      value={newItem.value}
                      onChange={(e) =>
                        setNewItem((pre) => {
                          return {
                            value: e.target.value,
                            label: pre?.label,
                          };
                        })
                      }
                      placeholder="输入记忆内容..."
                      onBlur={handleCreateMemory}
                    />
                    <div className="text-right text-xs text-gray-500">
                      {newItem.value?.length || 0} / 100
                    </div>
                  </div>
                </div>
              </Spin>
            )}
            {buyerLabelList?.buyerLabels?.map((item) => (
              <MemoryItem
                key={item.id}
                memory={item}
                onUpdate={handleUpdate}
                onDelete={handleRemove}
              />
            ))}
            {!buyerLabelList?.buyerLabels?.length && !isLoading && !newItem && (
              <div>
                <Empty
                  className="my-12"
                  description={
                    <div>
                      <p>暂无记忆</p>
                      <p>对话过程产生的关键信息，将在此处展示</p>
                    </div>
                  }
                />
              </div>
            )}
          </div>
        </div>
      </Spin>
    </Drawer>
  );
};

export default MemoryBottomSheet;
