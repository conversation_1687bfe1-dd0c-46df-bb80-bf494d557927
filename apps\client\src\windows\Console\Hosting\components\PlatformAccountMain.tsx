import type { ServiceAccount } from "@/business/components/ServiceccountProvider/store";
import useConfig from "@/business/hooks/useConfig";
import useFileDownload from "@/business/hooks/useFileDownload";
import useServiceAccount from "@/business/hooks/useServiceAccount";
import useTask from "@/business/hooks/useTask";
import { platformClientMap } from "@/constant/client";
import { SupportedPlatforms } from "@/constant/platform";
import { DownloadStatusMap, WsCmdSend } from "@/constant/protocol/index";
import { Platform } from "@workspace/business";
import { useDebounce } from "@workspace/ui";
import { Button, Collapse, Modal, Popconfirm } from "antd";
import { useCallback } from "react";
import { toast } from "sonner";
import CaretRightOutlined from "~icons/ant-design/caret-right-outlined";
import CloseOutlined from "~icons/ant-design/close-outlined";
import AccountList from "./AccountList";

const PlatformAccountMain = () => {
  const { store, socket } = useServiceAccount();
  const { store: configStore } = useConfig();
  const { files } = useFileDownload();
  const { addTasks } = useTask();

  const openPlatformClient = useDebounce((platform: Platform) => {
    socket?.sendMessage({
      cmd: WsCmdSend.OpenPlatformClient,
      data: {
        platformType: platform,
      },
    });
  }, 500);

  const handleCancelHosting = useCallback(
    (record: ServiceAccount) => {
      const key =
        platformClientMap[record.platformType as keyof typeof platformClientMap]
          .cancelHostingKey;
      store.setNotAutoHostAccountMap({
        [record.account]: record,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.CancelServiceHosting,
        data: {
          [key]: [record.sellerAccountForBackend],
        },
      });
    },
    [socket, store],
  );

  const handleHosting = useCallback(
    (record: ServiceAccount) => {
      const key =
        platformClientMap[record.platformType as keyof typeof platformClientMap]
          .hostingKey;
      store.setHostingAccountMap({
        [record.sellerAccountForBackend as string]: true,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.HostingServiceAccount,
        data: {
          [key]:
            record?.platformType === Platform.JD
              ? [`${record.sellerAccountForBackend}:0`] // 京东平台特殊标识
              : [record.sellerAccountForBackend],
        },
      });
    },
    [socket, store],
  );

  const handlePullImage = useCallback(
    (record: ServiceAccount) => {
      socket?.sendMessage({
        cmd: WsCmdSend.PullProductImage,
        data: {
          platformType: record.platformType,
          account: record.account,
        },
      });
    },
    [socket],
  );

  const handleInstallClient = useDebounce((platform: Platform) => {
    const targetClient = configStore.client.platformClient[platform];
    const target = configStore.supportedClientsInfo[platform]?.find((item) =>
      targetClient?.platformVersion
        ? item._id === targetClient?._id
        : item.isDefaultVs,
    );
    if (target?.fileUrl) {
      Modal.confirm({
        title: "即将开始下载客户端",
        content: `版本号：${target.version}`,
        onOk: () => {
          socket?.sendMessage({
            cmd: WsCmdSend.SetPlatformClientVersion,
            data: {
              platformType: platform,
              platformVersion: target.version,
              is64Bit: target.is64Bit,
            },
          });
          socket?.sendMessage({
            cmd: WsCmdSend.DownloadFile,
            data: {
              id: target._id,
              fileUrl: target.fileUrl,
              isPlatformSoftware: true,
              platformType: platform,
              is64Bit: target.is64Bit,
              platformVersion: target.version,
            },
          });
          addTasks([target._id]);
        },
      });
    } else {
      toast.error("当前没有可下载版本, 请联系客服人员");
    }
  }, 500);

  const handleCancelInstall = useDebounce((id: string) => {
    socket?.sendMessage({
      cmd: WsCmdSend.CancelDownload,
      data: {
        id,
      },
    });
  }, 500);

  const handleRefresh = useDebounce(() => {
    store.updateValidData(store.accountMap, true);
    socket?.sendMessage({
      cmd: WsCmdSend.RefreshAccountList,
      data: undefined,
    });
  }, 500);

  const openKsRobot = useDebounce(() => {
    socket?.sendMessage({
      cmd: WsCmdSend.OpenKsRobot,
      data: undefined,
    });
  }, 500);

  const items = Object.entries(store.accountMap)
    .filter(
      ([key]) =>
        SupportedPlatforms[Number(key) as keyof typeof SupportedPlatforms],
    )
    .map(([key, value]) => {
      const platform = Number(key) as keyof typeof platformClientMap;

      const client = {
        ...platformClientMap[platform],
        ...configStore.clientsInfo?.[platform],
      };

      const hasScan = !!configStore.clientsInfo?.[platform];

      const IconComponent = client.icon;
      const data = value.map((item) => ({
        ...item,
        isHosting:
          store.hostingAccountMap[item.sellerAccountForBackend as string],
      }));

      return {
        key: key,
        label: (
          <span className="flex items-center gap-2">
            <IconComponent />
            <span>{`${client.name}账号列表`}</span>
          </span>
        ),
        children: (
          <AccountList
            data={data}
            platformClient={client}
            onCancelHosting={handleCancelHosting}
            onHosting={handleHosting}
            onPullImage={handlePullImage}
          />
        ),
        extra: (
          <div className="space-x-4">
            <Button
              color="primary"
              htmlType="button"
              variant="outlined"
              onClick={(e) => {
                e.stopPropagation();
                handleRefresh();
              }}
            >
              刷新列表
            </Button>

            {client.platformType === Platform.KUAISHOU &&
              configStore.others.isKsRobotExist && (
                <Button
                  color="primary"
                  htmlType="button"
                  variant="outlined"
                  onClick={(e) => {
                    e.stopPropagation();
                    openKsRobot();
                  }}
                >
                  启动机器人
                </Button>
              )}

            {/* 1. 如果本地未存在，且没有找到下载任务
                2. 如果本地未存在，但是存在下载任务，且下载任务状态为安装成功 （程序被用户手动卸载的情况）
                以上情况下则显示立即安装按钮
            */}
            {!client?.isExist &&
            hasScan &&
            (!files[client._id] ||
              (files[client._id] &&
                ([DownloadStatusMap.InstallSuccess] as number[]).includes(
                  files[client._id]?.status,
                ))) ? (
              <Button
                color="primary"
                htmlType="button"
                variant="outlined"
                onClick={(e) => {
                  e.stopPropagation();
                  handleInstallClient(Number(key));
                }}
              >
                立即安装
              </Button>
            ) : null}
            {client?.isExist && (
              <Button
                variant="outlined"
                color="primary"
                htmlType="button"
                onClick={(e) => {
                  e.stopPropagation();
                  openPlatformClient(Number(key) as Platform);
                }}
              >
                {`启动${client.name}`}
              </Button>
            )}
            {!client?.isExist &&
              !!files[client._id] &&
              !(
                [
                  DownloadStatusMap.Installing,
                  DownloadStatusMap.InstallFailed,
                  DownloadStatusMap.InstallSuccess,
                ] as number[]
              ).includes(files[client._id].status) && (
                <Popconfirm
                  title={<span>你确定要取消吗?</span>}
                  onCancel={(e) => e?.stopPropagation()}
                  onConfirm={(e) => {
                    e?.stopPropagation();
                    handleCancelInstall(client._id);
                  }}
                >
                  <Button
                    variant="outlined"
                    color="primary"
                    htmlType="button"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <span>下载中 {files[client._id]?.progress}</span>
                    <CloseOutlined />
                  </Button>
                </Popconfirm>
              )}
            {!client?.isExist &&
              files[client._id]?.status === DownloadStatusMap.Installing && (
                <Button disabled>
                  <span>安装中...</span>
                </Button>
              )}
            {!client?.isExist &&
              files[client._id]?.status === DownloadStatusMap.InstallFailed && (
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleInstallClient(Number(key));
                  }}
                >
                  <span>安装失败，请重试</span>
                </Button>
              )}
          </div>
        ),
        classNames: {
          header: "flex !items-center ",
        },
      };
    });

  return (
    <Collapse
      bordered={false}
      defaultActiveKey={Object.keys(store.accountMap)}
      items={items}
      expandIcon={({ isActive }) => (
        <CaretRightOutlined className={isActive ? "rotate-90" : ""} />
      )}
      className="space-y-4 bg-transparent  [&_.ant-collapse-item]:shadow-sm rounded-none [&_.ant-collapse-item]:bg-white [&_.ant-collapse-item]:!rounded-8 [&_.ant-collapse-header]:border-b  overflow-hidden"
    />
  );
};

export default PlatformAccountMain;
