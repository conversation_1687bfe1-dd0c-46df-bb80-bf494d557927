import qs from "qs";

export const getFullPath = (
  path: string,
  config?: {
    query?: Record<string, unknown>;
    params?: Record<string, unknown>;
  },
) => {
  const _query = qs.stringify(config?.query || {}, {
    arrayFormat: "comma",
  });

  let _path = path;
  Object.keys(config?.params || {}).forEach((key) => {
    const reg = new RegExp(`(:${key}[?]*)`, "g");
    _path = _path.replace(reg, (config?.params?.[key] as string) || "");
  });

  _path = _path.replace(/\?/g, "");
  const fullPath = _query
    ? `${_path}?${_query}`.replace(/\/\//g, "/")
    : _path.replace(/\/\//g, "/");
  return fullPath;
};
