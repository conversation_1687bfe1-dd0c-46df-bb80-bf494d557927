import buyerIcon from "@/assets/svg/buyer-icon.svg";
import useSession from "@/business/hooks/useSession";
import {
  type IBuyerPortraitInfo,
  setBuyerSystemLabel,
} from "@/network/api/buyer";
import { Tag } from "antd";
import { useEffect } from "react";
import { useCopyToClipboard } from "react-use";
import { toast } from "sonner";
import CopyOutlined from "~icons/ant-design/copy-outlined";
import SyncOutlined from "~icons/ant-design/sync-outlined";

const NEED_VIEW_BUYER_ROLE_KEYS = ["S客", "差评师", "黑名单"];

const BuyerInfo = ({
  data,
  mutate,
  refresh,
}: {
  data?: IBuyerPortraitInfo;
  mutate?: () => void;
  refresh?: () => void;
}) => {
  const { buyer } = useSession();
  const [state, copyToClipboard] = useCopyToClipboard();

  useEffect(() => {
    if (state.value) {
      toast.success("复制成功");
    } else if (state.error) {
      toast.error("复制失败");
    }
  }, [state]);

  const handleCopy = () => {
    if (buyer?.buyerNick) {
      copyToClipboard(buyer.buyerNick);
    }
  };

  const handleTagChange = async (
    tag: IBuyerPortraitInfo["buyerInfo"][number],
  ) => {
    const res = await setBuyerSystemLabel({
      buyerNick: buyer?.buyerNick as string,
      remark: "",
      ...tag,
      value: tag.value === 1 ? 0 : 1,
    }).promise.catch((error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      mutate?.();
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-2 items-center text-14 font-bold">
        <div className="flex gap-1 items-center">
          <img src={buyerIcon} alt="" className="flex-none w-[14px] h-[14px]" />
          {buyer?.showCurrentBuyerNick}
        </div>
        <div
          className="text-12 text-black-2 cursor-pointer"
          onClick={handleCopy}
          onKeyDown={() => {}}
          title="复制用户ID"
        >
          <CopyOutlined />
        </div>
        <SyncOutlined
          className="ml-auto text-13 text-black-2"
          onClick={() => refresh?.()}
        />
      </div>
      <div>
        {data?.buyerInfo
          .filter((role) => NEED_VIEW_BUYER_ROLE_KEYS.includes(role.name))
          .map((tagItem) => (
            <Tag
              className="cursor-pointer"
              key={tagItem.order}
              color={tagItem.value === 1 ? "error" : "default"}
              onClick={() => handleTagChange(tagItem)}
            >
              {tagItem.name}
            </Tag>
          ))}
      </div>
    </div>
  );
};

export default BuyerInfo;
