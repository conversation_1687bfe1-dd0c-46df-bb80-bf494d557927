import { postVerifyCode } from "@/network/api/account";
import type { ButtonProps } from "antd";
import { useEffect, useState } from "react";
import { useInterval } from "react-use";

const useVerifyCode = (seconds: number) => {
  const [localSeconds, setLocalSeconds] = useState(seconds);
  const [isRunning, toggleIsRunning] = useState(false);
  const [token, setToken] = useState<string | undefined>();

  useInterval(
    () => {
      setLocalSeconds((s) => {
        if (s - 1 < 0) {
          return 0;
        }
        return s - 1;
      });
    },
    isRunning ? 1000 : null,
  );

  const sendVerifyCode = async (
    params: Parameters<typeof postVerifyCode>[0],
  ) => {
    const res = await postVerifyCode(params).promise.catch(
      (error: Error) => error,
    );
    if (res instanceof Error) return;
    if (res.data.success) {
      setToken(res.data.data);
      toggleIsRunning(true);
    }
  };

  useEffect(() => {
    if (localSeconds === 0) {
      toggleIsRunning(false);
      setLocalSeconds(seconds);
    }
  }, [localSeconds, seconds]);

  const getButtonProps = ({ disabled, ...others }: ButtonProps = {}) => {
    return {
      disabled: isRunning || disabled,
      children: isRunning
        ? `${localSeconds}秒后重发`
        : token
          ? "重新发送"
          : "获取验证码",
      ...others,
    };
  };

  return {
    token,
    sendVerifyCode,
    getButtonProps,
  };
};

export default useVerifyCode;
