import type { FormatedAgentMessageType } from "@/business/components/AgentProvider/store";
import useAgent from "@/business/hooks/useAgent";
import useSession from "@/business/hooks/useSession";
import useRemote from "@/hooks/useRemote";
import {
  AgentCardTypeMap,
  type AgentMessageType,
  getAgentMessageList,
  postMarkAgentMessageApproval,
} from "@/network/api/copilot";
import { tryCatch } from "@/utils";
import logger from "@/utils/log";
import type { PageResults } from "@workspace/types";
import { ScrollArea, useDebounce, useLast, useThrottle } from "@workspace/ui";
import { message } from "antd";
import clsx from "clsx";
import { useContext, useEffect, useRef, useState } from "react";
import DoubleRightOutlined from "~icons/ant-design/double-right-outlined";
import AgentContext from "../context";
import { Product, Reply, Urge } from "./Items";
import Tool from "./Items/Tool";

const MessageList = () => {
  const { buyer, serviceAccount } = useSession();
  const {
    store: { lastMsg },
  } = useAgent();
  const { updateShortcutIndexMap } = useContext(AgentContext);
  const scrollViewRef = useRef<HTMLDivElement>(null);
  const [messages, setMessages] = useState<FormatedAgentMessageType[]>([]);
  const lastMessages = useRef<FormatedAgentMessageType[]>(messages);
  const [newTopicNum, setNewTopicNum] = useState(0);
  const getBuyerNick = useLast(buyer?.buyerNick);
  const getSellerAccount = useLast(serviceAccount?.account);
  useRemote(
    buyer?.buyerNick ? [getAgentMessageList.name, buyer.buyerNick] : null,
    ([, buyerAccount]) => {
      const params = {
        buyerAccount,
        pageSize: 30,
      };
      return getAgentMessageList(params);
    },
    {
      onSuccess(data: PageResults<AgentMessageType>) {
        const results =
          data.results?.map((item) => {
            const parsedPayload =
              tryCatch(() => JSON.parse(item.payload)) || {};

            return {
              ...item,
              parsedPayload,
            };
          }) || [];
        setMessages(results);
        updateShortcutIndexMap(results);
      },
    },
  );

  const handleMoveToTop = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
      setNewTopicNum(0);
    }
  };

  const handleSend = (id: string) => {
    setMessages((state) =>
      state.map((item) => {
        if (item.id === id) {
          return { ...item, ifSend: true };
        }
        return item;
      }),
    );
  };

  const handleMarkReview = useDebounce(async (data: AgentMessageType) => {
    // if (data.reviewStatus === 2) {
    //   toast.warning("该标记已处理，无法取消");
    //   return;
    // }
    const ifLabel = !data.ifLabel;
    const res = await postMarkAgentMessageApproval({
      cardId: data.id,
      thirdShopId: data.thirdShopId,
      ifLabel,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error || !res.data.success) return;
    message.success(ifLabel ? "标记成功" : "已取消标记");
    setMessages((message) =>
      message.map((item) => {
        if (item.id === data.id) {
          return { ...item, ifLabel };
        }
        return item;
      }),
    );
  }, 500);

  const onScroll = useThrottle(() => {
    requestAnimationFrame(() => {
      (scrollViewRef.current?.scrollTop || 0) < 100 && setNewTopicNum(0);
    });
  }, 200);

  useEffect(() => {
    if (!lastMsg) return;
    // 校验消息是否属于当前买家
    if (
      lastMsg.buyerAccount !== getBuyerNick() ||
      lastMsg.sellerAccount !== getSellerAccount()
    ) {
      logger.info("[MessageList] message not belong to current buyer");
      return;
    }
    setMessages((state) => {
      const { type, id } = lastMsg;
      const targetIndex = state.findIndex(
        (item) => item.id === id && type === item.type,
      );
      let list = state;

      if (targetIndex === -1) {
        list = [lastMsg, ...state].slice(0, 30);
      } else {
        list = [
          ...state.slice(0, targetIndex),
          lastMsg,
          ...state.slice(targetIndex + 1),
        ].slice(0, 30);
      }

      setTimeout(() => {
        updateShortcutIndexMap(list);
      });
      return list;
    });
  }, [getBuyerNick, getSellerAccount, lastMsg, updateShortcutIndexMap]);

  useEffect(() => {
    if (lastMessages.current[0]?.id === messages[0]?.id) return;
    // 如果滚动距离超过150，则提示新话题+1
    lastMessages.current = messages;
    if ((scrollViewRef.current?.scrollTop || 0) > 150) {
      setNewTopicNum((state) => Math.min(state + 1, 50));
    }
  }, [messages]);

  return (
    <ScrollArea
      className="flex-1 h-0 relative [&_div.scroll-thumb]:bg-black-2"
      viewportRef={scrollViewRef}
      onViewportScroll={onScroll}
    >
      <div className="empty:p-0 space-y-2 py-2 h-full">
        {messages.map((item, index) => {
          if (item.type === AgentCardTypeMap.CONSULT_REPLY.value) {
            return (
              <Reply
                key={item.id}
                data={item}
                onSend={handleSend}
                onMarkReview={handleMarkReview}
                shortcutIndex={index <= 8 ? index + 1 : undefined}
              />
            );
          }
          if (item.type === AgentCardTypeMap.CONSULT_PRODUCT.value) {
            return (
              <Product
                key={item.id}
                data={item}
                onSend={handleSend}
                onMarkReview={handleMarkReview}
                shortcutIndex={index <= 8 ? index + 1 : undefined}
              />
            );
          }
          if (item.type === AgentCardTypeMap.TOOL_AGENT.value) {
            return <Tool key={item.id} data={item} />;
          }
          return (
            <Urge
              key={item.id}
              data={item}
              onSend={handleSend}
              onMarkReview={handleMarkReview}
              shortcutIndex={index <= 8 ? index + 1 : undefined}
            />
          );
        })}
      </div>
      <button
        type="button"
        className={clsx(
          "absolute right-0 transition-all overflow-hidden top-0 flex items-center border-none outline-none cursor-pointer ml-auto rounded-l-full px-4 py-2 text-14 text-blue-7 bg-white shadow-md mt-2 space-x-1",
          newTopicNum ? "translate-x-0" : "translate-x-full",
        )}
        onClick={() => handleMoveToTop()}
      >
        <DoubleRightOutlined style={{ transform: "rotate(-90deg)" }} />
        <span>{newTopicNum}</span>
        <span>条新话题</span>
      </button>
    </ScrollArea>
  );
};

export default MessageList;
