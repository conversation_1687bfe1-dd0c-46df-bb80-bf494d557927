import {
  executionStatusMap,
  type ToolParameter,
  type FormatedAgentMessageType,
} from "@/business/components/AgentProvider/store";
import { useMemo } from "react";
import QuestionCircleOutlined from "~icons/ant-design/question-circle-outlined";
import { formatTime } from "../../../utils";
import { Tag } from "antd";
import FlagFilled from "@/assets/svg/flag-filled.svg?react";

interface ToolContentProps {
  data: FormatedAgentMessageType<"TOOL_CALL">;
}

const flagColorMap = {
  "1": "text-red-7",
  "2": "text-orange-7",
  "3": "text-green-7",
  "4": "text-blue-7",
  "5": "text-purple-7",
};

const getFieldRender = (item: ToolParameter) => {
  const fieldMap = {
    flag:
      item.value === "0" ? (
        "不标记"
      ) : (
        <FlagFilled
          className={
            flagColorMap?.[item.value as keyof typeof flagColorMap] ??
            "text-gray-5"
          }
        />
      ),
  };
  return fieldMap?.[item.field as keyof typeof fieldMap] ?? item.value;
};

const ToolContent = ({ data }: ToolContentProps) => {
  const preData = useMemo(() => {
    return {
      ...data.parsedPayload,
      time: formatTime(data.time),
      status: data.status,
      buyerAccount: data.buyerAccount,
      id: data.id,
      ifLabel: data.ifLabel,
    };
  }, [data]);

  const contentList = useMemo(() => {
    const parameters =
      preData.parameters?.map((item) => ({
        label: item.label,
        value: getFieldRender(item),
      })) ?? [];
    const status = executionStatusMap?.[preData.executionStatus];
    return [
      { label: "订单号", value: preData.id },
      ...parameters,
      {
        label: "执行结果",
        value: <Tag color={status.status}>{status.label}</Tag>,
      },
      ...(preData.executionStatus === executionStatusMap.FAILED.value
        ? []
        : [{ label: "失败原因", value: preData.failureReason }]),
    ];
  }, [preData]);

  return (
    <div className="px-2 pb-2 text-12">
      <div className="flex pt-1 flex-col gap-1.5 text-black-3">
        {contentList.map((item) => (
          <div key={item.label} className="flex items-center">
            <span className="text-black-4 font-medium after:content-[':'] after:mx-1 shrink-0">
              {item.label}
            </span>
            <span className="w-0 flex-1 truncate">{item.value}</span>
          </div>
        ))}
      </div>
      <div className="pt-1.5 text-black-2 flex items-center">
        <QuestionCircleOutlined />
        <span className="pl-0.5">{preData.toolName}</span>
      </div>
    </div>
  );
};

export default ToolContent;
