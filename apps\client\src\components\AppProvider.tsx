import { ConfigProvider } from "antd";
// 由于 antd 组件的默认文案是英文，所以需要修改为中文
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { theme } from "@workspace/design-token";
import zhCN from "antd/locale/zh_CN";
import { Outlet } from "react-router";
import { Toaster } from "sonner";
import { SWRConfig } from "swr";
import CheckCircleFilled from "~icons/ant-design/check-circle-filled";
import CloseCircleFilled from "~icons/ant-design/close-circle-filled";
import ExclamationCircleFilled from "~icons/ant-design/exclamation-circle-filled";
import InfoCircleFilled from "~icons/ant-design/info-circle-filled";
dayjs.locale("zh-cn");

const Provider = () => {
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: theme.colors.blue[7],
          colorBorder: theme.colors.gray[4],
          colorLink: theme.colors.blue[7],
          borderRadius: 4,
          colorBgContainerDisabled: theme.colors.gray[2],
          borderRadiusLG: 8,
        },
        components: {
          Table: {
            headerBg: theme.colors.gray[1],
            headerColor: theme.colors.black[4],
            rowSelectedBg: "transparent",
            rowSelectedHoverBg: "#fafafa",
          },
          Form: {
            itemMarginBottom: 16,
          },
        },
      }}
    >
      <SWRConfig
        value={{
          revalidateOnFocus: false,
        }}
      >
        <Outlet />
        <Toaster
          position="top-center"
          expand={true}
          visibleToasts={1}
          icons={{
            success: <CheckCircleFilled className="text-green-7" />,
            info: <InfoCircleFilled className="text-blue-7" />,
            warning: <ExclamationCircleFilled className="text-orange-7" />,
            error: <CloseCircleFilled className="text-red-7" />,
          }}
          offset={{ left: 0, right: 0 }}
          mobileOffset={{ left: 0, right: 0 }}
          toastOptions={{
            style: {
              maxWidth: "fit-content", // 自适应内容宽度
              margin: "0 auto",
            },
          }}
        />
      </SWRConfig>
    </ConfigProvider>
  );
};

export default Provider;
