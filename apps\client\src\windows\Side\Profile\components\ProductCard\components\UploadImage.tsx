import useSession from "@/business/hooks/useSession";
import { Modal, Upload } from "antd";
import type { RcFile, UploadProps } from "antd/es/upload";
import type { UploadFile } from "antd/es/upload/interface";
import { useEffect, useState } from "react";
import PlusOutlined from "~icons/ant-design/plus-outlined";

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

interface UploadImageProps {
  shopId?: string;
  style?: React.CSSProperties;
  list: Array<string>;
  changeList: (fileList: string[]) => void;
}

function UploadImage(props: UploadImageProps) {
  const { token, groupId } = useSession();
  const urlsToUploadFiles = (urls: string[] | undefined | null) =>
    urls ? urls.map((url) => ({ uid: url, name: url, url })) : [];

  const { list, changeList } = props;
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [fileList, setFileList] = useState<UploadFile[]>(
    urlsToUploadFiles(props.list),
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => setFileList(urlsToUploadFiles(props.list)), [props.list]);

  const handleCancel = () => setPreviewOpen(false);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleChange: UploadProps["onChange"] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    const canUseFileList = newFileList
      .filter((file) => file.status === "done" && file.response)
      .map((canUseFile) => canUseFile.response.data) as string[];
    if (JSON.stringify(canUseFileList) !== JSON.stringify(list)) {
      changeList(canUseFileList);
    }
  };

  const params = {
    shopId: props.shopId,
    "tanyu-group-id": groupId,
    token: token,
  };
  return (
    <div style={props.style}>
      <Upload
        action="/api/im/workorder/upload"
        data={params}
        listType="picture-card"
        fileList={fileList}
        onPreview={handlePreview}
        onChange={handleChange}
      >
        {fileList.length < 1 && <PlusOutlined />}
      </Upload>
      <Modal
        open={previewOpen}
        title={"图片预览"}
        footer={null}
        onCancel={handleCancel}
      >
        <img alt="example" style={{ width: "100%" }} src={previewImage} />
      </Modal>
    </div>
  );
}

export default UploadImage;
