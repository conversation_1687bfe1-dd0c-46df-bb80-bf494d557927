import client from "@/network/client";
import type { Platform } from "@workspace/business";
import type { APIResponse } from "@workspace/types";

export type ServiceAccountRemoteInfo = {
  autoType: number;
  sellerAccount: string;
  groupId: string;
  sellerId: string;
  shopId: string;
  thirdShopId: string;
  /** 账号状态 0-正常 1-未授权 2-席位数不够 */
  status: 0 | 1 | 2;
  state: number;
  selfSupport: 0 | 1;
  platform: Platform;
};

/** 获取账号列表状态信息 */
export function getScanAccountStateByIdAndPlatform(data: {
  list: {
    platform: number;
    accountId?: string;
    sellerAccount: string;
    nick: string;
  }[];
  refresh?: boolean;
}) {
  return client<APIResponse<{ list: ServiceAccountRemoteInfo[] }>>(
    "/api/im/agent-account/scan-account-info",
    {
      method: "POST",
      data,
    },
  );
}
