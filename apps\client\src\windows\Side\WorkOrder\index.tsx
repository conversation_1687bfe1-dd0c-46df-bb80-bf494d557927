import useServiceAccount from "@/business/hooks/useServiceAccount";
import useSession from "@/business/hooks/useSession";
import useWebSocket from "@/business/hooks/useWebsocket";
import Logger from "@/class/Logger";
import { WsCmdSend } from "@/constant/protocol";
import { useLast } from "@workspace/ui";
import { useCallback, useEffect, useMemo, useRef } from "react";

const logger = new Logger("workOrder");

const PostMessageTypeMap = {
  getInfo: "getInfo",
  tokenChange: "tokenChange",
  sendOpenBuyerDialog: "sendOpenBuyerDialog",
  openUrlInWeb: "openUrlInWeb",
  accountInfo: "accountInfo",
};

export const Component: React.FC = () => {
  const socket = useWebSocket();
  const {
    store: { tanyuGroupSessions },
  } = useServiceAccount();
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  const { token, buyer, serviceAccount, platformType, groupId } = useSession();
  const getBuyer = useLast(buyer);
  const getServiceAccount = useLast(serviceAccount);
  const getPlatformType = useLast(platformType);
  const src = useRef<string>("");

  const targetGroup = useMemo(() => {
    if (!groupId) return;
    return tanyuGroupSessions[groupId];
  }, [tanyuGroupSessions, groupId]);
  const getGroupId = useLast(groupId);

  const searchParams = new URLSearchParams({
    tanyuGroupToken: targetGroup?.token as string,
    sellerToken: token as string,
  });

  if (!src.current && targetGroup?.token && token) {
    src.current = [import.meta.env.VITE_AFTER_SALES_URL, searchParams].join(
      "?",
    );
  }

  const sendMessageToIframe = useCallback(
    (type: string, value: Record<string, any>) => {
      const payload = {
        type,
        value,
      };
      logger.info("[send.iframe.message]", payload);
      if (!iframeRef.current) {
        logger.error("[send.iframe.message]", "cant not find iframe", payload);
        return;
      }
      iframeRef.current?.contentWindow?.postMessage(payload, "*");
    },
    [],
  );

  const openConversation = useCallback(
    (buyerNick: string) => {
      if (buyerNick) {
        socket?.sendMessage({
          cmd: WsCmdSend.OpenConversation,
          data: {
            currentBuyerUserId: buyerNick,
            account: getServiceAccount()?.account as string,
            isTop: true,
            platformType: getPlatformType(),
            shopName: getServiceAccount()?.account?.split(":")?.[0],
          },
        });
      }
    },
    [socket, getServiceAccount, getPlatformType],
  );

  const previewResource = useCallback(
    (url: string) => {
      url &&
        socket?.sendMessage({
          cmd: WsCmdSend.PreviewResource,
          data: {
            title: "",
            url,
          },
        });
    },
    [socket],
  );

  // token变化需要通知售后工单页面，避免请求失败
  useEffect(() => {
    if (token && targetGroup?.token) {
      sendMessageToIframe(PostMessageTypeMap.tokenChange, {
        sellerToken: token,
        tanyuGroupToken: targetGroup?.token,
      });
    }
  }, [token, sendMessageToIframe, targetGroup?.token]);

  // 买家变化，需要通知售后工单页面
  useEffect(() => {
    if (buyer?.buyerNick) {
      sendMessageToIframe(PostMessageTypeMap.accountInfo, {
        buyerNick: buyer.buyerNick,
        sellerAccount: getServiceAccount()?.account,
        platformType: getPlatformType(),
        tanyuAccountId: getGroupId(),
      });
    }
  }, [
    buyer?.buyerNick,
    getServiceAccount,
    getPlatformType,
    getGroupId,
    sendMessageToIframe,
  ]);

  useEffect(() => {
    const handler = (event: MessageEvent) => {
      logger.info("[recive.iframe.message]", event.data);
      const { type, value } = event.data;
      switch (type) {
        /** 获取买家、客服、集团信息 */
        case PostMessageTypeMap.getInfo:
          sendMessageToIframe(PostMessageTypeMap.accountInfo, {
            buyerNick: getBuyer()?.buyerNick,
            sellerAccount: getServiceAccount()?.account,
            platformType: getPlatformType(),
            tanyuAccountId: getGroupId(),
          });

          break;
        /** 打开指定买家聊天窗口 */
        case PostMessageTypeMap.sendOpenBuyerDialog:
          openConversation(value.buyerNick);
          break;
        /** 资源预览 */
        case PostMessageTypeMap.openUrlInWeb: {
          previewResource(value.url);
          break;
        }

        default:
          break;
      }
    };
    window.addEventListener("message", handler);
    return () => {
      window.removeEventListener("message", handler);
    };
  }, [
    sendMessageToIframe,
    openConversation,
    previewResource,
    getBuyer,
    getGroupId,
    getServiceAccount,
    getPlatformType,
  ]);

  if (!src) return null;
  return (
    <iframe
      ref={iframeRef}
      title="智能工单"
      id="workOrder"
      src={src.current}
      className="flex-1 w-full"
    />
  );
};

Component.displayName = "WorkOrder";
