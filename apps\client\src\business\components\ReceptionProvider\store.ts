import { SupportedPlatforms } from "@/constant/platform";
import type { Conversation } from "@/constant/protocol";
import { createStore } from "zustand";

export const sortedMap = {
  Ascending: "Ascending",
  Descending: "Descending",
} as const;

const initTypeState = {
  replyed: true,
  unreplyed: true,
};

const initStarState = {
  red: true,
  yellow: true,
  blue: true,
  green: true,
};

export const TabTypeMap = {
  reception: "reception",
  starMark: "starMark",
};

const initPlatformState = Object.fromEntries(
  Object.entries(SupportedPlatforms).map(([key]) => [key, true]),
) as Record<keyof typeof SupportedPlatforms, boolean>;

interface ReceptionState {
  unsubscribe: (() => void) | undefined;
  setSubscribe: (unsubscribe: (() => void) | undefined) => void;
  conversations: Conversation[];
  conversationMap: Map<string, Conversation>;
  addConversations: (conversations: ReceptionState["conversations"]) => void;
  updateConversation: (
    conversations: Partial<Conversation> & { sessionKey: string },
  ) => void;
  removeConversation: (payload: {
    sessionKey?: string;
    shopId: string;
    account: string;
  }) => void;
  clearConversations: () => void;
  shortcut: {
    openConversation: string | undefined;
  };
  setShortcut: (shortcut: Partial<ReceptionState["shortcut"]>) => void;
  sorter: keyof typeof sortedMap;
  setSorter: (sorter: keyof typeof sortedMap) => void;
  filter: {
    platform: typeof initPlatformState;
    type: typeof initTypeState;
    starType: typeof initStarState;
  };
  setFilter: (filter: {
    platform: Partial<typeof initPlatformState>;
    type?: Partial<typeof initTypeState>;
    starType?: Partial<typeof initStarState>;
  }) => void;
  tabActive: string;
  setTabActive: (tabActive: string) => void;
}

export const receptionStore = createStore<ReceptionState>((set, get) => ({
  conversations: [],
  conversationMap: new Map(),
  addConversations: (conversations: ReceptionState["conversations"]) => {
    const conversationMap = new Map(get().conversationMap);
    conversations.forEach((item) => {
      const old = conversationMap.get(item.sessionKey);
      if (old) {
        conversationMap.set(item.sessionKey, {
          ...old,
          ...item,
          // 如果前值是不显示读秒，则需要更新到最新的消息时间，反之，则取旧值
          // 读秒显示时间 = 当前时间 - msgTime字段的值
          msgTime: old.isRead ? old.msgTime : item.msgTime,
        });
      } else {
        conversationMap.set(item.sessionKey, item);
      }
    });
    set({
      conversations: Array.from(conversationMap.values()),
      conversationMap,
    });
  },
  removeConversation: (payload: {
    sessionKey?: string;
    account: string;
    shopId: string;
  }) => {
    const conversationMap = get().conversationMap;
    if (payload.sessionKey) {
      conversationMap.delete(payload.sessionKey);
    } else {
      conversationMap.forEach((item, sessionKey) => {
        if (
          item.account === payload.account &&
          item.shopId === payload.shopId
        ) {
          conversationMap.delete(sessionKey);
        }
      });
    }
    set({
      conversations: Array.from(conversationMap.values()),
      conversationMap,
    });
  },
  updateConversation: (
    conversation: Partial<Conversation> & { sessionKey: string },
  ) => {
    const oldConversation = get().conversationMap.get(conversation.sessionKey);

    if (!oldConversation) return;
    const conversationMap = new Map(
      get().conversationMap.set(conversation.sessionKey, {
        ...oldConversation,
        ...conversation,
      }),
    );
    set({
      conversations: Array.from(conversationMap.values()),
      conversationMap,
    });
  },
  clearConversations: () => {
    set({
      conversations: [],
      conversationMap: new Map(),
    });
  },
  shortcut: {
    openConversation: undefined,
  },
  setShortcut: (shortcut: Partial<ReceptionState["shortcut"]>) => {
    set({
      shortcut: {
        ...get().shortcut,
        ...shortcut,
      },
    });
  },
  unsubscribe: undefined,
  setSubscribe: (unsubscribe: (() => void) | undefined) =>
    set({
      unsubscribe: () => {
        unsubscribe?.();
        set({ unsubscribe: undefined });
      },
    }),
  filter: Object.assign(
    {},
    {
      type: initTypeState,
      platform: initPlatformState,
      starType: initStarState,
    },
    JSON.parse(localStorage.getItem("filter") || "{}"),
  ),
  setFilter: (filter) => {
    const _filter = {
      platform: {
        ...get().filter.platform,
        ...filter.platform,
      },
      type: {
        ...get().filter.type,
        ...filter.type,
      },
      starType: {
        ...get().filter.starType,
        ...filter.starType,
      },
    };
    localStorage.setItem("filter", JSON.stringify(_filter));
    set({
      filter: _filter,
    });
  },
  sorter: (localStorage.getItem("sorter") ||
    sortedMap.Ascending) as keyof typeof sortedMap,
  setSorter: (sorter) => {
    localStorage.setItem("sorter", sorter);
    set({ sorter });
  },
  tabActive: localStorage.getItem("tabActive") || TabTypeMap.reception,
  setTabActive: (tabActive) => {
    localStorage.setItem("tabActive", tabActive);
    set({ tabActive });
  },
}));
