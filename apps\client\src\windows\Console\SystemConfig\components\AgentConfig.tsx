import useConfig from "@/business/hooks/useConfig";
import { WsCmdSend } from "@/constant/protocol";
import { Form, type FormProps, Switch } from "antd";
import Section from "./Section";

const AgentConfig = () => {
  const { store, socket } = useConfig();

  const onValuesChange: FormProps["onValuesChange"] = (_, allValues) => {
    socket?.sendMessage({
      cmd: WsCmdSend.SetAgentConfig,
      data: {
        oldSidebarSwitch: allValues.oldSidebarSwitch,
      },
    });
    store.setConfig({
      agentConfig: {
        ...store.agentConfig,
        ...allValues,
      },
    });
  };

  return (
    <Form
      name="agentConfig"
      initialValues={store.agentConfig}
      onValuesChange={onValuesChange}
    >
      <Section title="旧版侧边栏">
        <div className="flex items-center gap-2">
          <Form.Item noStyle name="oldSidebarSwitch">
            <Switch />
          </Form.Item>
          <span>开启后，客户端将使用旧版客服机器人的侧边栏</span>
        </div>
      </Section>
    </Form>
  );
};
export default AgentConfig;
