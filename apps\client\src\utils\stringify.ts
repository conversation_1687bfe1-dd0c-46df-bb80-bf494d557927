// 处理不同类型参数的安全序列化
export const safeStringify = (arg: unknown) => {
  if (arg === undefined) return "undefined";
  if (arg === null) return "null";

  try {
    // 处理特殊对象类型
    if (arg instanceof Error) {
      return JSON.stringify({
        name: arg.name,
        message: arg.message,
        stack: arg.stack,
      });
    }

    // 处理循环引用
    const seen = new WeakSet();
    return JSON.stringify(
      arg,
      (_, value) => {
        if (typeof value === "object" && value !== null) {
          if (seen.has(value)) {
            return "[Circular]";
          }
          seen.add(value);
        }
        // 处理特殊函数
        if (typeof value === "function") {
          return value.toString();
        }
        return value;
      },
      2,
    );
  } catch (e) {
    // 如果JSON.stringify失败，尝试备用方法
    try {
      return String(arg);
    } catch {
      return "[Unable to stringify]";
    }
  }
};
