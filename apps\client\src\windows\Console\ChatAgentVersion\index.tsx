import useRemote from "@/hooks/useRemote";
import {
  getBindVersion,
  getStableVersion,
  getVersionList,
} from "@/network/api/agent";
import { bindUserToVersion } from "@/network/api/agent";
import { getFullPath } from "@/utils/route";
import { <PERSON>ert, Button, Form, Radio } from "antd";
import { useNavigate, useSearchParams } from "react-router";
import { toast } from "sonner";
import Arrow<PERSON>eftOutlined from "~icons/ant-design/arrow-left-outlined";
import VersionItem from "./components/VersionItem";

export const Component: React.FC = () => {
  const [searchParams] = useSearchParams();
  const account = searchParams.get("account");
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const { data } = useRemote([getVersionList.name], getVersionList);

  const { data: stable } = useRemote([getStableVersion.name], getStableVersion);

  useRemote(
    account ? [getBindVersion.name, account] : null,
    () => getBindVersion({ account: account!, type: 0 }),
    {
      onSuccess: (data) => {
        form.setFieldsValue({
          version: data.ifDefault ? 1 : 2,
          workFlowVersionId: data.id,
        });
      },
    },
  );

  const goBack = () => {
    navigate(
      getFullPath("/console/hosting", {
        query: { ...Object.fromEntries(searchParams), account: undefined },
      }),
    );
  };

  const handleFinish = async (values: {
    version: number;
    workFlowVersionId: string;
  }) => {
    const isStable =
      values.workFlowVersionId === stable?.id || values.version === 1;
    const res = await bindUserToVersion({
      account: searchParams.get("account")!,
      ifLatestStableVersion: isStable ? 1 : undefined,
      type: 0,
      workFlowVersionId: isStable ? undefined : values.workFlowVersionId,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      goBack();
    }
  };

  return (
    <div className="flex-1 flex flex-col h-screen w-full relative">
      <div className="bg-white pb-2 px-2 border-b">
        <div className="flex items-center gap-2">
          <Button type="text" onClick={() => goBack()}>
            <ArrowLeftOutlined />
          </Button>
          <h1>版本设置</h1>
        </div>
      </div>
      <div className="flex flex-col justify-center p-8 w-full ">
        <Alert
          showIcon
          className="w-full mb-4"
          message={
            <div>
              1. 推荐选择【稳定版最新】，系统将动态使用最新的稳定版
              <br />
              2. 有特殊需要可指定版本使用
              <br />
              3. 开发版可能存在不稳定，请谨慎选择
              <br />
              4. 变更版本需<span className="text-orange-7">重新托管</span>后生效
            </div>
          }
        />
        <Form
          form={form}
          id="chat-agent-version"
          initialValues={{ version: 1 }}
          className="w-full"
          onFinish={handleFinish}
        >
          <Form.Item
            label="选择agent版本"
            name="version"
            required
            className="mb-2"
          >
            <Radio.Group
              options={[
                { label: "稳定版最新", value: 1 },
                { label: "指定版本", value: 2 },
              ]}
            />
          </Form.Item>
          <Form.Item noStyle dependencies={["version"]}>
            {({ getFieldValue }) => {
              const version = getFieldValue("version");
              if (version === 1) return null;
              return (
                <Form.Item name="workFlowVersionId">
                  <Radio.Group className="w-full space-y-2 max-h-[350px] overflow-y-auto overflow-x-hidden">
                    {data?.map((mapItem) => (
                      <VersionItem key={mapItem.id} record={mapItem} />
                    ))}
                  </Radio.Group>
                </Form.Item>
              );
            }}
          </Form.Item>
        </Form>
      </div>
      <div className="fixed bottom-0 right-0 p-4 w-full border-t border-3 z-10 bg-white">
        <div className="flex justify-end w-full gap-2">
          <Button onClick={() => goBack()}>取消</Button>
          <Button type="primary" htmlType="submit" form="chat-agent-version">
            确认
          </Button>
        </div>
      </div>
    </div>
  );
};
Component.displayName = "ChatAgentVersion";
