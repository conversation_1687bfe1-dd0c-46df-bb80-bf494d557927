import useWebSocket from "@/business/hooks/useWebsocket";
import { Paths } from "@/config/path";
import { WsCmdSend } from "@/constant/protocol";
import { sendMessages } from "@/network/api/base";
import { getFullPath } from "@/utils/route";
import { Image as AntdImage, Divider } from "antd";
import { useState } from "react";
import { toast } from "sonner";
import EyeFilled from "~icons/ant-design/eye-filled";
import SendIcon from "~icons/icon-park-outline/send";

interface ImageProps {
  url: string;
  buyerNick: string;
}

const Image = ({ url, buyerNick }: ImageProps) => {
  const [visible, setVisible] = useState(false);
  const socket = useWebSocket();
  const sendImg = async (url: string) => {
    const postData = {
      content: "",
      buyerAccount: buyerNick,
      imageUrls: [url],
    };
    const res = await sendMessages(postData).promise.catch((error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("发送成功");
    }
  };

  const handlePreview = () => {
    const path = getFullPath(Paths.Preview, {
      query: {
        href: url,
        type: "pic",
      },
      params: {
        versionNo: window.__WEB_VERSION__,
      },
    });
    socket?.sendMessage({
      cmd: WsCmdSend.PreviewResource,
      data: {
        title: "图片预览",
        url: `${window.location.origin}${path}`,
      },
    });
  };

  return (
    <AntdImage
      key={url}
      src={url}
      style={{ objectFit: "cover", borderRadius: 4 }}
      width={83}
      height={83}
      preview={{
        visible,
        mask: (
          <div
            className="flex items-center text-12"
            onClick={(e) => e.stopPropagation()}
            onKeyDown={(e) => e.stopPropagation()}
          >
            <div
              className="flex items-center flex-col"
              onClick={() => sendImg(url)}
              onKeyDown={() => {}}
            >
              <SendIcon style={{ fontSize: 14 }} className="text-white" />
              <span className="flex-shrink-0">发送</span>
            </div>
            <Divider type="vertical" className="mx-2 h-6 bg-white opacity-50" />
            <div
              className="flex items-center flex-col"
              onClick={() => handlePreview()}
              onKeyDown={() => {}}
            >
              <EyeFilled style={{ fontSize: 14 }} />
              <span>预览</span>
            </div>
          </div>
        ),
        onVisibleChange: (value) => setVisible(value),
      }}
    />
  );
};
export default Image;
