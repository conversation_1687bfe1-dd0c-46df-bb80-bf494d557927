import { TabTypeMap } from "@/business/components/ReceptionProvider/store";
import { type SupportPlatform, SupportedPlatforms } from "@/constant/platform";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@workspace/ui";
import { usePartialState } from "@workspace/ui";
import FilterOutlined from "~icons/ant-design/filter-outlined";

const platoformOptions = Object.values(SupportedPlatforms);

const typeOptions = [
  {
    label: "回复",
    value: "replyed",
  },
  {
    label: "未回复",
    value: "unreplyed",
  },
];

const starList = [
  {
    value: "red",
    label: "红色",
    backgroundColor: "#FF574D",
  },
  {
    value: "yellow",
    label: "黄色",
    backgroundColor: "#FFB340",
  },
  {
    value: "blue",
    label: "蓝色",
    backgroundColor: "#669EFF",
  },
  {
    value: "green",
    label: "绿色",
    backgroundColor: "#6CCC3D",
  },
];

const initStarState = {
  red: true,
  yellow: true,
  blue: true,
  green: true,
};

const initTypeState = {
  replyed: true,
  unreplyed: true,
};

const initPlatformState = Object.fromEntries(
  Object.entries(SupportedPlatforms).map(([key]) => [key, true]),
) as Record<keyof typeof SupportedPlatforms, boolean>;

export type FilterType = {
  starType?: Partial<typeof initStarState>;
  platform: Partial<typeof initPlatformState>;
  type?: Partial<typeof initTypeState>;
};
interface FilterProps {
  onChange?: (values: FilterType) => void;
  tabActive: string;
  defaultValue?: {
    starType?: typeof initStarState;
    platform: typeof initPlatformState;
    type?: typeof initTypeState;
  };
}

const Filter = ({ onChange, tabActive, defaultValue }: FilterProps) => {
  const [checkedPlatform, setCheckedOPtions] = usePartialState(
    defaultValue?.platform || initPlatformState,
  );

  const [checkedType, setCheckedType] = usePartialState(
    defaultValue?.type || initTypeState,
  );

  const [checkedStar, setCheckedStar] = usePartialState(
    defaultValue?.starType || initStarState,
  );

  const onPlatformChange = (value: Partial<typeof initPlatformState>) => {
    setCheckedOPtions(value);
    onChange?.({
      platform: {
        ...checkedPlatform,
        ...value,
      },
      type: checkedType,
      starType: checkedStar,
    });
  };

  const onTypeChange = (value: Partial<typeof initTypeState>) => {
    setCheckedType(value);
    onChange?.({
      platform: checkedPlatform,
      type: {
        ...checkedType,
        ...value,
      },
    });
  };

  const onStarChange = (value: Partial<typeof initStarState>) => {
    setCheckedStar(value);
    onChange?.({
      platform: checkedPlatform,
      starType: {
        ...checkedStar,
        ...value,
      },
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="outline-none">
        <FilterOutlined className="hover:text-blue-7 focus-visible:text-blue-7" />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white">
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <span>平台</span>
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent className="bg-white">
              {platoformOptions.map((item) => (
                <DropdownMenuCheckboxItem
                  key={item.value}
                  checked={checkedPlatform[item.value as SupportPlatform]}
                  onCheckedChange={(checked) =>
                    onPlatformChange({ [item.value]: checked })
                  }
                  onSelect={(e) => e.preventDefault()}
                  className="[&_svg]:text-blue-7"
                >
                  {item.label}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>
        {tabActive === TabTypeMap.starMark ? (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <span>星标</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent className="bg-white">
                {starList.map((item) => (
                  <DropdownMenuCheckboxItem
                    key={item.value}
                    checked={
                      checkedStar[item.value as keyof typeof initStarState]
                    }
                    onSelect={(e) => e.preventDefault()}
                    onCheckedChange={(checked) =>
                      onStarChange({ [item.value]: checked })
                    }
                    className="[&_svg]:text-blue-7"
                  >
                    <div className="gap-2 flex items-center justify-center">
                      <div
                        className="color w-[9px] h-[9px] rounded-full"
                        style={{ backgroundColor: item.backgroundColor }}
                      />
                      {item.label}
                    </div>
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
        ) : (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <span>类型</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent className="bg-white">
                {typeOptions.map((item) => (
                  <DropdownMenuCheckboxItem
                    key={item.value}
                    checked={
                      checkedType[item.value as keyof typeof initTypeState]
                    }
                    onSelect={(e) => e.preventDefault()}
                    onCheckedChange={(checked) =>
                      onTypeChange({ [item.value]: checked })
                    }
                  >
                    {item.label}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Filter;
