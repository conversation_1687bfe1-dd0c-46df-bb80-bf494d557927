import type { VitePluginSvgrOptions } from "vite-plugin-svgr";

const template: NonNullable<VitePluginSvgrOptions["svgrOptions"]>["template"] =
  (variables, { tpl }) => {
    return tpl`
    ${variables.imports};
    import { useId, useMemo } from 'react';

    ${variables.interfaces};

     const ${variables.componentName} = (${variables.props}) => {
      const uniqueId = useId();

      const processedChildren = useMemo(() => {
        const idMap = new Map();

        const replaceUrlIds = (value) => {
          if (typeof value !== 'string') return value;
          return value.replace(/url\\(#([^)]+)\\)/g, (match, id) => {
            const newId = idMap.get(id) || \`\${uniqueId}-\${id}\`;
            idMap.set(id, newId);
            return \`url(#\${newId})\`;
          });
        };

        const processNode = (node) => {
          if (typeof node !== 'object' || node === null) {
            return node;
          }

          if (node.props) {
            const newProps = { ...node.props };

            // Handle 'id' attribute
            if (newProps.id) {
              const newId = \`\${uniqueId}-\${newProps.id}\`;
              idMap.set(newProps.id, newId);
              newProps.id = newId;
            }

            // Handle attributes that might contain url(#id)
            ['clipPath', 'fill', 'filter', 'mask', 'stroke','markerStart','markerMid','markerEnd','fillOpacity','strokeOpacity','lightingColor','colorInterpolationFilters','stopColor','stopOpacity'].forEach(attr => {
              if (newProps[attr]) {
                newProps[attr] = replaceUrlIds(newProps[attr]);
              }
            });

            // Special handling for style object
            if (typeof newProps.style === 'object') {
              Object.keys(newProps.style).forEach(key => {
                newProps.style[key] = replaceUrlIds(newProps.style[key]);
              });
            }

            // Handle 'xlinkHref' attribute
            if (newProps.xlinkHref && newProps.xlinkHref.startsWith('#')) {
              const oldId = newProps.xlinkHref.slice(1);
              const newId = idMap.get(oldId) || \`\${uniqueId}-\${oldId}\`;
              newProps.xlinkHref = \`#\${newId}\`;
            }

            // Recursively process children
            if (newProps.children) {
              newProps.children = Array.isArray(newProps.children)
                ? newProps.children.map(processNode)
                : processNode(newProps.children);
            }

            return { ...node, props: newProps };
          }

          return node;
        };

        return Array.isArray(${variables.jsx}.props.children)
          ? ${variables.jsx}.props.children.map(processNode)
          : processNode(${variables.jsx}.props.children);
      }, []);

      return React.cloneElement(${variables.jsx}, ${variables.props}, processedChildren);
    };

    ${variables.exports};
  `;
  };

export default template;
