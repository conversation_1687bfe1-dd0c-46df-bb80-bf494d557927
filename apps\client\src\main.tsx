import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { RouterProvider } from "react-router";
import "@/styles/index.css";
import router from "./config/route.ts";
import "core-js/actual/object/has-own";

/** 初始化版本信息 */
const url = new URL(window.location.href);
const searchParams = url.searchParams;
if (searchParams.get("version")) {
  window.__CLIENT_VERSION__ = searchParams.get("version") || "";
}
window.__WEB_VERSION__ = __WEB_VERSION__;

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
);
