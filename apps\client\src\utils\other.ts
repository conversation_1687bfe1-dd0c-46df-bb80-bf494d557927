// import { safeStringify } from "./stringify";

import logger from "./log";

export const compare = (a: unknown, b: unknown) => {
  return JSON.stringify(a) === JSON.stringify(b);
  // return safeStringify(a) === safeStringify(b);
};

export const tryCatch = (fn: () => any, ...args: string[]) => {
  try {
    return fn();
  } catch (e) {
    logger.error(e, ...args);
    console.log(e);
  }
};

export const trimNewlines = (str: string) => {
  if (!str) return "";
  // 匹配字符串开头和结尾的所有形式的换行符（\n, \r, \r\n）
  return str.replace(/^[\r\n]+|[\r\n]+$/g, "");
};
