import FlagFilled from "@/assets/svg/flag-filled.svg?react";
import FlagOutlined from "@/assets/svg/flag-outlined.svg?react";
import Rewrite from "@/assets/svg/rewrite.svg?react";
import {
  ContentTypeMap,
  type FormatedAgentMessageType,
} from "@/business/components/AgentProvider/store";
import useAgent from "@/business/hooks/useAgent";
import { type BatchSendMessageData, MsgContentMap } from "@/network/api/base";
import { AgentMessageStatusMap } from "@/network/api/copilot";
import { trimNewlines } from "@/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  useDebounce,
} from "@workspace/ui";
import { Typography } from "antd";
import clsx from "clsx";
import { useContext, useMemo, useRef } from "react";
import QuestionCircleOutlined from "~icons/ant-design/question-circle-outlined";
import SendOutlined from "~icons/icon-park-outline/send";
import AgentContext from "../../../context";
import { formatTime } from "../../../utils";
import SkeletonWrapper from "../../SkeletonWrapper";
import MeidaContent from "../MediaContent";
import TextContent from "../TextContent";

interface ReplyProps {
  data: FormatedAgentMessageType<"CONSULT_REPLY">;
  onSend: (id: string) => void;
  onMarkReview: (value: FormatedAgentMessageType<"CONSULT_REPLY">) => void;
  shortcutIndex?: number;
}

const Reply = ({ data, onSend, onMarkReview, shortcutIndex }: ReplyProps) => {
  const { store: groupSendShortcut } = useAgent(
    (state) => state.shortcut.groupSendShortcut,
  );
  const { sendMessages, shortcutIndexMap, updateMsg } =
    useContext(AgentContext);

  const preData = useMemo(() => {
    const fallbackContent = "未找到相关资料";
    const chatLogTrick = (data.parsedPayload?.trickList || [])
      ?.filter((item) => item.referenceType === ContentTypeMap.CHAT)
      ?.flatMap((item) => item?.content?.map((str) => trimNewlines(str)))
      ?.filter(Boolean);

    return {
      ...data.parsedPayload,
      time: formatTime(data.time),
      status: data.status,
      originQuestion:
        data?.parsedPayload?.originQuestion?.replace("买家:", "") || "",
      replyContent: {
        content: chatLogTrick,
        useFallback: !chatLogTrick.length,
        fallbackContent,
      },
      ifSend: data.ifSend,
      buyerAccount: data.buyerAccount,
      id: data.id,
      ifLabel: data.ifLabel,
    };
  }, [data]);

  const localContent = useRef(preData.replyContent.content);

  const multMessageSendText = useDebounce(
    (items: BatchSendMessageData["msgContents"]) => {
      let editSend = undefined;
      const sourceContents = preData.replyContent.content;
      items.forEach((item) => {
        if (
          item.type === MsgContentMap.text &&
          !sourceContents.includes(item.value)
        ) {
          editSend = "1";
        }
      });
      sendMessages({
        msgContents: items,
        feature: {
          // agent相关
          agent: "1",
          sidebar: "1",
          editSend,
          agentTopicId: data.id,
          agentTopic: data.parsedPayload.topicName,
          logIsCredible: data.parsedPayload.extBody.logIsCredible,
        },
        payload: JSON.stringify({
          ...data.parsedPayload,
          trickList: data.parsedPayload?.trickList?.map((item) => ({
            ...item,
            content: item.content.map(
              (_, index) => localContent.current[index],
            ),
          })),
        }),
        cardId: data.id,
        buyerAccount: preData.buyerAccount,
      }).then((ifSuccess) => {
        if (ifSuccess) {
          onSend(data.id);
        }
      });
    },
    500,
    { leading: true, trailing: false },
  );

  const handleSendAll = () => {
    const msgContents = [
      ...localContent.current.map((item) => ({
        type: MsgContentMap.text,
        value: item,
      })),
      ...(data.parsedPayload.medias || [])
        .map((item) => {
          switch (item.copilotMediaType) {
            case "pic":
              return { type: MsgContentMap.image, value: item.url };
            case "video":
              return { type: MsgContentMap.video, value: item.url };
            case "link":
              return { type: MsgContentMap.text, value: item.url };
            default:
              break;
          }
        })
        .filter(Boolean),
    ] as BatchSendMessageData["msgContents"];
    multMessageSendText(msgContents);
  };

  const handleEdit = (text: string, index: number) => {
    localContent.current[index] = text;
    updateMsg({
      ...data,
      parsedPayload: {
        ...data.parsedPayload,
        trickList: data.parsedPayload?.trickList?.map((item) => ({
          ...item,
          content: item.content.map((_, i) => localContent.current[i]),
        })),
      },
    });
  };

  const startIndex = shortcutIndexMap.get(preData.id)?.[0];

  return (
    <div className="overflow-hidden bg-white rounded-8 relative [--card-theme:theme(colors.green.7)]">
      <div className="px-2 pt-1 pb-0.5 text-12 flex items-center bg-gradient-to-b from-green-50 to-white">
        {shortcutIndex ? (
          <span className="text-white bg-green-7 font-medium mr-1 rounded-sm w-4 h-4 flex items-center justify-center">
            {shortcutIndex}
          </span>
        ) : null}
        <span className="text-green-7 flex-shrink-0 font-medium">
          客服Agent
        </span>
        <span className="text-black-2 ml-2">{preData.time}</span>
        <div className="ml-auto flex items-center gap-2">
          {!preData?.replyContent?.useFallback ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger onClick={handleSendAll}>
                  {preData?.ifSend ? (
                    <button
                      type="button"
                      className="flex gap-0.5 border border-green-4 bg-green-1 rounded-4 items-center text-green-7 px-1 bg-transparent text-12 outline-none"
                    >
                      <SendOutlined className="text-14" />
                      已发送
                    </button>
                  ) : (
                    <div className="bg-transparent text-14 outline-none border-none text-blue-7 cursor-pointer p-1 hover:text-blue-5">
                      <SendOutlined />
                    </div>
                  )}
                </TooltipTrigger>
                <TooltipContent
                  collisionPadding={20}
                  className="max-w-[--radix-tooltip-content-available-width] whitespace-pre-wrap break-words bg-black-4"
                  avoidCollisions
                >
                  <span>
                    {shortcutIndex && groupSendShortcut
                      ? `发送（${groupSendShortcut} + ${shortcutIndex}）`
                      : "发送"}
                  </span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : null}
        </div>
      </div>
      <div className="space-y-1.5 p-2 pt-0">
        <div className="space-y-0.5 pt-1.5 border-t border-gray-3">
          <SkeletonWrapper.Header loading={!preData.originQuestion}>
            <div className="text-12 text-black-5">
              <span className="after:content-[':'] after:mx-1">买家</span>
              {preData.originQuestion}
            </div>
          </SkeletonWrapper.Header>
          <SkeletonWrapper.Header loading={!preData.topicName}>
            <div className="flex items-center">
              <Rewrite className="text-14" />
              <Typography.Text
                className={clsx(
                  "font-medium text-12",
                  preData.ifSend ? "text-black-3" : "text-black-4",
                )}
              >
                {preData.topicName}
              </Typography.Text>
            </div>
          </SkeletonWrapper.Header>
        </div>
        <SkeletonWrapper.Content
          loading={
            !preData.replyContent.content.length &&
            ![
              AgentMessageStatusMap.FAILED.value,
              AgentMessageStatusMap.COMPLETED.value,
            ].includes(preData.status)
          }
        >
          <div>
            {preData.replyContent.useFallback ? (
              <div className="text-12 py-1 px-2 bg-gray-50 transition-colors rounded-4 my-2">
                {preData.replyContent?.fallbackContent}
              </div>
            ) : (
              <div className="space-y-2 my-2">
                {preData.replyContent?.content?.map((content, index) => (
                  <TextContent
                    // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                    key={index}
                    ifSend={preData.ifSend}
                    text={content}
                    onContentChange={(text) => handleEdit(text, index)}
                    onSend={(value) => multMessageSendText(value)}
                    shortcutIndex={startIndex ? startIndex + index : undefined}
                  />
                ))}
              </div>
            )}
          </div>
          <MeidaContent
            data={data.parsedPayload.medias}
            buyerAccount={preData.buyerAccount}
            id={preData.id}
            shortcutIndex={
              startIndex
                ? startIndex + preData.replyContent?.content.length
                : undefined
            }
          />

          <div className="text-black-2 text-12 flex items-center">
            {preData?.extBody?.logIsCredible ? (
              <QuestionCircleOutlined />
            ) : null}
            {preData?.extBody?.logIsCredible === 1 ? (
              <span>采纳/全店</span>
            ) : preData?.extBody?.logIsCredible === -1 ? (
              <span>商详/聊天/采纳/全店</span>
            ) : (
              ""
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    type="button"
                    className="py-0.5 ml-auto"
                    onClick={() => onMarkReview(data)}
                  >
                    {!preData.ifLabel ? (
                      <FlagOutlined className="text-black-2" />
                    ) : (
                      <FlagFilled className="text-blue-7" />
                    )}
                  </button>
                </TooltipTrigger>
                <TooltipContent className="bg-black-4">
                  <div className="space-y-1">
                    <p>点击标记本次生成结果</p>
                    <p>可在调优工坊筛选查看</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </SkeletonWrapper.Content>
      </div>
    </div>
  );
};

export default Reply;
