import { throttle } from "lodash-es";
import { useMemo } from "react";
import useLast from "./useLast";

const useThrottle = <T extends (...args: any[]) => any>(
  fn: T,
  config?: Parameters<typeof throttle>[1],
  options?: Parameters<typeof throttle>[2],
) => {
  const getCallback = useLast(fn);
  const getConfig = useLast(config);
  return useMemo(
    () =>
      throttle(
        (...args: Parameters<T>) => {
          getCallback()(...args);
        },
        getConfig(),
        options,
      ) as unknown as T,
    [getCallback, getConfig, options],
  );
};

export default useThrottle;
