import { downloadStore } from "@/business/components/DowloadProvider/store";
import useWebSocket from "@/business/hooks/useWebsocket";
import { SupportedPlatforms } from "@/constant/platform";
import { WsCmdReceive, WsCmdSend, type WsMessage } from "@/constant/protocol";
import { compare } from "@/utils";
import type { Platform } from "@workspace/business";
import { useLast } from "@workspace/ui";
import { useCallback, useEffect, useRef } from "react";
import { Outlet } from "react-router";
import { type Config, configStore } from "./store";

const ConfigProvider = () => {
  const socket = useWebSocket();

  const inited = useRef(false);

  const getSocket = useLast(socket);
  const getStore = useCallback(() => {
    return configStore.getState();
  }, []);

  useEffect(() => {
    const socket = getSocket();
    if (!getStore()?.unsubscribe && socket) {
      const unsubscribe = socket?.on("message", (payload: WsMessage) => {
        switch (payload.cmd) {
          case WsCmdReceive.init: {
            const {
              version,
              isAutoStart: isAutoBootStart,
              isDyRobotExist,
              isKsRobotExist,
            } = payload.data.clientConfig;
            const others = {
              version,
              isAutoBootStart,
              isDyRobotExist,
              isKsRobotExist,
            };
            const clientsInfo = payload.data.platformSoftwareInfo.reduce(
              (acc, cur) => {
                return Object.assign(acc, {
                  [cur.platformType]: {
                    ...cur,
                    _id: `${cur.is64Bit ? "x64" : "x86"}_${cur.version}`,
                  },
                });
              },
              {} as Config["clientsInfo"],
            );
            if (!compare(others, getStore().others)) {
              getStore().setConfig({
                others,
              });
            }
            if (!compare(clientsInfo, getStore().clientsInfo)) {
              // 用户本地手动删除平台客户端时，需要同步更新本地平台客户端列表
              const localPlatformClientsInfo = Object.entries(
                SupportedPlatforms,
              ).reduce(
                (acc, [key]) => {
                  const platform = Number(key) as Platform;
                  let list =
                    configStore.getState().localPlatformClientsInfo[platform] ||
                    [];
                  const target = list?.find(
                    (item) =>
                      item._id === clientsInfo[platform]._id &&
                      !clientsInfo[platform].isExist,
                  );
                  if (target) {
                    list = list.filter((item) => item._id !== target._id);
                    downloadStore.getState().removeFile(target._id);
                  }
                  return Object.assign(acc, {
                    [key]: list,
                  });
                },
                {} as Config["localPlatformClientsInfo"],
              );
              configStore.setState({
                clientsInfo,
                localPlatformClientsInfo,
              });
            }
            if (!compare(others, getStore().pluginInfo)) {
              getStore().setConfig({
                pluginInfo: payload.data.pluginInfo,
              });
            }

            break;
          }
          case WsCmdReceive.ClientOthersConfig: {
            const {
              version,
              isAutoStart: isAutoBootStart,
              isDyRobotExist,
              isKsRobotExist,
            } = payload.data;
            const data = {
              version,
              isAutoBootStart,
              isDyRobotExist,
              isKsRobotExist,
            };
            if (compare(data, getStore().others)) return;

            configStore.setState({
              others: data,
            });

            break;
          }
          /** 平台客户端安装目录 */
          case WsCmdReceive.InstallDirectory: {
            configStore.setState({
              installDirectory: payload.data.path,
            });
            break;
          }
          /** 本地平台客户端 */
          case WsCmdReceive.LocalPlatformClientsInfo: {
            const localPlatformClientsInfo = payload.data.platformInfo.reduce(
              (acc, cur) => {
                return Object.assign(acc, {
                  [cur.platformType]: cur.platformList.map((item) => ({
                    ...item,
                    _id: `${item.is64Bit ? "x64" : "x86"}_${item.platformVersion}`,
                  })),
                });
              },
              {} as Config["localPlatformClientsInfo"],
            );

            configStore.setState({
              localPlatformClientsInfo,
            });
            break;
          }

          // 基础设置
          case WsCmdReceive.BaseConfig: {
            const data = {
              offlineNotify: payload.data.offlineNotifySwitch,
              updateNotify: payload.data.updateNotifySwitch,
              autoLogin: payload.data.autoLoginSwitch,
              reminderShortcut: payload.data.reminderShortcut,
              singleMsgShortcut: payload.data.singleMsgShortcut,
              cardMsgShortcut: payload.data.cardMsgShortcut,
            };
            if (compare(data, getStore().base)) return;

            configStore.setState({
              base: data,
            });
            break;
          }

          // 智能体设置
          case WsCmdReceive.AgentConfig: {
            const data = {
              oldSidebarSwitch: payload.data.oldSidebarSwitch,
            };
            if (compare(data, getStore().agentConfig)) return;
            configStore.setState({
              agentConfig: data,
            });
            break;
          }
          // 智能体设置
          case WsCmdReceive.AppConfig: {
            const data = {
              fileSavePath: payload.data.fileSavePath,
              pluginInfo: payload.data.pluginInfo,
            };
            if (compare(data, getStore().appConfig)) return;
            configStore.setState({
              appConfig: data,
            });
            break;
          }

          // 客户端设置
          case WsCmdReceive.ClientConfig: {
            const platformClient = payload.data.selectedPlatformVersion.reduce(
              (acc, cur) => {
                return Object.assign(acc, {
                  [cur.platformType]: {
                    ...cur,
                    _id: cur?.platformVersion
                      ? `${cur?.is64Bit ? "x64" : "x86"}_${cur?.platformVersion}`
                      : null,
                  },
                });
              },
              {} as Config["client"]["platformClient"],
            );

            configStore.setState({
              client: {
                fielPath: payload.data.fileSavePath,
                platformClient,
              },
            });
            break;
          }

          // 智能体快捷键配置
          case WsCmdReceive.AgentShortcutConfigSetCallback:
          case WsCmdReceive.AgentShortcutConfig: {
            const data = payload.data.shortcutList.reduce(
              (pre, cur) => {
                return Object.assign(pre, {
                  [cur.number]: cur.shortcut,
                });
              },
              {} as Record<number, string>,
            );
            if (compare(data, getStore().agent)) return;

            configStore.setState({
              agent: data,
            });
            break;
          }

          // 聚合接待设置
          case WsCmdReceive.ReceptionConfig: {
            const data = {
              enable: payload.data.aggregatedListSwitch,
              isAlone: payload.data.aggregatedListAloneStatus,
              shortcutForOpenconversation:
                payload.data.aggregatedListOpenConversationShortcut,
              shortcutForChangeMode: payload.data.aggregatedListAloneShortcut,
            };
            if (compare(data, getStore().reception)) return;

            configStore.setState({
              reception: data,
            });
            break;
          }

          default:
            break;
        }
      });

      getStore().setSubscribe(unsubscribe);
    }

    return getStore().unsubscribe;
  }, [getStore, getSocket]);

  useEffect(() => {
    if (socket?.readyState === 1 && !inited.current) {
      inited.current = true;
      socket?.sendMessage({
        cmd: WsCmdSend.GetBaseConfig,
        data: undefined,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.GetClientConfig,
        data: undefined,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.GetReceptionConfig,
        data: undefined,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.GetLocalPlatformClients,
        data: undefined,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.GetPlatformClientsInstallDirectory,
        data: undefined,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.GetAgentConfig,
        data: undefined,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.GetAppConfig,
        data: undefined,
      });
    }
  }, [socket, socket?.readyState]);

  return <Outlet context={socket} />;
};

export default ConfigProvider;
