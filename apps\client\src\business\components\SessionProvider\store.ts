import type { ProductChangeMessage } from "@/constant/protocol";
import type { Platform } from "@workspace/business";
import { createStore } from "zustand";

export interface SessionState {
  unsubscribe: (() => void) | undefined;
  setSubscribe: (unsubscribe: (() => void) | undefined) => void;
  productInfo: ProductChangeMessage["data"];
  platformType: Platform;
  thirdShopId: string | undefined;
  groupId: string | undefined;
  buyer:
    | {
        showCurrentBuyerNick: string;
        buyerNick: string;
        display: string;
      }
    | undefined;
  serviceAccount:
    | {
        account: string;
        ifLogin: boolean;
        receptionIsOpen: boolean;
      }
    | undefined;
  token: string | undefined;
  setState: (state: Partial<SessionState>) => void;
}

export const sessionStore = createStore<SessionState>((set) => ({
  productInfo: {} as ProductChangeMessage["data"],
  buyer: undefined,
  platformType: 0,
  serviceAccount: undefined,
  thirdShopId: undefined,
  groupId: undefined,
  token: undefined,
  setState: (state) => {
    set(state);
  },
  unsubscribe: undefined,
  setSubscribe: (unsubscribe: (() => void) | undefined) =>
    set({
      unsubscribe: () => {
        unsubscribe?.();
        set({ unsubscribe: undefined });
      },
    }),
}));
