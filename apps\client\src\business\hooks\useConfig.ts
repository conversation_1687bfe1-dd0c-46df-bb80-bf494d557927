import {
  type ConfigState,
  configStore,
} from "@/business/components/ConfigProvider/store";
import useWebSocket from "@/business/hooks/useWebsocket";
import { useStore } from "zustand";

const defaultSelector = (state: ConfigState) => state;

const useConfig = <T = ConfigState>(
  selector: (state: ConfigState) => T = defaultSelector as (
    state: ConfigState,
  ) => T,
) => {
  const socket = useWebSocket();
  const store = useStore(configStore, selector);
  return { socket, store };
};
export default useConfig;
