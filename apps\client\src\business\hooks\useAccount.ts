import { AccountTypeMap } from "@/constant/protocol";
import { type AccountInfoType, getAccountInfo } from "@/network/api/account";
import { type StateCreator, create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
export interface Account {
  data: AccountInfoType | undefined;
  accountType: (typeof AccountTypeMap)[keyof typeof AccountTypeMap];
  updateAccountType: (
    type: (typeof AccountTypeMap)[keyof typeof AccountTypeMap],
  ) => void;
  token: string | undefined | null;
  update: (user: AccountInfoType | undefined) => void;
  setToken: (token: string) => void;
  getToken: () => string | null;
  asyncUpdate: () => Promise<AccountInfoType | undefined>;
}

export const createUserSlice: StateCreator<Account> = (set, get) => ({
  data: undefined,
  accountType: AccountTypeMap.Password,
  updateAccountType: (type) => {
    set({
      accountType: type,
    });
  },
  token: localStorage.getItem("tanyu-agent-account"),
  setToken: (token) => {
    localStorage.setItem("tanyu-agent-account", token);
    set({
      token,
    });
  },
  getToken: () => {
    let token = get().token;
    if (token) {
      return token;
    }
    token = localStorage.getItem("tanyu-agent-account");
    set({
      token,
    });
    return token;
  },
  update: (data) =>
    set({
      data,
    }),
  asyncUpdate: async () => {
    const res = await getAccountInfo().promise.catch((error: Error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      set({
        data: res.data.data,
      });
      return res.data.data;
    }
    return;
  },
});

export const useAccount = create<Account>()(
  persist(
    (...args) => ({
      ...createUserSlice(...args),
    }),
    {
      name: "account",
      storage: createJSONStorage(() => localStorage),
    },
  ),
);
