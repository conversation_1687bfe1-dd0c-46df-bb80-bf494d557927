import { getFullPath } from "@/utils/route";
import { Button, Tabs } from "antd";
import { useNavigate, useSearchParams } from "react-router";
import ArrowLeftOutlined from "~icons/ant-design/arrow-left-outlined";
import AgentConfig from "./components/AgentConfig";
import ApplicationConfig from "./components/ApplicationConfig";
import BaseConfig from "./components/BaseConfig";
import ClientConfig from "./components/ClientConfig";
import ReceptionConfig from "./components/ReceptionConfig";

const items = [
  {
    key: "base",
    label: "基础设置",
    children: <BaseConfig />,
  },
  {
    key: "reception",
    label: "聚合接待设置",
    children: <ReceptionConfig />,
  },
  {
    key: "client",
    label: "客户端设置",
    children: <ClientConfig />,
  },
  {
    key: "agent",
    label: "智能体设置",
    children: <AgentConfig />,
  },
  {
    key: "app",
    label: "应用设置",
    children: <ApplicationConfig />,
  },
];

export const Component: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  return (
    <div className="flex-1 flex flex-col h-screen">
      <div className="bg-white pb-2 px-2 shadow-3 border-b">
        <div className="flex items-center gap-2">
          <Button
            type="text"
            onClick={() => {
              navigate(
                getFullPath("/console/hosting", {
                  query: Object.fromEntries(searchParams),
                }),
              );
            }}
          >
            <ArrowLeftOutlined />
          </Button>
          <h1>系统设置</h1>
        </div>
      </div>
      <Tabs
        className="bg-white  overflow-hidden flex-1 p-3 [&_.ant-tabs-content]:h-full [&_.ant-tabs-tabpane]:h-full"
        tabPosition="left"
        items={items}
      />
    </div>
  );
};

Component.displayName = "SystemConfig";
