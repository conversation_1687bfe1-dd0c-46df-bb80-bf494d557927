import type { IProductItem } from "@/network/api/product";
import { Button } from "antd";
import clsx from "clsx";
import { useMemo } from "react";
import LeftOutlined from "~icons/ant-design/left-outlined";
import Seassion from "../../Seassion";
import ProductDetail from "./ProductDetail";

interface SearchProductProps {
  buyerNick: string;
  data: IProductItem;
  onClear: () => void;
  className?: string;
}

const SearchProduct = ({
  buyerNick,
  data,
  onClear,
  className,
}: SearchProductProps) => {
  const currentProduct = data;
  const productList = useMemo(() => {
    return data ? [data] : [];
  }, [data]);

  return (
    <Seassion
      title="搜索结果"
      className={className}
      action={
        <Button type="link" className="p-0 h-auto" onClick={onClear}>
          <LeftOutlined />
          返回
        </Button>
      }
    >
      <div className="flex items-center overflow-x-auto overflow-y-hidden">
        {productList.map((product) => (
          <div
            key={product.productId}
            className={clsx(
              "py-[6px] px-1 border-solid",
              currentProduct?.productId === product.productId
                ? "border border-b-transparent border-gray-4 bg-gray-2 rounded-tl-[2px] rounded-tr-[2px]"
                : "border-none",
            )}
          >
            <img
              src={product.pic}
              alt=""
              className="w-[28px] h-[28px] rounded-[2px] object-cover block"
            />
          </div>
        ))}
      </div>
      <ProductDetail data={currentProduct} buyerNick={buyerNick} />
    </Seassion>
  );
};

export default SearchProduct;
