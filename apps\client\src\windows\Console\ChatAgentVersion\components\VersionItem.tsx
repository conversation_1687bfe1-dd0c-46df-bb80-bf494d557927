import { ChatAgentChannel, type IChatAgentItem } from "@/network/api/agent";
import { Radio, Tag } from "antd";

const VersionItem = (props: { record: IChatAgentItem }) => {
  const { record } = props;
  return (
    <Radio
      value={record.id}
      className="border border-gray-3 px-4 py-2 w-full rounded-4"
    >
      <div className="pb-1 w-full">
        <span className="font-bold">{record.versionName} </span>
        <Tag
          color={record.channel === ChatAgentChannel.STABLE ? "blue" : "orange"}
        >
          {record.channel === ChatAgentChannel.STABLE ? "稳定版" : "开发版"}
        </Tag>
      </div>
      <div title={record.versionDesc} className="truncate max-w-[690px]">
        {record.versionDesc}
      </div>
    </Radio>
  );
};
export default VersionItem;
