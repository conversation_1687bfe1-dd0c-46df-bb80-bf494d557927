import type { AppEnum } from "@/constant/client";
import client from "@/network/client";
import type { APIResponse } from "@workspace/types";

export interface SendMessageData {
  content?: string;
  imageUrls?: string[];
  buyerAccount: string;
  videoUrls?: string[];
  feature?: {
    // 智能体相关
    agent?: string;
    sidebar?: "1" | "2"; //1 准确回复 2 促进转化
    editSend?: "1"; // 1 视为编辑后发送
    agentTopicId?: string;
    logIsCredible?: any;
    agentTopic?: string;
  };
  payload?: any;
  cardId?: string;
}

//新版发送消息
export function sendMessages(data: SendMessageData) {
  return client<APIResponse<boolean>>(
    "/api/im/agent-buyer/send-message-to-im",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
}
export const MsgContentMap = {
  text: 0,
  image: 1,
  video: 2,
} as const;

export type BatchSendMessageData = {
  buyerAccount: string;
  cardId?: string;
  feature?: {
    // 智能体相关
    agent?: string;
    sidebar?: "1" | "2"; //1 准确回复 2 促进转化
    editSend?: "1"; // 1 视为编辑后发送
    agentTopicId?: string;
    logIsCredible?: any;
    agentTopic?: string;
  };
  /** 0-文本 1-图片 2-视频 */
  msgContents: {
    type: (typeof MsgContentMap)[keyof typeof MsgContentMap];
    value: string;
  }[];
  payload?: any;
};
//新版批量发送消息
export function batchSendMessages(data: BatchSendMessageData) {
  return client<APIResponse<boolean>>(
    "/api/im/agent-buyer/batch-send-message-to-im",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
}

export interface LabelItem {
  groupId: number;
  groupName: string;
  labelInfos: Array<{
    groupId: number;
    labelId: string;
    labelName: string;
    labelType: number;
  }>;
}

/** 获取分组标签 */
export function getGroupLabelsList() {
  return client<APIResponse<LabelItem[]>>(
    "/api/im/agent-buyer/bind-group-label",
    {
      shouldLog: true,
    },
  );
}

export interface IAppItem {
  app: AppEnum;
  versionNo: string;
  remark: string;
  objectKey: string;
  objectUrl: string;
  objectMd5: string;
  rarObjectKey: string;
  rarObjectUrl: string;
  rarObjectMd5: string;
  rarObjectBit: string;
}

export const getAppLast = (params: {
  app: AppEnum;
  clientVersionNo: string;
}) => {
  return client<APIResponse<IAppItem>>(
    "/api/im/client/agent/app-version/get-latest-version",
    {
      params,
      shouldLog: true,
    },
  );
};

export interface IAuthItem {
  expireAt: string;
  thirdAppKey: string;
  thirdAppName: string;
}

//获取店铺授权
export function getShopAuth() {
  return client<
    APIResponse<{
      auths: IAuthItem[];
    }>
  >("/api/im/agent/shop/get-shop-auth", {
    method: "GET",
    shouldLog: true,
  });
}
