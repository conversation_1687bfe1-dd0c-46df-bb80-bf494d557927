import useAgent from "@/business/hooks/useAgent";
import type { BatchSendMessageData } from "@/network/api/base";
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui";
import clsx from "clsx";
import { useRef } from "react";
import SendOutlined from "~icons/icon-park-outline/send";
import Editor from "../Editor";

const TextContent = ({
  text,
  onSend,
  onContentChange,
  ifSend,
  shortcutIndex,
}: {
  text: string;
  onSend: (value: BatchSendMessageData["msgContents"]) => void;
  onContentChange?: (value: string) => void;
  ifSend: boolean;
  shortcutIndex?: number;
}) => {
  const { store: singleSendShortcut } = useAgent(
    (state) => state.shortcut.singleSendShortcut,
  );
  const localText = useRef(text);
  const onChange = (value: string) => {
    localText.current = value;
    onContentChange?.(value);
  };
  return (
    <div className="flex rounded-lg px-2 border border-gray-3 hover:bg-gray-50 transition-colors relative group">
      {shortcutIndex && shortcutIndex <= 9 ? (
        <span className=" flex items-center justify-center h-[28px] self-start ">
          <span className="text-12  font-semibold  text-[--card-theme] rounded-sm mr-1   ">
            {shortcutIndex}.
          </span>
        </span>
      ) : null}
      <div
        className={clsx(
          "text-12 my-0 flex-1",
          ifSend ? "text-black-4" : "text-black-5",
        )}
      >
        <Editor content={text} onChange={onChange} />
      </div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger
            className="absolute top-1 right-1 shadow-lg bg-white self-start ml-auto px-1 h-[24px] flex items-center justify-center rounded-sm  cursor-pointer bg-transparent outline-none border-none  text-blue-7 group-hover:opacity-100 opacity-0 hover:text-blue-5"
            onClick={() => onSend([{ type: 0, value: localText.current }])}
          >
            <SendOutlined className="text-12" />
          </TooltipTrigger>
          <TooltipContent className="bg-black-4">
            <span>
              {shortcutIndex && singleSendShortcut
                ? `发送（${singleSendShortcut} + ${shortcutIndex}）`
                : "发送"}
            </span>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default TextContent;
