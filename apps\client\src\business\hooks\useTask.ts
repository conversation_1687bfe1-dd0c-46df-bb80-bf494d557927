import {
  DownloadStatusMap,
  WsCmdReceive,
  WsCmdSend,
  type WsMessage,
} from "@/constant/protocol/index";
import { useLast } from "@workspace/ui";
import { useEffect } from "react";
import { createStore, useStore } from "zustand";
import useWebSocket from "./useWebsocket";

interface TaskState {
  tasks: Set<string>;
  addTasks: (ids: string[]) => void;
  deletTask: (id: string) => void;
  deletTasks: (ids: string[]) => void;
  unsubscribe: (() => void) | undefined;
  setSubscribe: (unsubscribe: (() => void) | undefined) => void;
}

export const configStore = createStore<TaskState>((set) => ({
  tasks: new Set(),
  addTasks: (ids: string[]) => {
    set((state) => {
      const tasks = new Set([...state.tasks, ...ids]);
      return {
        ...state,
        tasks,
      };
    });
  },
  deletTask: (id: string) => {
    set((state) => {
      state.tasks.delete(id);
      const tasks = new Set(state.tasks);
      return {
        ...state,
        tasks,
      };
    });
  },
  deletTasks: (ids: string[]) => {
    set((state) => {
      const tasks = new Set(
        Array.from(state.tasks).filter((item) => !ids.includes(item)),
      );
      return {
        ...state,
        tasks,
      };
    });
  },
  unsubscribe: undefined,
  setSubscribe: (unsubscribe: (() => void) | undefined) =>
    set({
      unsubscribe: () => {
        unsubscribe?.();
        set({ unsubscribe: undefined });
      },
    }),
}));

const useTask = () => {
  const socket = useWebSocket();
  const store = useStore(configStore);
  const getStore = useLast(store);
  const getSocket = useLast(socket);
  useEffect(() => {
    const socket = getSocket();
    if (!getStore().unsubscribe && socket) {
      const unsubscribe = socket?.on("message", (payload: WsMessage) => {
        switch (payload.cmd) {
          /** 删除本地平台客户端回调 */
          case WsCmdReceive.DeleteLocalFilesACK: {
            getStore().deletTask(payload.data.path);
            getSocket()?.sendMessage({
              cmd: WsCmdSend.GetLocalPlatformClients,
              data: undefined,
            });
            break;
          }
          /** 下载任务回调 */
          case WsCmdReceive.DownloadProgress: {
            const { data } = payload;
            const { fileList } = data;
            const addIds: string[] = [];
            const removeIds: string[] = [];
            fileList.forEach((item) => {
              if (
                (
                  [
                    DownloadStatusMap.InstallFailed,
                    DownloadStatusMap.InstallSuccess,
                    DownloadStatusMap.Cancelled,
                    DownloadStatusMap.Completed,
                  ] as number[]
                ).includes(item.status)
              ) {
                removeIds.push(item.id);
              } else if (!getStore().tasks.has(item.id)) {
                addIds.push(item.id);
              }
            });
            removeIds.length && getStore().deletTasks(removeIds);
            addIds.length && getStore().addTasks(addIds);
            break;
          }
          default:
            break;
        }
      });
      getStore().setSubscribe(unsubscribe);
    }

    return getStore().unsubscribe;
  }, [getStore, getSocket]);
  return store;
};

export default useTask;
