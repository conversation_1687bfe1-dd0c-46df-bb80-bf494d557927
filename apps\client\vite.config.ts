import path from "node:path";
import react from "@vitejs/plugin-react";
import Icons from "unplugin-icons/vite";
import { defineConfig } from "vite";
import type vitePluginAliOss from "vite-plugin-ali-oss";
import svgr from "vite-plugin-svgr";
import template from "./svgr-template.ts";

const isVitePluginAliOssOptions = (
  options: Record<
    "region" | "accessKeyId" | "accessKeySecret" | "bucket",
    unknown
  >,
): options is Parameters<typeof vitePluginAliOss>[0] => {
  return !!(
    options?.region &&
    options?.accessKeyId &&
    options?.accessKeySecret &&
    options?.bucket
  );
};

// https://vite.dev/config/
export default defineConfig(() => {
  const options = {
    region: process.env.OSS_REGION,
    accessKeyId: process.env.OSS_ACCESS_KEY_ID,
    accessKeySecret: process.env.OSS_ACCESS_SECRET,
    bucket: process.env.OSS_BUCKET,
  };

  const isbuild = !!isVitePluginAliOssOptions(options);

  const base = isbuild
    ? `${process.env.OSS_BASE_URL}/${process.env.OSS_BASE_DIR_CLIENT}`
    : "/";
  return {
    base,
    plugins: [
      react(),
      svgr({
        svgrOptions: {
          template: template,
        },
      }),
      // isbuild && vitePluginAliOss(options),
      Icons({
        compiler: "jsx",
        jsx: "react",
        // experimental
        autoInstall: true,
      }),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@workspace/ui": path.resolve(__dirname, "../../packages/ui/src"),
        "@workspace/types": path.resolve(__dirname, "../../packages/types/src"),
        "@workspace/business": path.resolve(
          __dirname,
          "../../packages/business/src",
        ),
      },
    },
    optimizeDeps: {
      include: [
        "@radix-ui/react-slot",
        "@radix-ui/react-switch",
        "@radix-ui/react-scroll-area",
        "@radix-ui/react-dropdown-menu",
        "clsx",
        "tailwind-merge",
        "class-variance-authority",
        "tailwindcss-animate",
      ],
    },
    define: {
      __WEB_VERSION__: JSON.stringify(process.env.npm_package_version),
    },
    server: {
      host: "0.0.0.0",
      port: 6173,
      strictPort: true,
      proxy: {
        "/api": {
          target: "https://agent-client.qa.feizhi-ai.com",
          changeOrigin: true,
          cookieDomainRewrite: {
            "agent-client.qa.feizhi-ai.com": "localhost",
          },
          configure: (proxy) => {
            proxy.on("error", (err) => {
              console.log("proxy error", err);
            });
            proxy.on("proxyReq", (proxyReq, req) => {
              console.log(
                "Sending Request:",
                req.method,
                req.url,
                " => TO THE TARGET =>  ",
                proxyReq.method,
                proxyReq.protocol,
                proxyReq.host,
                proxyReq.path,
                JSON.stringify(proxyReq.getHeaders()),
              );
            });
            proxy.on("proxyRes", (proxyRes, req) => {
              console.log(
                "Received Response from the Target:",
                proxyRes.statusCode,
                req.url,
                JSON.stringify(proxyRes.headers),
              );
            });
          },
        },
      },
    },
  };
});
