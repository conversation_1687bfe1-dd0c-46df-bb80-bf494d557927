import { useAccount } from "@/business/hooks/useAccount";
import useVerifyCode from "@/business/hooks/useVerifyCode";
import useWebSocket from "@/business/hooks/useWebsocket";
import { AccountTypeMap, WsCmdSend } from "@/constant/protocol/index";
import { postPhoneLogin } from "@/network/api/account";
import { Button, Checkbox, Form, Input } from "antd";
import MailOutlined from "~icons/ant-design/mail-outlined";

const PhoneLogin = () => {
  const [form] = Form.useForm();
  const mobile = Form.useWatch("mobile", form);
  const { token, sendVerifyCode, getButtonProps } = useVerifyCode(60);
  const sockect = useWebSocket();
  const account = useAccount();

  const onSubmit = async (values: Parameters<typeof postPhoneLogin>[0]) => {
    const res = await postPhoneLogin({
      ...values,
      token: token as string,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      account.setToken(res.data.data);
      account.updateAccountType(AccountTypeMap.Password);
      localStorage.removeItem("accountMap");
      account.asyncUpdate().then((info) => {
        if (info) {
          sockect?.sendMessage({
            cmd: WsCmdSend.AccountLogin,
            data: {
              userName: info.account.account,
              cookie: res.data.data as string,
              showUserName: info.account.account,
              accountType: AccountTypeMap.Password,
            },
          });
        }
      });
    }
  };

  const handleSendVerifyCode = () => {
    sendVerifyCode({ mobile });
  };

  const toView = (url: string) => {
    sockect?.sendMessage({
      cmd: "open_url",
      data: {
        url: url,
      },
    });
  };

  return (
    <Form
      form={form}
      onFinish={onSubmit}
      initialValues={{ policy: false }}
      className="flex flex-col"
    >
      <Form.Item
        name="mobile"
        // rules={[
        //   {
        //     required: true,
        //     message: "请输入你的手机号",
        //     pattern: /^\d{11}$/g,
        //   },
        // ]}
      >
        <Input
          prefix={<span className="mr-2.5 text-black-1">+86 |</span>}
          placeholder="请输入你的手机号"
          className="py-2.5"
        />
      </Form.Item>
      <div className="flex space-x-4">
        <Form.Item
          className="flex-1"
          name="verifyCode"
          // rules={[
          //   { required: true, message: "请输入验证码", pattern: /^\d{6}$/g },
          // ]}
        >
          <Input
            placeholder="请输入短信验证码"
            prefix={<MailOutlined className="mr-2.5 text-black-1" />}
            className="py-2.5"
          />
        </Form.Item>
        <Button
          onClick={handleSendVerifyCode}
          {...getButtonProps({ disabled: !/^\d{11}$/g.test(mobile) })}
          className="h-[42px]"
        />
      </div>

      <button
        type="button"
        className="ml-auto cursor-pointer bg-transparent text-14 text-black-2 hover:text-blue-7"
        onClick={() => toView(import.meta.env.VITE_CHANGE_PASSWORD_URL)}
      >
        忘记密码
      </button>
      <Button
        type="primary"
        className="mb-3 mt-4 block w-full py-2 h-auto"
        htmlType="submit"
      >
        立即登录
      </Button>
      <Form.Item>
        <div className="mt-auto space-x-3 text-center">
          <Form.Item
            noStyle
            name="policy"
            rules={[
              {
                validator: (_, value) => {
                  if (!value) {
                    return Promise.reject(new Error("请同意服务协议"));
                  }
                  return Promise.resolve(value);
                },
              },
            ]}
            valuePropName="checked"
          >
            <Checkbox />
          </Form.Item>
          <span className="text-14 text-black-5">
            我已阅读并同意{" "}
            <Button
              type="link"
              className="h-auto p-0"
              onClick={() => toView(import.meta.env.VITE_PRIVACY_POLICY)}
            >
              《隐私政策》
            </Button>
            与
            <Button
              type="link"
              className="h-auto p-0"
              onClick={() => toView(import.meta.env.VITE_NETWORK_SERVICE)}
            >
              《探域网络服务条款》
            </Button>
          </span>
        </div>
      </Form.Item>
    </Form>
  );
};

export default PhoneLogin;
