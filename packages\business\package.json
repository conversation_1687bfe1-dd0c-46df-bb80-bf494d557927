{"name": "@workspace/business", "version": "1.0.0", "description": "", "main": "index.ts", "types": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@repo/typescript-config": "workspace:*", "react": "^18.3.1"}, "devDependencies": {"@types/react": "^18.3.1", "vite-plugin-svgr": "^4.3.0", "react": "^18.3.1"}}