import useFocusProduct from "@/business/hooks/useFocusProduct";
import useSession from "@/business/hooks/useSession";
import useRemote from "@/hooks/useRemote";
import {
  type IFocusProductInfo,
  type ProductDetailType,
  type SearchProductDataResultItem,
  getFocusProductInfo,
  updateFocusProduct,
} from "@/network/api/product";
import { Button, usePartialState } from "@workspace/ui";
import { Typography } from "antd";
import MallBagSolid from "~icons/icon-park-solid/mall-bag";
import ProductBottomSheet from "./ProductBottomSheet";
import SKUBottomSheet from "./SKUBottomSheet";

const initState: {
  sku: boolean;
  search: boolean;
  product: IFocusProductInfo | undefined;
} = {
  sku: false,
  search: false,
  product: undefined,
};

const FocusProduct = () => {
  const {
    store: { product, update },
  } = useFocusProduct();

  const [state, setState] = usePartialState(initState);
  const { buyer, serviceAccount } = useSession();
  const buyerNick = buyer?.buyerNick ?? "";
  const sellerAccount = serviceAccount?.account ?? "";

  useRemote(
    buyerNick && sellerAccount
      ? [getFocusProductInfo.name, buyerNick, sellerAccount]
      : null,
    ([, buyerNick, sellerAccount]) => {
      const params = {
        buyerAccount: buyerNick,
        sellerAccount: sellerAccount,
      };
      return getFocusProductInfo(params);
    },
    {
      onSuccess(data) {
        update(data);
      },
    },
  );

  const resetState = () => {
    setState(initState);
  };

  const handleUpdateSKU = async (sku: ProductDetailType | undefined) => {
    if (!sku) return;
    const postData = {
      buyerAccount: buyerNick,
      sellerAccount: sellerAccount,
      skuId: sku.skuId,
      spuId: sku.goodsId,
    };
    const res = await updateFocusProduct(postData).promise.catch(
      (error: Error) => error,
    );
    if (res instanceof Error) return;
    if (res.data.success) {
      update({
        spuId: sku.goodsId,
        imageUrl: sku.image,
        productName: sku.title,
        detailUrl: sku.detailUrl,
        skuId: sku.skuId,
        skuAttr: sku.attrs.map((item) => item.value).join("/"),
      });
      resetState();
    }
  };

  const handleUpdateSPU = async (
    spu: SearchProductDataResultItem | undefined,
  ) => {
    if (!spu) return;
    const postData = {
      buyerAccount: buyerNick,
      sellerAccount: sellerAccount,
      spuId: spu.productId,
    };
    const res = await updateFocusProduct(postData).promise.catch(
      (error: Error) => error,
    );
    if (res instanceof Error) return;
    if (res.data.success) {
      update({
        spuId: spu.productId,
        imageUrl: spu.pic,
        productName: spu.name,
        detailUrl: spu.url,
        skuId: undefined,
        skuAttr: undefined,
      });
      resetState();
    }
  };

  // 兼容商品未同步的边缘情况
  const _product =
    product?.productName === "商品未同步"
      ? {
          ...product,
          productName: `未能找到SkuId为${product.skuId}的商品信息`,
          _unknown: true,
          desc: "请联系客服人员帮您处理",
        }
      : {
          ...product,
          _unknown: false,
          desc: "",
        };

  return (
    <>
      <div>
        <div className="flex gap-2 max-h-[66px]">
          {_product?.imageUrl ? (
            <img
              src={_product?.imageUrl}
              className="w-[66px] h-[66px] flex-shrink-0 object-cover rounded-4"
              alt=""
            />
          ) : (
            <div className="bg-[#F7F7F7] rounded-4 flex items-center justify-center w-[66px] h-[66px]">
              <MallBagSolid className="text-[#CACCD2] text-[25px]" />
            </div>
          )}
          <div className="flex-1 flex flex-col min-w-0">
            {product ? (
              <>
                <Typography.Text
                  className="text-black-4 text-12 font-medium w-full !mb-0"
                  ellipsis={{ tooltip: true }}
                >
                  {_product?.productName}
                </Typography.Text>
                <Typography.Text
                  className="text-black-2 text-12 [&_.ant-typography-copy]:text-black-2"
                  copyable={{ text: _product.skuId || _product.spuId }}
                >
                  ID: {_product.skuId || _product.spuId}
                </Typography.Text>
              </>
            ) : (
              <div className="text-black-2 text-12">
                暂无焦点商品，可手动选择或由买家发送链接更新
              </div>
            )}
            <div className="text-12 text-black-3 flex items-center mt-auto">
              <Typography.Text
                className="text-12 text-black-3 w-0 flex-grow"
                ellipsis={{ tooltip: true }}
              >
                {_product._unknown ? _product.desc : _product?.skuAttr}
              </Typography.Text>
              {!_product._unknown ? (
                <Button
                  variant="link"
                  className="p-0 hover:no-underline mx-auto !h-auto"
                  onClick={() =>
                    setState({
                      sku: true,
                      product,
                    })
                  }
                >
                  选sku
                </Button>
              ) : null}
              <Button
                variant="link"
                className="p-0 hover:no-underline ml-2 !h-auto"
                onClick={() => setState({ search: true })}
              >
                换商品
              </Button>
            </div>
          </div>
        </div>
      </div>
      <ProductBottomSheet
        open={state.search}
        onClose={resetState}
        onOpenSKU={(product) =>
          setState({
            sku: true,
            product: {
              spuId: product.productId,
              imageUrl: product.pic,
              productName: product.name,
              detailUrl: product.url,
              skuId: undefined,
              skuAttr: undefined,
            },
          })
        }
        onSetSPU={handleUpdateSPU}
        zIndex={1000}
      />
      <SKUBottomSheet
        open={state.sku}
        onClose={() => {
          setState({ sku: false });
        }}
        onOk={handleUpdateSKU}
        data={state.product}
        zIndex={1001}
      />
    </>
  );
};

export default FocusProduct;
