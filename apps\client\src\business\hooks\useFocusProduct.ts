import { sessionStore } from "@/business/components/SessionProvider/store";
import {
  type ProductChangeMessage,
  WsCmdReceive,
  type WsMessage,
} from "@/constant/protocol/index";
import {
  type IFocusProductInfo,
  getFocusProductInfo,
} from "@/network/api/product";
import logger from "@/utils/log";
import { useEffect } from "react";
import { useStore } from "zustand";
import { createStore } from "zustand";
import useWebSocket from "./useWebsocket";

interface FocusProductState {
  lastMsg: ProductChangeMessage["data"] | undefined;
  product: IFocusProductInfo | undefined;
  setLastMsg: (data: ProductChangeMessage["data"] | undefined) => void;
  update: (data: IFocusProductInfo | undefined) => void;
  asyncUpdate: (payload: ProductChangeMessage["data"]) => Promise<void>;
}

export const focusProductState = createStore<FocusProductState>((set) => ({
  lastMsg: undefined,
  product: undefined,
  setLastMsg: (data: ProductChangeMessage["data"] | undefined) => {
    set((state) => ({ ...state, lastMsg: data }));
  },
  update: (data) => {
    set({ product: data });
  },

  asyncUpdate: async (payload: ProductChangeMessage["data"]) => {
    const res = await getFocusProductInfo({
      buyerAccount: payload.buyerAccount,
      sellerAccount: payload.account,
    }).promise.catch((error: Error) => error);
    if (res instanceof Error || !res.data.success) {
      return;
    }
    if (res.data.success) {
      set({
        product: res.data.data,
      });
    }
  },
}));

const useFocusProduct = () => {
  const socket = useWebSocket();
  const store = useStore(focusProductState);

  useEffect(() => {
    const unsubscribe = socket?.on("message", (data: WsMessage) => {
      if (data.cmd === WsCmdReceive.ProductChange) {
        const { buyer, serviceAccount } = sessionStore.getState();
        if (
          buyer?.buyerNick === data.data.buyerAccount &&
          serviceAccount?.account === data.data.account
        ) {
          store.setLastMsg(data.data as ProductChangeMessage["data"]);
          store.asyncUpdate(data.data as ProductChangeMessage["data"]);
        } else {
          logger.warn("买家/客服信息不匹配，不做更新处理", {
            buyer,
            serviceAccount,
            message: data.data,
          });
        }
      }
    });
    return unsubscribe;
  }, [store, socket]);

  return { socket, store };
};

export default useFocusProduct;
