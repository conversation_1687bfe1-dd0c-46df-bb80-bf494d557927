import { serviceAccountStore } from "@/business/components/ServiceccountProvider/store";
import { sessionStore } from "@/business/components/SessionProvider/store";
import { useAccount } from "@/business/hooks/useAccount";
import { websocketStore } from "@/business/hooks/useWebsocket";
import Logger from "@/class/Logger";
import type { SupportPlatform } from "@/constant/platform";
import { WsCmdSend } from "@/constant/protocol";
import axios, { type AxiosRequestConfig, type AxiosResponse } from "axios";
import qs from "qs";
import { toast } from "sonner";

const logger = new Logger("api");

// 扩展AxiosRequestConfig的类型
declare module "axios" {
  interface AxiosRequestConfig {
    shouldLog?: boolean;
  }
}

// TODO: axios实例配置
const axiosInstance = axios.create({
  withCredentials: true,
  timeout: 20000,
  paramsSerializer(params) {
    const _params: Record<string, unknown> = {};
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        _params[key] = value;
      }
    });
    return qs.stringify(_params, {
      arrayFormat: "comma",
    });
  },
});

axiosInstance.interceptors.request.use((config) => {
  if (config.shouldLog) {
    logger.info({
      method: config.method?.toLocaleUpperCase(),
      headers: config.headers,
      url: config.url,
      data: config.data,
      params: config.params,
    });
  }
  const currentTanyuGroupAccount =
    serviceAccountStore.getState().tanyuGroupSessions[
      sessionStore.getState().groupId as string
    ];
  const { serviceAccount, platformType } = sessionStore.getState();
  const currentServiceAccount = serviceAccountStore
    .getState()
    .accountMap[platformType as SupportPlatform]?.find(
      (item) => item.sellerAccountForBackend === serviceAccount?.account,
    );

  config.headers.set("tanyu-agent-account", useAccount.getState().getToken());
  config.headers.set("tanyu-group-id", currentTanyuGroupAccount?.groupId);
  config.headers.set("tanyu-group-account", currentTanyuGroupAccount?.token);
  config.headers.set(
    "token",
    currentServiceAccount?.token || sessionStore.getState().token,
  );
  return config;
});

axiosInstance.interceptors.response.use(
  (res) => {
    if (res.config.shouldLog) {
      logger.info({
        method: res.config.method?.toLocaleUpperCase(),
        headers: res.config.headers,
        url: res.config.url,
        data: res.config.data,
        params: res.config.params,
        response: res.data,
      });
    }
    if (!res.data.success && !(res.data instanceof Blob)) {
      toast.error(res.data.msg || "系统开小差了，请联系客服人员");

      logger.warn({
        method: res.config.method?.toLocaleUpperCase(),
        headers: res.config.headers,
        url: res.config.url,
        data: res.config.data,
        params: res.config.params,
        response: res.data,
      });
    }

    /** token过期手动退出登录 */
    if (res.data.code === 401) {
      console.log("token过期手动退出登录");
      websocketStore.getState().socket?.sendMessage({
        cmd: WsCmdSend.Logout,
        data: {
          userName: "",
        },
      });
    }

    return res;
  },
  (error) => {
    logger.error({
      method: error.config?.method?.toLocaleUpperCase(),
      headers: error.config?.headers,
      url: error.config?.url,
      data: error.config?.data,
      params: error.config?.params,
      error: error,
    });
    if (error?.message?.includes("timeout") || error.code === "ECONNABORTED") {
      toast.error("请求超时,请重新尝试");
    }
    const { response } = error;

    if (
      response &&
      (Number(response.status) === 502 || Number(response.status) === 503)
    ) {
      toast.error("系统开小差了，请联系客服人员");
    }
    return error;
  },
);

const client = <T>(
  url: string,
  params?: (AxiosRequestConfig<unknown> & { shouldLog?: boolean }) | undefined,
): {
  promise: Promise<AxiosResponse<T>>;
  controller: AbortController;
} => {
  const controller = new AbortController();
  const promise = axiosInstance(url, { ...params, signal: controller.signal });
  return { promise, controller };
};

export default client;
