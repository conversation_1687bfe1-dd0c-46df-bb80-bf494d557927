import { Paths } from "@/config/path";
import { type BatchSendMessageData, MsgContentMap } from "@/network/api/base";
import type { CopilotMedia } from "@/network/api/copilot";
import { getFullPath } from "@/utils/route";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
  useDebounce,
} from "@workspace/ui";
import { Typography } from "antd";
import { useContext, useState } from "react";
import LinkOneOutline from "~icons/icon-park-outline/link-one";
import PlayOneSolid from "~icons/icon-park-solid/play-one";
import Image from "~icons/lucide/image";
import Video from "~icons/lucide/video";
import AgentContext from "../../context";

interface ActionMenuProps {
  url: string;
  title: string;
  type: CopilotMedia["copilotMediaType"];
  desc: string;
  agentTopicId: string;
  children: React.ReactNode;
  buyerAccount: string;
}

const ActionMenu = ({
  title,
  url,
  type,
  agentTopicId,
  children,
  desc,
  buyerAccount,
}: ActionMenuProps) => {
  const { sendMessages, preview } = useContext(AgentContext);
  const [open, setOpen] = useState(false);

  const handleSend = useDebounce(
    async () => {
      const payload: BatchSendMessageData = {
        feature: {
          // copilot相关
          agent: "1",
          agentTopicId,
        },
        buyerAccount,
        msgContents: [],
      };

      switch (type) {
        case "pic":
          payload.msgContents = [{ type: MsgContentMap.image, value: url }];
          break;
        case "video":
          payload.msgContents = [{ type: MsgContentMap.video, value: url }];
          break;
        case "link":
          payload.msgContents = [{ type: MsgContentMap.text, value: url }];
          break;
        default:
          break;
      }
      sendMessages(payload);
    },
    500,
    {
      leading: true,
      trailing: false,
    },
  );

  const handlePreview = () => {
    const path = getFullPath(Paths.Preview, {
      query: {
        href: url,
        type,
      },
      params: {
        versionNo: window.__WEB_VERSION__,
      },
    });
    preview({
      title,
      url: `${window.location.origin}${path}`,
    });
  };
  const hanldeClick: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    const { altKey, ctrlKey } = e;
    if (ctrlKey) {
      handleSend();
    } else if (altKey) {
      handlePreview();
    } else {
      setOpen((value) => !value);
    }
  };

  return (
    <DropdownMenu open={open}>
      <div className="flex items-center gap-2">
        <DropdownMenuTrigger
          onClick={hanldeClick}
          className="w-8 h-8 flex-shrink-0 rounded-4 overflow-hidden outline-none block"
        >
          {children}
        </DropdownMenuTrigger>
        <Typography.Text
          ellipsis={{ tooltip: true }}
          className="text-black-3 text-12"
        >
          {desc || "暂无描述"}
        </Typography.Text>
      </div>

      <DropdownMenuContent
        className="min-w-[150px]"
        onInteractOutside={() => setOpen(false)}
      >
        <DropdownMenuItem
          onClick={() => {
            handleSend();
            setOpen(false);
          }}
        >
          <span>发送</span>
          <DropdownMenuShortcut>Ctrl+单击</DropdownMenuShortcut>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            handlePreview();
            setOpen(false);
          }}
        >
          <span>预览</span>
          <DropdownMenuShortcut>Alt+单击</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const MeidaContent = ({
  data,
  buyerAccount,
  id,
  shortcutIndex,
}: {
  data: CopilotMedia[];
  buyerAccount: string;
  id: string;
  shortcutIndex?: number;
}) => {
  if (!data) return null;
  return (
    <div className="grid grid-cols-2 gap-[6px]">
      {data?.map((fileItem: CopilotMedia, index) => {
        let component = null;
        switch (fileItem.copilotMediaType) {
          case "pic":
            component = fileItem.url ? (
              <img
                src={fileItem.url}
                alt=""
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="bg-[#F2F2F2] rounded-4 flex items-center justify-center w-8 h-8">
                <Image className="text-[#ACB1B6] text-16" />
              </div>
            );
            break;
          case "video":
            component = fileItem.coverUrl ? (
              <div className="flex w-full h-full items-center justify-center relative">
                <img
                  src={fileItem.coverUrl}
                  alt=""
                  className="w-full h-full object-cover"
                />
                <PlayOneSolid className="text-white absolute text-16 " />
              </div>
            ) : (
              <div className="bg-[#F2F2F2] rounded-4 flex items-center justify-center w-8 h-8">
                <Video className="text-[#ACB1B6] text-16" />
              </div>
            );
            break;
          case "link":
            component = (
              <div className="bg-[#F2F2F2] rounded-4 flex items-center justify-center w-8 h-8">
                <LinkOneOutline className="text-[#ACB1B6] text-10  rotate-90" />
              </div>
            );
            break;
          default:
            break;
        }

        const props = {
          title: fileItem.fileName,
          url: fileItem.url,
          type: fileItem.copilotMediaType,
          agentTopicId: id,
          desc: fileItem?.desc,
          buyerAccount: buyerAccount,
        };

        return (
          <div key={fileItem.url} className="flex items-center ">
            {shortcutIndex && shortcutIndex + index <= 9 ? (
              <span className=" flex items-center justify-center h-8 self-start ">
                <span className="text-12  font-semibold  text-[--card-theme] rounded-sm mr-1   ">
                  {shortcutIndex + index}.
                </span>
              </span>
            ) : null}
            <ActionMenu {...props}>{component}</ActionMenu>
          </div>
        );
      })}
    </div>
  );
};
export default MeidaContent;
