import { debounce } from "lodash-es";
import { useMemo } from "react";
import useLast from "./useLast";

const useDebounce = <T extends (...args: any[]) => any>(
  fn: T,
  config?: Parameters<typeof debounce>[1],
  options?: Parameters<typeof debounce>[2],
) => {
  const getCallback = useLast(fn);
  const getConfig = useLast(config);
  return useMemo(
    () =>
      debounce(
        (...args: Parameters<T>) => {
          getCallback()(...args);
        },
        getConfig(),
        options,
      ) as unknown as T,
    [getCallback, getConfig, options],
  );
};
export default useDebounce;
