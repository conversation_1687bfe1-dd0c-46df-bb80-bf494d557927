import useSession from "@/business/hooks/useSession";
import {
  type SearchProductDataResultItem,
  searchByName,
} from "@/network/api/product";
import { useDebounce, useLast, useThrottle } from "@workspace/ui";
import {
  Button,
  Drawer,
  type DrawerProps,
  Input,
  type InputProps,
  Spin,
  Typography,
} from "antd";
import { useCallback, useMemo, useState } from "react";
import useSWRInfinite from "swr/infinite";
import CloseOutlined from "~icons/ant-design/close-outlined";

interface ProductBottomSheetProps extends DrawerProps {
  onOpenSKU?: (product: SearchProductDataResultItem) => void;
  onSetSPU?: (product: SearchProductDataResultItem) => Promise<void>;
}

const ProductBottomSheet = ({
  open,
  onClose,
  onOpenSKU,
  onSetSPU,
  ...others
}: ProductBottomSheetProps) => {
  const [spinning, setSpinning] = useState(false);
  const [search, setSearch] = useState("");
  const [debounceSearch, setDeounceSearch] = useState("");
  const { serviceAccount } = useSession();
  const getSellerAccount = useLast(serviceAccount?.account ?? "");
  const { data, setSize, isLoading, isValidating } = useSWRInfinite(
    (pageIndex: number, prePageData) => {
      if (pageIndex * prePageData?.pageSize > prePageData?.total) return null;
      return [
        searchByName.name,
        pageIndex + 1,
        debounceSearch,
        getSellerAccount(),
      ];
    },
    async ([, pageIndex, itemName]) => {
      const res = await searchByName({
        itemName,
        pageIndex: String(pageIndex),
        pageSize: 20,
        account: getSellerAccount(),
      }).promise.catch((error: Error) => error);
      if (res instanceof Error) return;
      if (res.data.success) {
        return res.data.data?.results ?? [];
      }
      return;
    },
    {
      revalidateOnFocus: false,
      initialSize: 1,
    },
  );

  const getLoading = useLast(isLoading || isValidating);

  const products = useMemo(() => {
    // biome-ignore lint/correctness/noFlatMapIdentity: <explanation>
    return data
      ?.flatMap((item) => item)
      .filter(Boolean) as SearchProductDataResultItem[];
  }, [data]);

  const _search = useDebounce((value: string) => {
    setDeounceSearch(value);
  }, 300);

  const handleSearch: InputProps["onChange"] = (e) => {
    setSearch(e.target.value);
    _search(e.target.value.trim());
  };

  const handleOnSetSPU = async (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
    product: SearchProductDataResultItem,
  ) => {
    // 避免点击tooltip避免误触发
    if ((e.target as HTMLDivElement).classList.contains("ant-tooltip-inner"))
      return;
    try {
      setSpinning(true);
      onSetSPU?.(product).finally(() => {
        setSpinning(false);
      });
    } catch (e) {
      setSpinning(false);
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onScroll: React.UIEventHandler<HTMLDivElement> = useThrottle(
    useCallback(
      (e) => {
        const { scrollHeight, scrollTop, clientHeight } =
          (e.target as HTMLDivElement) ?? {};
        if (
          Math.abs(scrollHeight - clientHeight - scrollTop) < 1 &&
          !getLoading()
        ) {
          setSize((pageIndex) => pageIndex + 1);
        }
      },
      [getLoading, setSize],
    ),
    300,
  );

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <span>商品SKU</span>
          <button
            type="button"
            className="bg-transparent outline-none border-none cursor-pointer "
            onClick={onClose}
          >
            <CloseOutlined />
          </button>
        </div>
      }
      footer={null}
      closeIcon={null}
      placement="bottom"
      open={open}
      height="80%"
      className=" overflow-hidden rounded-t-8  [&_.ant-drawer-body]:p-0   [&_.ant-drawer-header]:px-3 [&_.ant-drawer-header]:py-[14px]"
      {...others}
    >
      <div className="h-full flex flex-col  [&_.ant-spin-nested-loading]:flex-1  [&_.ant-spin-nested-loading]:h-0 [&_.ant-spin-container]:h-full">
        <div className="sticky top-0 left-0 right-0 p-2 bg-white z-10">
          <Input
            placeholder="商品标题或商品ID"
            value={search}
            onChange={handleSearch}
          />
        </div>
        <Spin spinning={spinning || isLoading}>
          <div
            onScroll={onScroll}
            className="flex flex-col flex-1 h-full  overflow-auto  box-border pb-2"
          >
            <div className="[&_>_:not([hidden])_~_:not([hidden])]:border-b  ">
              {products?.map((product) => {
                return (
                  <div
                    className="flex items-start px-3 py-2 gap-2 border border-transparent border-b-gray-3 border-solid hover:bg-gray-1 cursor-pointer"
                    key={product?.productId}
                    data-product={product?.productId}
                    onClick={(e) => handleOnSetSPU(e, product)}
                    onKeyDown={() => {}}
                  >
                    <img
                      className="w-10 h-10 object-cover rounded-4"
                      src={product?.pic}
                      alt=""
                    />
                    <div className="w-0 flex-1">
                      <Typography.Text
                        ellipsis={{
                          tooltip: product?.name,
                        }}
                      >
                        <span className="text-black-4 text-14">
                          {product?.name}
                        </span>
                      </Typography.Text>
                      <div className="text-black-3 text-12 flex items-start gap-2">
                        <Button
                          type="link"
                          className="h-auto p-0 ml-auto"
                          onClick={(e) => {
                            e.stopPropagation();
                            onOpenSKU?.(product);
                          }}
                        >
                          指定SKU
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Spin>
      </div>
    </Drawer>
  );
};

export default ProductBottomSheet;
