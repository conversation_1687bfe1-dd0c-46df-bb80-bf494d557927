import useConfig from "@/business/hooks/useConfig";
import { SupportedPlatforms } from "@/constant/platform";
import { WsCmdReceive, WsCmdSend } from "@/constant/protocol";
import { Platform, PlatformMap } from "@workspace/business";
import { useDebounce, useLast } from "@workspace/ui";
import {
  Button,
  Form,
  type FormProps,
  Input,
  Radio,
  Segmented,
  Table,
  type TableColumnsType,
} from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import ClientAction, { type ClientType } from "./ClientAction";

const platforms = Object.values(SupportedPlatforms)
  .filter(
    (item) =>
      ![Platform.CHANNELS, Platform.ALI1688, Platform.YOUZAN].includes(
        item.value,
      ),
  )
  .map((item) => {
    return {
      label: item.label,
      value: item.value,
    };
  });

const Client = () => {
  const { store, socket } = useConfig();
  const getStore = useLast(store);
  const [loading, setLoading] = useState(true);
  const [form] = Form.useForm();
  const platform: Platform = Form.useWatch("platform", form);
  const directory = Form.useWatch("directory", form);
  const useDefaultVersion = Form.useWatch("useDefaultVersion", form);
  const handleSelectDirectory = () => {
    socket?.sendMessage({
      cmd: WsCmdSend.SelectDirectory,
      data: {
        windowTitle: "选择目录",
        defaultDir: directory,
      },
    });
    store.setConfig({
      client: {
        ...store.client,
        fielPath: directory,
      },
    });
  };

  const { recommend, dataSource } = useMemo(() => {
    const list = store.supportedClientsInfo[platform]?.map((data) => {
      const array = store.localPlatformClientsInfo[platform];
      const target = array?.find?.(
        (item) =>
          item.platformVersion === data.version &&
          item.is64Bit === data.is64Bit,
      );
      return {
        ...data,
        localClient: target,
        currentUsed:
          store.client?.platformClient?.[platform]?._id === data?._id,
      };
    });
    const recommend = list?.find((item) => item.isDefaultVs);
    return {
      dataSource: list,
      recommend,
    };
  }, [
    store.supportedClientsInfo,
    store.localPlatformClientsInfo,
    platform,
    store.client,
  ]);

  const handleDeleteClient = useCallback(
    (client: ClientType) => {
      const path = client.localClient?.platformPath;
      if (path) {
        socket?.sendMessage({
          cmd: WsCmdSend.DeleteLocalPlatformClient,
          data: {
            path,
          },
        });
      }
    },
    [socket],
  );
  const handleInstallClient = useCallback(
    (record: ClientType) => {
      const payload = {
        id: record._id,
        fileUrl: record.fileUrl,
        isPlatformSoftware: true,
        platformType: record.platform,
        platformVersion: record.version,
        is64Bit: record.is64Bit,
      };
      socket?.sendMessage({
        cmd: WsCmdSend.DownloadFile,
        data: payload,
      });
    },
    [socket],
  );

  const handleCancelInstall = useDebounce((record: ClientType) => {
    socket?.sendMessage({
      cmd: WsCmdSend.CancelDownload,
      data: {
        id: record._id,
      },
    });
  }, 500);

  const columns: TableColumnsType<ClientType> = [
    {
      width: 40,
      dataIndex: "_id",
      render: (id) => <Radio disabled={useDefaultVersion} value={id} />,
    },
    {
      title: `${PlatformMap[platform]?.label || ""}版本`,
      dataIndex: "version",
      render: (version, record) => (
        <span>
          <span>{`${version}_${record.is64Bit ? "x64" : "x86"}`}</span>
          <span>{}</span>
        </span>
      ),
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 150,
      render: (_, record) => {
        return (
          <ClientAction
            key={record._id}
            data={record}
            onDelete={handleDeleteClient}
            onInstall={handleInstallClient}
            onCancel={handleCancelInstall}
          />
        );
      },
    },
  ];
  const versionOptions = [
    {
      label: `推荐版本 ${recommend?.version || ""} `,
      value: 1,
    },
    {
      label: "使用其他版本",
      value: 0,
    },
  ];

  const onValuesChange: FormProps["onValuesChange"] = (change, allValues) => {
    if (
      // 当前客户端浏览器版本不支持Object.hasOwn
      // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
      change.hasOwnProperty("useDefaultVersion") &&
      change.useDefaultVersion
    ) {
      const versionId =
        store.supportedClientsInfo?.[platform]?.find((item) => item.isDefaultVs)
          ?._id || "";
      form.setFieldsValue({
        versionId,
      });
      const [bit, version] = versionId.split("_");
      const data = {
        platformType: allValues.platform,
        platformVersion: version,
        is64Bit: bit === "x64",
      };
      socket?.sendMessage({
        cmd: WsCmdSend.SetPlatformClientVersion,
        data,
      });
      const client = Object.assign({}, store.client, {
        platformClient: {
          ...store.client.platformClient,
          [platform]: {
            ...data,
            _id: `${data?.is64Bit ? "x64" : "x86"}_${data?.platformVersion}`,
          },
        },
      });
      store.setConfig({
        client,
      });
    }

    // 当前客户端浏览器版本不支持Object.hasOwn
    // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
    else if (change.hasOwnProperty("platform")) {
      const platform = change.platform as Platform;
      const versionId =
        store.client.platformClient[platform]?._id ||
        store.supportedClientsInfo?.[platform]?.find((item) => item.isDefaultVs)
          ?._id;
      form.setFieldsValue({
        platform: platform,
        directory: store.installDirectory,
        versionId,
        useDefaultVersion: store.client.platformClient[platform]
          ?.platformVersion
          ? Number(
              store.client.platformClient[platform]?._id ===
                store.supportedClientsInfo?.[platform]?.find(
                  (item) => item.isDefaultVs,
                )?._id,
            )
          : Number(true),
      });
      if (versionId) {
        const [bit, version] = versionId.split("_");
        socket?.sendMessage({
          cmd: WsCmdSend.SetPlatformClientVersion,
          data: {
            platformType: platform,
            platformVersion: version,
            is64Bit: bit === "x64",
          },
        });
      }
    }
    // 当前客户端浏览器版本不支持Object.hasOwn
    // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
    else if (change.hasOwnProperty("versionId")) {
      const [bit, version] = change.versionId.split("_");
      const data = {
        platformType: allValues.platform,
        platformVersion: version,
        is64Bit: bit === "x64",
      };
      socket?.sendMessage({
        cmd: WsCmdSend.SetPlatformClientVersion,
        data,
      });
      store.setConfig({
        client: {
          ...store.client,
          platformClient: {
            ...store.client.platformClient,
            [platform]: {
              ...data,
              _id: change.versionId,
            },
          },
        },
      });
    }
  };

  useEffect(() => {
    if (socket) {
      socket?.sendMessage({
        cmd: WsCmdSend.GetClientConfig,
        data: undefined,
      });
      socket?.sendMessage({
        cmd: WsCmdSend.GetLocalPlatformClients,
        data: undefined,
      });
    }
  }, [socket]);

  useEffect(() => {
    const unsubscribe = socket?.on("message", (paylod) => {
      // 获取用户选择目录结果
      if (
        paylod.cmd === WsCmdReceive.SelectedDirectory &&
        paylod.data.directory
      ) {
        form.setFieldsValue({
          directory: paylod.data.directory,
        });
        socket?.sendMessage({
          cmd: WsCmdSend.SetPlatformClientsInstallDirectory,
          data: {
            path: paylod.data.directory,
          },
        });
        getStore().setConfig({
          installDirectory: paylod.data.directory,
        });
      }
    });
    return unsubscribe;
  }, [socket, form, getStore]);

  useEffect(() => {
    if (store.others.version && store.client.fielPath) {
      store.getAllPlatformSupportedClientsInfo().then(() => {
        setLoading(false);
        const _store = getStore();
        const platform = Platform.TAOBAO;
        const versionId =
          _store.client.platformClient[platform]?._id ||
          _store.supportedClientsInfo?.[platform]?.find(
            (item) => item.isDefaultVs,
          )?._id;

        form.setFieldsValue({
          platform: platform,
          directory: _store.installDirectory,
          versionId,
          useDefaultVersion: _store.client.platformClient[platform]
            ?.platformVersion
            ? Number(
                _store.client.platformClient[platform]?._id ===
                  _store.supportedClientsInfo?.[platform]?.find(
                    (item) => item.isDefaultVs,
                  )?._id,
              )
            : Number(true),
        });
      });
    }
  }, [
    store.getAllPlatformSupportedClientsInfo,
    store.others.version,
    store.client.fielPath,
    form,
    getStore,
  ]);

  return (
    <Form
      form={form}
      onValuesChange={onValuesChange}
      initialValues={{
        platform: Platform.TAOBAO,
        directory: store.installDirectory,
        useDefaultVersion: store.client?.platformClient?.[Platform.TAOBAO]
          ?.platformVersion
          ? Number(
              store.client?.platformClient?.[Platform.TAOBAO]?._id ===
                store.supportedClientsInfo?.[Platform.TAOBAO]?.find(
                  (item) => item.isDefaultVs,
                )?._id,
            )
          : Number(true),
      }}
      className="space-y-2 h-full flex flex-col"
    >
      <div className="space-y-2">
        <div className="after:content-[':'] after:mx-1">平台客户端安装路径</div>
        <div className="flex items-center gap-2">
          <Form.Item noStyle name="directory">
            <Input readOnly />
          </Form.Item>
          <Button onClick={handleSelectDirectory}>选择</Button>
        </div>
        <p className="text-black-2 text-12">
          变更路径后系统将自动迁移已安装内容，并在下次启动本软件生效，半小时内请勿关机
        </p>
      </div>

      <Form.Item name="platform">
        <Segmented options={platforms} />
      </Form.Item>
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <Form.Item noStyle name="useDefaultVersion">
            <Radio.Group options={versionOptions} />
          </Form.Item>
        </div>
        <p className="text-black-2 text-12">
          支持多账号登录。使用其他版本可能存在稳定性问题
        </p>
      </div>
      <Form.Item noStyle name="versionId">
        <Radio.Group className="w-full flex-1 h-0">
          <Table
            columns={columns}
            dataSource={dataSource}
            rowKey="_id"
            scroll={{ x: "max-content", y: "calc(100% - 55px)" }}
            className="h-full [&_.ant-spin-nested-loading]:h-full [&_.ant-spin-container]:h-full [&_.ant-table]:h-full [&_.ant-table-container]:h-full"
            pagination={false}
            loading={loading}
          />
        </Radio.Group>
      </Form.Item>
    </Form>
  );
};

export default Client;
