import {
  type ServiceAccountState,
  serviceAccountStore,
} from "@/business/components/ServiceccountProvider/store";
import useWebSocket from "@/business/hooks/useWebsocket";
import { useStore } from "zustand";

const defaultSelector = (state: ServiceAccountState) => state;

const useServiceAccount = <T = ServiceAccountState>(
  selector: (state: ServiceAccountState) => T = defaultSelector as (
    state: ServiceAccountState,
  ) => T,
) => {
  const socket = useWebSocket();
  const store = useStore(serviceAccountStore, selector);
  return { socket, store };
};
export default useServiceAccount;
