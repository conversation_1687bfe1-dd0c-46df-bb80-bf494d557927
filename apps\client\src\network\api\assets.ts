import client from "@/network/client";
import type { Platform } from "@workspace/business";
import type { APIResponse } from "@workspace/types";

export type SupportedClientInfo = {
  /** 平台客户端位数标识 */
  bit: "x64" | "x86";
  /** 是否默认版本 */
  isDefaultVs: boolean;
  /** 平台客户端下载路径 */
  platform: string;
  /** 平台类型 */
  platformCode: Platform;
  /** 平台客户端rar下载路径 */
  platformRar: string;
  /** 探域客户端下载路径 */
  tanyu: string;
  /** 最新探域客户端更新时间 */
  updDate: string;
  /** 最新探域客户端版本 */
  version: string;
};

export const getSupportedClientInfo = (data: {
  platform?: Platform;
  versionNo: string;
}) => {
  return client<APIResponse<SupportedClientInfo[]>>(
    "/api/im/client/agent/check-support",
    {
      method: "POST",
      data,
    },
  );
};
