import { AppEnum } from "@/constant/client";
import { type IAppItem, getAppLast } from "@/network/api/base";
import { type StateCreator, create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

type AppMap = Record<AppEnum, IAppItem | undefined>;

export interface AppData {
  appMap: AppMap;
  update: (key: AppEnum, app: IAppItem) => void;
  asyncUpdate: (data: {
    app: AppEnum;
    clientVersionNo: string;
  }) => Promise<IAppItem | undefined>;
}

export const createUserSlice: StateCreator<AppData> = (set, get) => ({
  appMap: {
    [AppEnum.SQS]: undefined,
  },
  update: (key, data) =>
    set({
      appMap: {
        ...get().appMap,
        [key]: {
          ...(get().appMap?.[key] ?? {}),
          ...(data ?? {}),
        },
      },
    }),
  asyncUpdate: async (params) => {
    const res = await getAppLast(params).promise.catch((error: Error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      const data = res.data.data;
      set({
        appMap: {
          ...get().appMap,
          [params.app]: {
            ...(get().appMap?.[params.app] ?? {}),
            ...(data ?? {}),
          },
        },
      });
      return data;
    }
    return;
  },
});

export const useLastApp = create<AppData>()(
  persist(
    (...args) => ({
      ...createUserSlice(...args),
    }),
    {
      name: "lastApp",
      storage: createJSONStorage(() => localStorage),
    },
  ),
);
