import DwIcon from "@/assets/logo/dw.svg?react";
import FgIcon from "@/assets/logo/fg.svg?react";
import JmIcon from "@/assets/logo/jm.svg?react";
import KsIcon from "@/assets/logo/ks.svg?react";
import PddIcon from "@/assets/logo/pdd.svg?react";
import QnIcon from "@/assets/logo/qn.svg?react";
import TanyuIcon from "@/assets/logo/tanyu.svg?react";
import { Platform } from "@workspace/business";

export const platformClientMap = {
  [Platform.TAOBAO]: {
    name: "千牛",
    icon: QnIcon,
    hostingKey: "QnLoginAccountList",
    cancelHostingKey: "QnLogoutAccountList",
  },
  [Platform.PDD]: {
    name: "拼多多",
    icon: PddIcon,
    hostingKey: "PddLoginAccountList",
    cancelHostingKey: "PddLogoutAccountList",
  },
  [Platform.KUAISHOU]: {
    name: "快手",
    icon: KsIcon,
    hostingKey: "KsLoginAccountList",
    cancelHostingKey: "KsLogoutAccountList",
  },
  [Platform.DOUDIAN]: {
    name: "飞鸽",
    icon: FgIcon,
    hostingKey: "DyLoginAccountList",
    cancelHostingKey: "DyLogoutAccountList",
  },
  [Platform.JD]: {
    name: "京麦",
    icon: JmIcon,
    hostingKey: "JdLoginAccountList",
    cancelHostingKey: "JdLogoutAccountList",
  },
  [Platform.DEWU]: {
    name: "得物",
    icon: DwIcon,
    hostingKey: "DewuLoginAccountList",
    cancelHostingKey: "DewuLogoutAccountList",
  },
};

export enum AppEnum {
  SQS = "sqs",
}

export enum AppTypeEnum {
  UNKNOWN = -1 /* 未知 */,
  SQS = 1 /* 数据洞察 */,
}

export const AppMap = {
  [AppEnum.SQS]: {
    name: "数据洞察",
    icon: TanyuIcon,
    pluginType: AppTypeEnum.SQS,
    value: AppEnum.SQS,
  },
};
