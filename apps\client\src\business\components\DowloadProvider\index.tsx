import useWebSocket from "@/business/hooks/useWebsocket";
import {
  DownloadStatusMap,
  WsCmdReceive,
  WsCmdSend,
  type WsMessage,
} from "@/constant/protocol/index";
import { useLast } from "@workspace/ui";
import { useCallback, useEffect } from "react";
import { Outlet } from "react-router";
import { type DownloadFile, downloadStore } from "./store";

const DownloadProvider = () => {
  const socket = useWebSocket();

  const getSocket = useLast(socket);

  const getStore = useCallback(() => {
    return downloadStore.getState();
  }, []);

  useEffect(() => {
    const socket = getSocket();
    if (!getStore()?.unsubscribe && socket) {
      const unsubscribe = socket?.on("message", (payload: WsMessage) => {
        switch (payload.cmd) {
          case WsCmdReceive.DownloadProgress: {
            const { data } = payload;
            const { fileList } = data;
            const files = fileList.reduce(
              (acc, cur) => {
                return Object.assign(acc, {
                  [cur.id]: {
                    ...cur,
                    progress:
                      cur.byteReceived && cur.fileSize
                        ? `${Math.floor((cur.byteReceived / cur.fileSize) * 100)}%`
                        : "0%",
                  },
                });
              },
              {} as Record<string, DownloadFile>,
            );

            getStore().setFiles(files);
            const needUpdate = fileList.find((item) =>
              (
                [
                  DownloadStatusMap.InstallSuccess,
                  DownloadStatusMap.InstallFailed,
                ] as number[]
              ).includes(item.status),
            );
            if (needUpdate) {
              socket?.sendMessage({
                cmd: WsCmdSend.GetLocalPlatformClients,
                data: undefined,
              });
            }
            const needRemove = fileList.find(
              (item) => item.status === DownloadStatusMap.Cancelled,
            );
            if (needRemove) {
              getStore().removeFile(needRemove.id);
            }
            break;
          }
        }
      });
      getStore().setSubscribe(unsubscribe);
    }

    return getStore().unsubscribe;
  }, [getStore, getSocket]);

  return <Outlet context={socket} />;
};

export default DownloadProvider;
