import ReceptionPreviewImg from "@/assets/images/console/view-img.png";
import useConfig from "@/business/hooks/useConfig";
import { WsCmdSend } from "@/constant/protocol/index";
import { useLast } from "@workspace/ui";
import { Button, Form, type FormProps, Radio, Switch, Tooltip } from "antd";
import { useMemo } from "react";
import QuestionCircleOutlined from "~icons/ant-design/question-circle-outlined";
import RecordHotKeys from "./RecordHotKeys";

const Reception = () => {
  const { store, socket } = useConfig();

  const [form] = Form.useForm();
  const getStore = useLast(store);

  const reception = useMemo(() => {
    const {
      enable,
      isAlone,
      shortcutForOpenconversation,
      shortcutForChangeMode,
    } = store.reception;
    return {
      enable,
      mode: Number(isAlone),
      shortcutForOpenconversation,
      shortcutForChangeMode,
    };
  }, [store.reception]);
  const onValuesChange: FormProps["onValuesChange"] = (change) => {
    // 当前客户端浏览器版本不支持Object.hasOwn
    // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
    if (change.hasOwnProperty("enable")) {
      socket?.sendMessage({
        cmd: WsCmdSend.ToggleReception,
        data: {
          isSwitchOn: change.enable,
        },
      });
      getStore().setConfig({
        reception: {
          ...getStore().reception,
          enable: change.enable,
        },
      });
    }
    // 当前客户端浏览器版本不支持Object.hasOwn
    // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
    else if (change.hasOwnProperty("mode")) {
      socket?.sendMessage({
        cmd: WsCmdSend.SetReceptionMode,
        data: {
          isAlone: Boolean(Number(change.mode)),
        },
      });
      getStore().setConfig({
        reception: {
          ...getStore().reception,
          isAlone: Boolean(Number(change.mode)),
        },
      });
    } else {
      getStore().setConfig({
        reception: {
          ...getStore().reception,
          ...change,
        },
      });
    }
  };

  const handlePreview = () => {
    const imgUrl = new URL(ReceptionPreviewImg, import.meta.url).href;
    socket?.sendMessage({
      cmd: WsCmdSend.PreviewResource,
      data: {
        title: "聚合页面",
        url: imgUrl,
      },
    });
  };

  console.log("reception", reception);

  return (
    <Form
      form={form}
      name="reception"
      initialValues={reception}
      onValuesChange={onValuesChange}
    >
      <h3 className="font-medium text-14 text-black-5 ">聚合页面</h3>
      <div className="flex items-center gap-2">
        <Form.Item noStyle name="enable">
          <Switch />
        </Form.Item>
        <span>开启后显示聚合接待列表</span>
        <Button type="link" onClick={handlePreview}>
          查看示例
        </Button>
      </div>
      <Form.Item name="mode">
        <Radio.Group
          options={[
            { label: "吸附左侧", value: 0 },
            { label: "单独窗口", value: 1 },
          ]}
        />
      </Form.Item>
      <div className="space-y-4">
        <h3 className="flex items-center gap-2">
          <span>快捷键</span>
          <Tooltip title="快捷键组合的第一个键位需是特殊键,如Ctrl、Alt、Shift等">
            <QuestionCircleOutlined />
          </Tooltip>
        </h3>
        <div className="space-y-2">
          <div className="text-black-3 text-12">
            <p>切换至聚合接待页面等待时间最长的会话</p>
            <p>快速跳转到等待时间最长的买家消息窗口，无需鼠标点击</p>
          </div>
          <Form.Item noStyle name="shortcutForOpenconversation">
            <RecordHotKeys
              placeholder="按下组合键 例如 Alt + 1"
              cmd={WsCmdSend.SetReceptionShotcutForOpenConversation}
            />
          </Form.Item>
        </div>
        <div className="space-y-2">
          <div className="text-black-3 text-12">
            <p>聚合接待页面吸附左侧/单独窗口</p>
            <p>默认吸附于平台客户端左侧，按快捷键切</p>
          </div>
          <Form.Item noStyle name="shortcutForChangeMode">
            <RecordHotKeys
              placeholder="按下组合键 例如 Alt + 1"
              cmd={WsCmdSend.SwitchReceptionShotcutForMode}
            />
          </Form.Item>
        </div>
      </div>
    </Form>
  );
};

export default Reception;
