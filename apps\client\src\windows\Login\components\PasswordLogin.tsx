import { useAccount } from "@/business/hooks/useAccount";
import useWebSocket from "@/business/hooks/useWebsocket";
import {
  AccountTypeMap,
  WsCmdReceive,
  WsCmdSend,
  type WsReceiveMessage,
} from "@/constant/protocol";
import { postPasswordLogin } from "@/network/api/account";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  usePartialState,
} from "@workspace/ui";
import { Button, Checkbox, Form, Input, message } from "antd";
import clsx from "clsx";
import { useEffect } from "react";
import { useState } from "react";
import CloseOutlined from "~icons/ant-design/close-outlined";
import DownOutlined from "~icons/ant-design/down-outlined";
import LockOutlined from "~icons/ant-design/lock-outlined";
import UserOutlined from "~icons/ant-design/user-outlined";

const initState = {
  isAutoFill: false,
  openList: false,
  init: false,
};

const PasswordLogin = () => {
  const socket = useWebSocket();
  const account = useAccount();
  const [form] = Form.useForm();
  const accountValue = Form.useWatch("account", form);
  const [state, setState] = usePartialState(initState);
  const [accountList, setAccountList] = useState<
    { password: string; userName: string }[]
  >([]);
  const onSubmit = async (
    values: Parameters<typeof postPasswordLogin>[0] & { ifRemember?: boolean },
  ) => {
    const { ifRemember = false, ...others } = values;
    const res = await postPasswordLogin(others).promise.catch(
      (error: Error) => error,
    );
    if (res instanceof Error) return;
    if (res.data.success) {
      account.setToken(res.data.data);
      account.updateAccountType(AccountTypeMap.Password);
      localStorage.removeItem("accountMap");
      account.asyncUpdate().then((info) => {
        if (info) {
          socket?.sendMessage({
            cmd: WsCmdSend.AccountLogin,
            data: {
              password: values.password,
              userName: values.account,
              cookie: res.data.data as string,
              showUserName: info.account.account,
              accountType: AccountTypeMap.Password,
              isRemember: Number(ifRemember),
            },
          });
        }
      });
    }
  };

  const toView = (url: string) => {
    socket?.sendMessage({
      cmd: "open_url",
      data: {
        url: url,
      },
    });
  };

  const handleAutoFillAccount = (value: (typeof accountList)[number]) => {
    setState({ isAutoFill: true });
    form.setFieldsValue({
      account: value.userName,
      password: value.password,
    });
  };

  const handleRemoveAccount = (value: (typeof accountList)[number]) => {
    setAccountList((pre) =>
      pre.filter((item) => item.userName !== value.userName),
    );
    socket?.sendMessage({
      cmd: WsCmdSend.DeleteRememberAccount,
      data: {
        userName: value.userName,
      },
    });
    const { account } = form.getFieldsValue();
    if (value.userName === account) {
      form.setFieldsValue({
        account: "",
        password: "",
      });
    }
  };

  const handleChangePassword = () => {
    setState({ isAutoFill: false });
    form.setFieldsValue({
      password: "",
    });
  };

  useEffect(() => {
    const unsubscribe = socket?.on("message", (payload: WsReceiveMessage) => {
      switch (payload.cmd) {
        case WsCmdReceive.RememberAccountList: {
          setAccountList(payload.data);
          form.setFieldsValue({
            ifRemember: Boolean(payload.data?.length),
          });
          if (payload.data?.length) {
            setState({ isAutoFill: true });
            form.setFieldsValue({
              account: payload.data[0].userName,
              password: payload.data[0].password,
            });
          }
          break;
        }
        default:
          break;
      }
    });
    if (socket?.readyState === 1) {
      socket?.sendMessage({
        cmd: WsCmdSend.GetRememberAccount,
        data: undefined,
      });
      setState({ init: false });
    }
    return unsubscribe;
  }, [socket, form, setState]);

  return (
    <Form
      className="flex h-full flex-col gap-4"
      initialValues={{ policy: true, ifRemember: false }}
      form={form}
      onFinish={onSubmit}
    >
      <div className="flex flex-col ">
        <Form.Item>
          <Form.Item
            name="account"
            noStyle
            rules={[{ required: true, message: "账号不能为空" }]}
          >
            <Input
              autoFocus
              placeholder="请输入账号"
              prefix={<UserOutlined className="mr-2.5 text-black-1" />}
              suffix={
                accountList?.length ? (
                  <DropdownMenu
                    open={state.openList}
                    onOpenChange={(value) => setState({ openList: value })}
                  >
                    <DropdownMenuTrigger className="text-black-1">
                      <DownOutlined
                        className={clsx(
                          "transition-transform",
                          state.openList ? "rotate-180" : "",
                        )}
                      />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      sideOffset={14}
                      align="end"
                      alignOffset={-12}
                      className="w-[372px]"
                    >
                      {accountList.map((item) => (
                        <DropdownMenuItem
                          key={item.userName}
                          className="flex items-center"
                          onClick={() => handleAutoFillAccount(item)}
                        >
                          <span className="flex-1">{item.userName}</span>
                          <Button
                            color="default"
                            variant="text"
                            htmlType="button"
                            className="p-0.5 h-auto"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveAccount(item);
                            }}
                          >
                            <CloseOutlined />
                          </Button>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : null
              }
              className="py-2.5"
            />
          </Form.Item>
        </Form.Item>
        <Form.Item
          name="password"
          rules={[{ required: true, message: "密码不能为空" }]}
        >
          {state.isAutoFill ? (
            <Input
              type="password"
              placeholder="请输入密码"
              prefix={<LockOutlined className="mr-2.5 text-black-1" />}
              className="py-2.5"
              onChange={handleChangePassword}
            />
          ) : (
            <Input.Password
              placeholder="请输入密码"
              prefix={<LockOutlined className="mr-2.5 text-black-1" />}
              className="py-2.5"
              autoFocus={Boolean(accountValue)}
            />
          )}
        </Form.Item>
        <div className="flex justify-between">
          <Form.Item noStyle name="ifRemember" valuePropName="checked">
            <Checkbox>记住密码</Checkbox>
          </Form.Item>
          <button
            type="button"
            className="ml-auto cursor-pointer bg-transparent text-14 text-black-2 hover:text-blue-7"
            onClick={() => toView(import.meta.env.VITE_CHANGE_PASSWORD_URL)}
          >
            忘记密码
          </button>
        </div>

        <Button
          type="primary"
          className="mb-3 mt-4 block w-full py-2 h-auto"
          htmlType="submit"
        >
          立即登录
        </Button>
        {/* <div className="text-center">
          <span>
            还没有账号？
            <Button
              type="link"
              className="h-auto p-0"
              onClick={() => toView(import.meta.env.VITE_REGISTER_URL)}
            >
              立即注册
            </Button>
          </span>
        </div> */}
      </div>
      <div className="mt-auto space-x-3 text-center">
        <Form.Item
          noStyle
          name="policy"
          rules={[
            {
              validator: (_, value) => {
                if (!value) {
                  message.warning(
                    "请阅读并同意《隐私政策》与《探域网络服务条款》",
                  );
                  return Promise.reject();
                }
                return Promise.resolve(value);
              },
            },
          ]}
          valuePropName="checked"
        >
          <Checkbox />
        </Form.Item>
        <span className="text-14 text-black-5">
          我已阅读并同意{" "}
          <Button
            type="link"
            className="h-auto p-0"
            onClick={() => toView(import.meta.env.VITE_PRIVACY_POLICY)}
          >
            《隐私政策》
          </Button>
          与
          <Button
            type="link"
            className="h-auto p-0"
            onClick={() => toView(import.meta.env.VITE_NETWORK_SERVICE)}
          >
            《探域网络服务条款》
          </Button>
        </span>
      </div>
    </Form>
  );
};

export default PasswordLogin;
