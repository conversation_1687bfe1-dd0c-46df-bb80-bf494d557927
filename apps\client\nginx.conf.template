server {
    listen       80;
    server_name  localhost;
    root /app;

    access_log  /var/log/nginx/access.log;
    error_log  /var/log/nginx/error.log;

    location / {
        root /app;
        try_files $uri $uri/ /index.html;
        error_page 404 /index.html;

        if ($request_filename ~ .*\.(htm|html)$){
            add_header Cache-Control no-cache;
        }

        if ($request_uri ~* /.*\.(js|css)$) {
            # add_header Cache-Control max-age=2592000;
            expires 30d;
        }
    }
}
