import Big from "big.js";

export const mul = (...args: Big.BigSource[]) => {
  const [first, ...others] = args;
  const source = new Big(first as Big.BigSource);
  others.forEach((item) => {
    source.mul(item);
  });
  return source;
};

export const div = (...args: Big.BigSource[]) => {
  const [first, ...others] = args;
  const source = new Big(first as Big.BigSource);
  others.forEach((item) => {
    source.div(item);
  });
  return source;
};

export const toPrice = (num: Big.BigSource) => {
  return new Big(num).toNumber().toLocaleString("zh", {
    style: "currency",
    currency: "CNY",
  });
};

export class NumTool {
  source: Big.Big;
  constructor(value: Big.BigSource) {
    try {
      this.source = new Big(value);
    } catch {
      this.source = new Big(0);
    }
  }
  mul(...args: Big.BigSource[]) {
    args.forEach((item) => {
      this.source = this.source.mul(item);
    });
    return this;
  }
  div(...args: Big.BigSource[]) {
    args.forEach((item) => {
      this.source = this.source.div(item);
    });
    return this;
  }
  toPrice() {
    return this.source.toNumber().toLocaleString("zh", {
      style: "currency",
      currency: "CNY",
    });
  }
}

export const formatSeconds = (secondsNum: number): string => {
  let totalSeconds = secondsNum;
  // 确保输入是非负数
  if (!totalSeconds) {
    return "-";
  }
  totalSeconds = Number(totalSeconds);

  const seconds = totalSeconds % 60;
  const minutes = Math.floor(totalSeconds / 60) % 60;
  const hours = Math.floor(totalSeconds / 3600);

  let result = "";

  // 只有当小时数大于0时，才显示小时
  if (hours > 0) {
    result += `${hours}时`;
  }

  // 只有当分钟数大于0时，才显示分钟
  if (minutes > 0) {
    result += `${minutes}分`;
  }

  // 秒数始终显示
  result += `${seconds}秒`;

  return result;
};
