import type { Platform } from "@workspace/business";
import type { AppTypeEnum } from "../client";
import type { WsProtocol } from "./base";
import type { AccountTypeMap } from "./receive";

export const WsCmdSend = {
  /** 集团帐号登录 */
  AccountLogin: "tanyu_account_login",
  /** 打开网页 */
  OpenUrl: "open_url",
  /** 获取基础设置 */
  GetBaseConfig: "get_basic_config",
  /** 设置基础设置 */
  SetBaseConfig: "change_basic_config",
  /** 设置催单快捷键 */
  SetReminderShortcut: "set_reminder_shortcut",
  /** 设置开片发送快捷键 */
  SetCardMsgShortcut: "set_card_msg_shortcut",
  /** 设置单条信息快捷键 */
  SetSingleMsgShortcut: "set_single_msg_shortcut",
  /** 获取智能体设置 */
  GetAgentConfig: "get_agent_config",
  /** 设置智能体设置 */
  SetAgentConfig: "change_agent_config",
  /** 获取应用设置 */
  GetAppConfig: "get_app_page_config",
  /** 获取客户端设置 */
  GetClientConfig: "get_client_page_config",
  /** 获取智能体快捷键设置 */
  GetAgentShotcutConfig: "get_quick_send_shortcut",
  /** 设置智能体快捷键设置 */
  SetAgentShotcutConfig: "set_quick_send_shortcut",
  /** 获取聚合接待设置 */
  GetReceptionConfig: "get_aggregated_list_config",
  /** 开/关聚合接待 */
  ToggleReception: "change_aggregated_list_switch",
  /** 设置聚合接待窗口模式 */
  SetReceptionMode: "change_aggregated_list_alone_status",
  /** 设置聚合接待打开会话快捷键 */
  SetReceptionShotcutForOpenConversation:
    "set_aggregated_list_open_conversation_shortcut",
  /** 设置聚合接待切换窗口模式快捷键 */
  SwitchReceptionShotcutForMode: "set_aggregated_list_alone_shortcut",
  /** 启动平台客户端 */
  OpenPlatformClient: "open_platform_software",
  /** 客服帐号取消托管 */
  CancelServiceHosting: "account_logout",
  /** 托管客服帐号 */
  HostingServiceAccount: "account_login",
  /** 拉取商品主图 */
  PullProductImage: "get_shop_product",
  /** 下载文件 */
  DownloadFile: "download_file",
  /** 获取本地已下载平台客户端信息 */
  GetLocalPlatformClients: "get_local_platformform_list",
  /** 选择目录 */
  SelectDirectory: "get_existing_directory",
  /** 获取平台客户端安装目录 */
  GetPlatformClientsInstallDirectory: "get_file_save_path",
  /** 设置平台客户端安装目录 */
  SetPlatformClientsInstallDirectory: "set_file_save_path",
  /** 删除本地指定版本平台客户端 */
  DeleteLocalPlatformClient: "filesystem_delete_all",
  /** 设置指定平台的版本 */
  SetPlatformClientVersion: "change_selected_platform_version",
  /** 打开快手机器人 */
  OpenKsRobot: "open_ks_robot",
  /** 取消下载 */
  CancelDownload: "cancel_download",
  /** 设置开机自启动 */
  SetAutoBoot: "set_auto_start",
  /** 不同平台插入文本 */
  InsertTextToPlatformClient: "insert_text_to_platform",
  /** 账号登出 */
  Logout: "tanyu_account_logout",
  /* 设置标题 */
  SetTitle: "set_title",
  /** 获取聚合接待会话列表 */
  GetReceptionList: "get_aggregate_list",
  /** 打开指定买家会话窗口 */
  OpenConversation: "open_customer_dialog",
  /** 获取打开最长等待会话快捷键 */
  GetShortCutForOpenConversation:
    "get_aggregated_list_open_conversation_shortcut",
  /** 删除聚合接待会话 */
  DeleteReceptionConversations: "del_aggregate_items",
  /** 删除聚合接待指定会话 */
  DeleteReceptionOneConversation: "del_aggregate_item",
  /** 资源预览 */
  PreviewResource: "resource_preview",
  /** 刷新帐号列表 */
  RefreshAccountList: "refresh_account_list",
  /** 启动插件 */
  OpenPlugin: "open_plugin",
  /** 上报tanyu_group_account */
  SendTanyuGroupAccount: "tanyu_group_account",
  /** 删除帐号*/
  DeleteRememberAccount: "delete_remember_account",
  /** 获取记住的帐号 */
  GetRememberAccount: "get_remember_account",
  /** 删除星标  */
  DelStarAggregateItem: "del_star_aggregate_item",
  /** 更新星标 */
  UpdateStarAggregateItem: "update_star_aggregate_item",
} as const;

/** 使用默认浏览器打开网页 */
export type MessageToOpenUrl = WsProtocol<
  typeof WsCmdSend.OpenUrl,
  { url: string }
>;
/** 集团帐号登录成功 */
export type MessageToLogin = WsProtocol<
  typeof WsCmdSend.AccountLogin,
  {
    userName: string;
    password: string;
    cookie: string;
    showUserName: string;
    accountType: (typeof AccountTypeMap)[keyof typeof AccountTypeMap];
    isRemember: number;
  }
>;

/** 取消托管 */
export type MessageToCancelServiceHosting = WsProtocol<
  typeof WsCmdSend.CancelServiceHosting,
  {
    /** 千牛 */
    QnLogoutAccountList?: string[];
    /** 京麦 */
    JdLogoutAccountList?: string[];
    /** 拼多多 */
    PddLogoutAccountList?: string[];
    /** 快手 */
    KsLogoutAccountList?: string[];
    /** 飞鸽 */
    DyLogoutAccountList?: string[];
    /** 得物 */
    DewuLogoutAccountList?: string[];
  }
>;

/** 拉取商品主图 */
export type MessageToPullProductImage = WsProtocol<
  typeof WsCmdSend.PullProductImage,
  {
    /** 平台类型 */
    platformType: Platform;
    /** 账号 */
    account: string;
  }
>;

export type MessageToHostingServiceAccount = WsProtocol<
  typeof WsCmdSend.HostingServiceAccount,
  {
    /** 千牛 */
    QnLoginAccountList?: string[];
    /** 京麦 */
    JdLoginAccountList?: string[];
    /** 拼多多 */
    PddLoginAccountList?: string[];
    /** 快手 */
    KsLoginAccountList?: string[];
    /** 飞鸽 */
    DyLoginAccountList?: string[];
    /** 得物 */
    DewuLoginAccountList?: string[];
  }
>;

/** 启动平台客户端 */
export type MessageToOpenPlatformClient = WsProtocol<
  typeof WsCmdSend.OpenPlatformClient,
  {
    platformType: Platform;
  }
>;

/** 获取基础设置 */
export type MessageToGetBaseConfig = WsProtocol<
  typeof WsCmdSend.GetBaseConfig,
  undefined
>;

/** 设置基础设置 */
export type MessageToSetBaseConfig = WsProtocol<
  typeof WsCmdSend.SetBaseConfig,
  {
    offlineNotifySwitch?: boolean;
    updateNotifySwitch?: boolean;
    autoLoginSwitch?: boolean;
  }
>;
/** 设置催单快捷键 */
export type MessageToSetReminderShortcut = WsProtocol<
  typeof WsCmdSend.SetReminderShortcut,
  {
    shortcut: string;
  }
>;
/** 设置开片发送快捷键 */
export type MessageToSetCardMsgShortcut = WsProtocol<
  typeof WsCmdSend.SetCardMsgShortcut,
  {
    shortcut: string;
  }
>;
/** 设置单条信息快捷键 */
export type MessageToSetSingleMsgShortcut = WsProtocol<
  typeof WsCmdSend.SetSingleMsgShortcut,
  {
    shortcut: string;
  }
>;

/** 获取智能体设置 */
export type MessageToGetAgentConfig = WsProtocol<
  typeof WsCmdSend.GetAgentConfig,
  undefined
>;

/** 设置智能体设置 */
export type MessageToSetAgentConfig = WsProtocol<
  typeof WsCmdSend.SetAgentConfig,
  {
    oldSidebarSwitch?: boolean;
  }
>;

/** 获取应用设置 */
export type MessageToGetAppConfig = WsProtocol<
  typeof WsCmdSend.GetAppConfig,
  undefined
>;

/** 获取客户端设置 */
export type MessageToGetClientConfig = WsProtocol<
  typeof WsCmdSend.GetClientConfig,
  undefined
>;

/** 获取智能体快捷键设置 */
export type MessageToGetAgentShotcutConfig = WsProtocol<
  typeof WsCmdSend.GetAgentShotcutConfig,
  undefined
>;

/** 设置智能体快捷键设置 */
export type MessageToSetAgentShotcutConfig = WsProtocol<
  typeof WsCmdSend.SetAgentShotcutConfig,
  {
    shortcutList: { number: number; shortcut: string }[];
  }
>;

/** 获取聚合接待设置 */
export type MessageToGetReceptionConfig = WsProtocol<
  typeof WsCmdSend.GetReceptionConfig,
  undefined
>;

/** 开/关聚合接待 */
export type MessageToToggleReception = WsProtocol<
  typeof WsCmdSend.ToggleReception,
  {
    isSwitchOn: boolean;
  }
>;

/** 设置聚合接待窗口模式 */
export type MessageToSetReceptionMode = WsProtocol<
  typeof WsCmdSend.SetReceptionMode,
  {
    isAlone: boolean;
  }
>;

/** 设置聚合接待打开会话快捷键设置 */
export type MessageToSetReceptionShotcutForOpenConversation = WsProtocol<
  typeof WsCmdSend.SetReceptionShotcutForOpenConversation,
  {
    shortcut: string;
  }
>;

/** 设置聚合接待切换窗口模式快捷键设置 */
export type MessageToSwitchReceptionShotcutForMode = WsProtocol<
  typeof WsCmdSend.SwitchReceptionShotcutForMode,
  {
    shortcut: string;
  }
>;

/** 下载文件 */
export type MessageToDownloadFile = WsProtocol<
  typeof WsCmdSend.DownloadFile,
  {
    /** 文件id，用于标识一个文件 */
    id: string;
    /** 文件下载地址 */
    fileUrl: string;
    /** 额外字段 */
    extra?: string;
    /** 是否平台软件，平台软件存放在另一个目录 */
    isPlatformSoftware: boolean;
    /** 平台类型 */
    platformType?: Platform;
    /** 是否64位程序 */
    is64Bit?: boolean;
    /** 平台版本号 */
    platformVersion?: string;
    isPlugin?: boolean;
    /** 插件类型 */
    pluginType?: AppTypeEnum;
  }
>;

/** 取消下载 */
export type MessageToCancelDownload = WsProtocol<
  typeof WsCmdSend.CancelDownload,
  {
    id: string;
  }
>;
/** 选择目录 */
export type MessageToSelectDirectory = WsProtocol<
  typeof WsCmdSend.SelectDirectory,
  {
    windowTitle: string;
    defaultDir: string;
  }
>;
/** 获取本地已下载平台客户端信息 */
export type MessageToGetLocalPlatformClients = WsProtocol<
  typeof WsCmdSend.GetLocalPlatformClients,
  undefined
>;

/** 获取平台客户端安装目录 */
export type MessageToGetPlatformClientsInstallDirectory = WsProtocol<
  typeof WsCmdSend.GetPlatformClientsInstallDirectory,
  undefined
>;

/** 获取平台客户端安装目录 */
export type MessageToSetPlatformClientsInstallDirectory = WsProtocol<
  typeof WsCmdSend.SetPlatformClientsInstallDirectory,
  { path: string }
>;

/** 删除本地指定版本平台客户端 */
export type MessageToDeleteLocalPlatformClient = WsProtocol<
  typeof WsCmdSend.DeleteLocalPlatformClient,
  {
    path: string;
  }
>;

/** 设置指定平台的版本 */
export type MessageToSetPlatformClientVersion = WsProtocol<
  typeof WsCmdSend.SetPlatformClientVersion,
  {
    platformType: Platform;
    platformVersion: string;
    is64Bit: boolean;
  }
>;

/** 打开快手机器人 */
export type MessageToOpenKsRobot = WsProtocol<
  typeof WsCmdSend.OpenKsRobot,
  undefined
>;

/** 设置开机自启动 */
export type MessageToSetAutoBoot = WsProtocol<
  typeof WsCmdSend.SetAutoBoot,
  {
    isAutoStart: boolean;
  }
>;

/** 不同平台插入文本 */
export type MessageToInsertTextToPlatformClient = WsProtocol<
  typeof WsCmdSend.InsertTextToPlatformClient,
  {
    platformType: Platform;
    content: string;
  }
>;
/** 账号登出 */
export type MessageToLogout = WsProtocol<
  typeof WsCmdSend.Logout,
  { userName: string }
>;

/** 设置窗口标题 */
export type MessageToSetTitle = WsProtocol<
  typeof WsCmdSend.SetTitle,
  { title: string }
>;

/** 获取聚合接待会话列表 */
export type MessageToGetReceptionList = WsProtocol<
  typeof WsCmdSend.GetReceptionList,
  undefined
>;

/** 打开指定买家会话窗口 */
export type MessageToOpenConversation = WsProtocol<
  typeof WsCmdSend.OpenConversation,
  {
    // 淘宝平台特有
    appCid?: string;
    currentBuyerUserId: string;
    account: string;
    isTop: boolean;
    platformType: Platform;
    // 抖音特有
    shopName?: string;
    sessionKey?: string;
    // 京东平台特有
    platformSessionId?: string;
  }
>;

/** 获取打开最长等待会话快捷键 */
export type MessageToGetShortcutForOpenConversation = WsProtocol<
  typeof WsCmdSend.GetShortCutForOpenConversation,
  undefined
>;

/** 清空聚合接待会话列表 */
export type MessageToDeleteReceptionConversations = WsProtocol<
  typeof WsCmdSend.DeleteReceptionConversations,
  undefined
>;

/** 删除聚合接待指定会话  */
export type MessageToDeleteReceptionOneConversation = WsProtocol<
  typeof WsCmdSend.DeleteReceptionOneConversation,
  {
    sessionKey: string;
    platformType: Platform;
  }
>;

/** 资源预览 */
export type MessageToPreviewResource = WsProtocol<
  typeof WsCmdSend.PreviewResource,
  {
    title?: string;
    url: string;
  }
>;

/** 刷新帐号列表 */
export type MessageToRefreshAccountList = WsProtocol<
  typeof WsCmdSend.RefreshAccountList,
  undefined
>;

/** 启动插件 */
export type MessageToOpenPlugin = WsProtocol<
  typeof WsCmdSend.OpenPlugin,
  {
    pluginType: AppTypeEnum;
  }
>;

/** 上报tanyu_group_account */
export type MessageToSendTanyuGroupAccount = WsProtocol<
  typeof WsCmdSend.SendTanyuGroupAccount,
  { groupAccount: string }
>;

/** 删除帐号 */
export type MessageToDeleteRememberAccount = WsProtocol<
  typeof WsCmdSend.DeleteRememberAccount,
  {
    userName: string;
  }
>;

/** 获取记住的帐号 */
export type MessageToGetRememberAccount = WsProtocol<
  typeof WsCmdSend.GetRememberAccount,
  undefined
>;

export type MessageToDeleteStarMark = WsProtocol<
  typeof WsCmdSend.DelStarAggregateItem,
  {
    sessionKey?: string;
    account?: string;
    platformType: Platform[];
    starType?: string[];
  }
>;

export type MessageToUpdateStarMark = WsProtocol<
  typeof WsCmdSend.UpdateStarAggregateItem,
  {
    sessionKey: string;
    account: string;
    starType: string;
    starId: string;
  }
>;

export type WsSendMessage =
  | MessageToOpenUrl
  | MessageToLogin
  | MessageToPullProductImage
  | MessageToRefreshAccountList
  | MessageToOpenPlatformClient
  | MessageToGetBaseConfig
  | MessageToSetBaseConfig
  | MessageToSetReminderShortcut
  | MessageToSetCardMsgShortcut
  | MessageToSetSingleMsgShortcut
  | MessageToGetAgentConfig
  | MessageToSetAgentConfig
  | MessageToGetAppConfig
  | MessageToGetClientConfig
  | MessageToGetAgentShotcutConfig
  | MessageToSetAgentShotcutConfig
  | MessageToGetReceptionConfig
  | MessageToToggleReception
  | MessageToSetReceptionMode
  | MessageToSetReceptionShotcutForOpenConversation
  | MessageToSwitchReceptionShotcutForMode
  | MessageToCancelServiceHosting
  | MessageToHostingServiceAccount
  | MessageToDownloadFile
  | MessageToGetLocalPlatformClients
  | MessageToSelectDirectory
  | MessageToGetPlatformClientsInstallDirectory
  | MessageToSetPlatformClientsInstallDirectory
  | MessageToDeleteLocalPlatformClient
  | MessageToSetPlatformClientVersion
  | MessageToOpenKsRobot
  | MessageToCancelDownload
  | MessageToSetAutoBoot
  | MessageToInsertTextToPlatformClient
  | MessageToSetTitle
  | MessageToGetReceptionList
  | MessageToOpenConversation
  | MessageToGetShortcutForOpenConversation
  | MessageToDeleteReceptionConversations
  | MessageToDeleteReceptionOneConversation
  | MessageToPreviewResource
  | MessageToOpenPlugin
  | MessageToSendTanyuGroupAccount
  | MessageToDeleteRememberAccount
  | MessageToGetRememberAccount
  | MessageToDeleteStarMark
  | MessageToUpdateStarMark;
