import client from "@/network/client";
import type { APIResponse } from "@workspace/types";

export interface IBuyerPortraitInfo {
  buyerInfo: Array<{
    name: string;
    order: number;
    value: number;
  }>;
  consult: [];
  remark: string;
  significance: Array<{
    name: string;
    order: number;
    value: string;
  }>;
}

/** 获取买家系统标签 */
export function getBuyerSystemLabel(params: { buyerNick: string }) {
  return client<APIResponse<IBuyerPortraitInfo>>(
    "/api/im/agent-buyer/portrait-info",
    {
      method: "GET",
      params,
      shouldLog: true,
    },
  );
}

/** 获取买家系统标签 */
export function setBuyerSystemLabel(data: {
  buyerNick: string;
  name: string;
  order: number;
  remark: string;
  value: number;
}) {
  return client<APIResponse<unknown>>(
    "/api/im/agent-buyer/portrait-info-edit",
    {
      method: "POST",
      data,
      shouldLog: true,
    },
  );
}

/** 获取用户绑定标签 */
export function getUserBindLabel(params: { buyerNick: string }) {
  return client<
    APIResponse<
      {
        groupId: number;
        labelId: string;
        labelName: string;
        labelType: number;
      }[]
    >
  >("/api/im/agent-buyer/bind-manual-label", {
    params,
    shouldLog: true,
  });
}

export const saveManualLabel = (data: {
  buyerNick: string;
  labelIds: Array<string | number>;
  addLabelIds: Array<string | number>;
  delLabelIds: Array<string | number>;
}) =>
  client<APIResponse<boolean>>("/api/im/agent-buyer/save-manual-label", {
    method: "POST",
    data,
    shouldLog: true,
  });
