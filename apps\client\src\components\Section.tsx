import clsx from "clsx";

const Section = ({
  children,
  title,
  stickyHeader,
  ...others
}: {
  children: React.ReactNode;
  title: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  stickyHeader?: boolean;
}) => {
  return (
    <section {...others}>
      <div
        className={clsx(
          "py-4",
          stickyHeader ? "sticky top-0 z-10 bg-white" : "",
        )}
      >
        <h2 className="flex items-center gap-2 text-16 font-medium text-black-5 before:h-6 before:w-1 before:bg-blue-7">
          {title}
        </h2>
      </div>

      <div className="bg-gray-1 p-4">{children}</div>
    </section>
  );
};

export default Section;
