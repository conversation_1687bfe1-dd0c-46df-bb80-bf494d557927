import useSession from "@/business/hooks/useSession";
import { saveManualLabel } from "@/network/api/buyer";
import { Button, Tag } from "antd";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import EditOutlined from "~icons/ant-design/edit-outlined";
interface CustomLabelItem {
  groupId: number;
  groupName: string;
  labels: {
    groupId: number;
    value: number;
    labelName: string;
    labelId: string;
  }[];
}

const TagsCard = ({
  labelList,
  setLabelList,
  userBindLabelId,
  mutate,
}: {
  labelList: CustomLabelItem[];
  setLabelList: React.Dispatch<React.SetStateAction<CustomLabelItem[]>>;
  userBindLabelId: string[];
  mutate?: () => void;
}) => {
  const { buyer } = useSession();

  const [editing, setEditing] = useState(false);

  const handleEditSubmit = async () => {
    const currentSelectIds = labelList?.reduce((pr: string[], cur) => {
      const ids: string[] = [];
      cur.labels.map((item) => {
        if (item.value === 1) {
          ids.push(item.labelId);
        }
      });
      return pr.concat(ids);
    }, []);
    //新增激活状态的标签
    const addLabelIds: string[] = [];
    //取消激活状态的标签
    const delLabelIds: string[] = [];
    currentSelectIds.map((newId) => {
      if (!userBindLabelId.includes(newId)) {
        addLabelIds.push(newId);
      }
    });
    userBindLabelId.map((oldId) => {
      if (!currentSelectIds.includes(oldId)) {
        delLabelIds.push(oldId);
      }
    });

    //如果没有变化
    // if (!addLabelIds.length && !delLabelIds.length) {
    //   setIsEdit(false);
    //   return
    // }

    const postData = {
      addLabelIds,
      delLabelIds,
      buyerNick: buyer?.buyerNick as string,
      labelIds: currentSelectIds,
    };
    const res = await saveManualLabel(postData).promise.catch((error) => error);
    if (res instanceof Error) return;
    if (res.data.success) {
      toast.success("操作成功");
      mutate?.();
    }
    setEditing(false);
  };

  //标签变化
  const handleLabelChange = (labelId: string) => {
    if (!editing) return;
    const updateList = [...labelList];
    updateList.map((item, index) => {
      item.labels.map((tagItem, itemIndex) => {
        if (tagItem.labelId === labelId) {
          const currentItem = updateList[index].labels[itemIndex];
          currentItem.value = ~~(currentItem.value === 0);
        }
      });
    });
    setLabelList(updateList);
  };

  useEffect(() => {
    setEditing(false);
    mutate?.();
  }, [mutate]);

  return (
    <div>
      <div className="flex justify-end">
        {editing ? (
          <Button
            size="small"
            type="primary"
            onClick={() => handleEditSubmit()}
          >
            完成
          </Button>
        ) : (
          <Button type="link" onClick={() => setEditing(true)}>
            <EditOutlined /> 修改标签
          </Button>
        )}
      </div>
      <div
        className={`flex flex-col ${!editing ? "max-h-[300px] overflow-auto" : ""}`}
      >
        {labelList.map((item) => (
          <div className="mb-[5px]" key={item.groupId}>
            <div className="title text-zinc-400 mb-[5px] text-[12px] ">
              {item.groupName}
            </div>
            <div className="tabs">
              {item.labels.map((tagItem) => (
                <Tag
                  key={tagItem.labelId}
                  onClick={() => {
                    handleLabelChange(tagItem.labelId);
                  }}
                  color={tagItem.value === 1 ? "processing" : "default"}
                  className={`mb-[5px] cursor-pointer ${tagItem.value === 0 ? "text-slate-400" : ""}`}
                >
                  {tagItem.labelName}
                </Tag>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
export default TagsCard;
